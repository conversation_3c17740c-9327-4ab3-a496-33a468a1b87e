import { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
import Certifications from '../components/Certifications';
import { productData } from '../data/productData';

interface HomeProps {
  language: 'vi' | 'en';
}

interface Slide {
  image: string;
  alt: string;
}

interface ProductCategory {
  name: {
    vi: string;
    en: string;
  };
  image: string;
  path: string;
}

// Chúng ta sẽ sử dụng kiểu any để tránh các vấn đề về kiểu dữ liệu

interface CategoryImage {
  image: string;
  name: string;
}

const Home = ({ language }: HomeProps) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [currentCategorySlides, setCurrentCategorySlides] = useState([0, 0, 0, 0]);

  // Sử dụng useMemo để tránh tạo lại mảng slides mỗi khi render
  const slides: Slide[] = useMemo(() => [
    {
      image: '/images/slides/assortment-fresh-fruits-table_434193-4560.avif',
      alt: 'Fresh fruits display'
    },
    {
      image: '/images/slides/tropical_fruit_1917837551.jpg',
      alt: 'Fruit processing'
    },
    {
      image: '/images/slides/tropical_fruits.webp',
      alt: 'Fruit export'
    },
    {
      image: '/images/slides/fruits-in-vietnam-1.jpg',
      alt: 'Fruit export'
    }
  ], []);

  // Sử dụng useMemo cho productCategories
  const productCategories: ProductCategory[] = useMemo(() => [
    {
      name: {
        vi: 'Trái cây tươi',
        en: 'Fresh Fruits'
      },
      image:
        '/images/FRESH FRUIT/avocado/1.jpg',
      path: '/products'
    },
    {
      name: {
        vi: 'Trái cây đông lạnh',
        en: 'Frozen Fruits'
      },
      image: '/images/FROZEN FRUIT/f_avocado/avofr1.jpg',
      path: '/products'
    },
    {
      name: {
        vi: 'Trái cây sấy khô',
        en: 'Dried Fruits'
      },
      image: '/images/DRIED FRUIT/d_banana/badr1.jpg',
      path: '/products'
    },
    {
      name: {
        vi: 'Trái cây Đông khô',
        en: 'Freeze Dried Fruits'
      },
      image: '/images/FREEZE DRIED FRUIT/fd_avocado/avofd1.jpg',
      path: '/products'
    }
  ], []);

  // Lọc ra các sản phẩm theo danh mục và sử dụng useMemo
  const categoryProducts = useMemo(() => [
    // Trái cây tươi
    productData.filter(product => product.category.id === 'fresh-fruit'),
    // Trái cây đông lạnh
    productData.filter(product => product.category.id === 'frozen-fruit'),
    // Trái cây sấy khô
    productData.filter(product => product.category.id === 'dried-fruit'),
    // Trái cây đông khô
    productData.filter(product => product.category.id === 'freeze-dried-fruit')
  ], []);

  // Tạo dữ liệu ảnh cho mỗi danh mục và sử dụng useMemo
  const categoryImages: CategoryImage[][] = useMemo(() => productCategories.map((category: ProductCategory, index: number) => {
    const products = categoryProducts[index];

    // Tạo ít nhất 4 ảnh cho mỗi danh mục để đảm bảo slideshow hoạt động
    // Nếu không có đủ sản phẩm, sử dụng ảnh danh mục
    if (!products || products.length < 2) {
      return [
        { image: category.image, name: category.name[language] },
        { image: category.image, name: category.name[language] },
        { image: category.image, name: category.name[language] },
        { image: category.image, name: category.name[language] }
      ];
    }

    // Nếu có đủ sản phẩm, lấy ảnh đầu tiên của mỗi sản phẩm
    return products.map((product: any) => ({
      image: product.images[0],
      name: product.name[language as keyof typeof product.name]
    }));
  }), [productCategories, categoryProducts, language]);

  const translations = {
    hero: {
      title: {
        vi: 'NHÀ SẢN XUẤT VÀ XUẤT KHẨU TRÁI CÂY ĐÔNG LẠNH VÀ SẤY KHÔ',
        en: 'MANUFACTURER AND EXPORTER OF PRODUCTS FROM FRUITS AND NUTS'
      },
      subtitle: {
        vi: 'Chúng tôi cung cấp trái cây tươi chất lượng cao, uy tín và giá cạnh tranh.',
        en: 'We provide high-quality fresh fruits from around the world'
      },
      button: {
        vi: 'Khám phá ngay',
        en: 'Explore now'
      }
    },
    productSection: {
      title: {
        vi: 'DANH MỤC SẢN PHẨM',
        en: 'PRODUCT CATEGORIES'
      },
      viewMore: {
        vi: 'Xem chi tiết',
        en: 'View more'
      }
    },
    aboutSection: {
      title: {
        vi: 'VỀ CHÚNG TÔI',
        en: 'ABOUT US'
      },
      content: {
        vi: 'Việt Nam nổi tiếng trên thế giới về trái cây nhiệt đới, với hàng ngàn chủng loại trái cây và rau củ bổ dưỡng như : Mít, thanh long, khóm, xoài, chuối, khoai môn,..Với mục tiêu được xác định ngay từ những ngày đầu thành lập, ANBINHFOODS luôn hướng tới mục tiêu góp phần đưa nông sản Việt Nam ra thế giới, để mọi người trên thế giới có thể dễ dàng thưởng thức được hương vị và chất lượng cao nhất của những trái cây vùng nhiệt đới.',
        en: 'Vietnam is renowned worldwide for its tropical fruits, with thousands of varieties of nutritious fruits and vegetables such as jackfruit, dragon fruit, pineapple, mango, banana, taro, and more. With a mission established from its founding days, ANBINHFOODS has always aimed to contribute to bringing Vietnamese agricultural products to the world, so that people globally can easily enjoy the finest flavors and highest quality of these tropical fruits.'
      },
      button: {
        vi: 'Tìm hiểu thêm',
        en: 'Learn more'
      }
    }
  };

  useEffect(() => {
    // Interval cho hero slider
    const heroInterval = setInterval(() => {
      setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
    }, 5000);

    // Tạo mảng để lưu các interval
    const intervals: number[] = [];

    // Tạo interval riêng cho từng danh mục
    for (let i = 0; i < categoryImages.length; i++) {
      const interval = setInterval(() => {
        setCurrentCategorySlides(prev => {
          const newSlides = [...prev];
          newSlides[i] = (newSlides[i] + 1) % categoryImages[i].length;
          return newSlides;
        });
      }, 3000); // Đặt thời gian giống nhau cho tất cả các danh mục

      intervals.push(interval);
    }

    // Cleanup function
    return () => {
      clearInterval(heroInterval);
      intervals.forEach(interval => clearInterval(interval));
    };
  }, [slides, categoryImages]);

  return (
    <div>
      {/* Hero Section with Slider */}
      <section className="relative h-[600px] overflow-hidden">
        {slides.map((slide: Slide, index: number) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <div className="absolute inset-0 bg-black bg-opacity-20"></div>
            <img
              src={slide.image}
              alt={slide.alt}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 flex flex-col items-center justify-center text-white text-center px-4">
              <h1 className="text-3xl md:text-5xl font-bold mb-4 max-w-4xl">
                {translations.hero.title[language]}
              </h1>
              <p className="text-xl md:text-2xl mb-8 max-w-2xl">
                {translations.hero.subtitle[language]}
              </p>
              <Link
                to="/products"
                className="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-md transition duration-300"
              >
                {translations.hero.button[language]}
              </Link>
            </div>
          </div>
        ))}

        {/* Slider indicators */}
        <div className="absolute bottom-5 left-0 right-0 flex justify-center space-x-2">
          {slides.map((_: Slide, index: number) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full ${
                index === currentSlide ? 'bg-red-600' : 'bg-white bg-opacity-50'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            ></button>
          ))}
        </div>
      </section>

      {/* Product Categories Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-red-600">
            {translations.productSection.title[language]}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {productCategories.map((category: ProductCategory, index: number) => (
              <div key={index} className="bg-white rounded-[20px] shadow-md overflow-hidden group border border-gray-200 hover:shadow-xl hover:-translate-y-2 transition-all duration-300">
                <div className="h-80 overflow-hidden relative rounded-t-[20px]">
                  {/* Slideshow cho tất cả các danh mục sản phẩm */}
                  <>
                    {categoryImages[index].map((product: CategoryImage, productIndex: number) => (
                        <div
                          key={productIndex}
                          className={`absolute inset-0 transition-all duration-1000 ${
                            productIndex === currentCategorySlides[index]
                              ? 'opacity-100 transform scale-100'
                              : 'opacity-0 transform scale-105'
                          }`}
                        >
                          <div className="relative w-full h-full">
                            <img
                              src={product.image}
                              alt={product.name}
                              className="w-full h-full object-cover transition-transform duration-1000"
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-20 flex items-end justify-center">
                              <div className="bg-black bg-opacity-50 text-white px-4 py-2 mb-8 rounded-md text-center">
                                <span className="font-semibold">{product.name}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* Slideshow indicators */}
                      <div className="absolute bottom-3 left-0 right-0 flex justify-center space-x-1.5 z-10">
                        {categoryImages[index].map((_: CategoryImage, productIndex: number) => (
                          <button
                            key={productIndex}
                            onClick={() => {
                              const newSlides = [...currentCategorySlides];
                              newSlides[index] = productIndex;
                              setCurrentCategorySlides(newSlides);
                            }}
                            className={`w-2 h-2 rounded-full transition-all duration-300 ${
                              productIndex === currentCategorySlides[index]
                                ? 'bg-red-600 w-3'
                                : 'bg-white bg-opacity-70'
                            }`}
                            aria-label={`Go to product slide ${productIndex + 1}`}
                          ></button>
                        ))}
                      </div>
                    </>
                </div>
                <div className="p-8 relative z-10">
                  <h3 className="text-xl font-semibold mb-4">{category.name[language]}</h3>
                  <Link
                    to={category.path}
                    className="text-red-600 hover:text-red-700 font-medium inline-flex items-center"
                  >
                    {translations.productSection.viewMore[language]}
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 ml-1"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-4 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-3/5 mb-8 md:mb-0 md:pr-8">
              <div className="grid grid-cols-2 gap-4">
                <div className="overflow-hidden rounded-lg shadow-lg">
                  <img
                    src="/images/about/anh10.jfif"
                    alt="About FreshFruits 1"
                    className="w-full h-[250px] object-cover hover:scale-110 transition-transform duration-500"
                  />
                </div>
                <div className="overflow-hidden rounded-lg shadow-lg">
                  <img
                    src="/images/about/anh9.jfif"
                    alt="About FreshFruits 2"
                    className="w-full h-[250px] object-cover hover:scale-110 transition-transform duration-500"
                  />
                </div>
                <div className="overflow-hidden rounded-lg shadow-lg">
                  <img
                    src="/images/about/anh7.jpg"
                    alt="About FreshFruits 3"
                    className="w-full h-[250px] object-cover hover:scale-110 transition-transform duration-500"
                  />
                </div>
                <div className="overflow-hidden rounded-lg shadow-lg">
                  <img
                    src="/images/about/anh8.jpg"
                    alt="About FreshFruits 4"
                    className="w-full h-[250px] object-cover hover:scale-110 transition-transform duration-500"
                  />
                </div>
              </div>
            </div>
            <div className="md:w-2/5">
              <h2 className="text-3xl font-bold mb-6 text-red-600 text-center">
                {translations.aboutSection.title[language]}
              </h2>
              <p className="text-gray-700 mb-6 text-lg leading-relaxed text-justify">
                {translations.aboutSection.content[language]}
              </p>
              <div className="flex justify-center">
                <Link
                  to="/about/passion"
                  className="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-md transition duration-300 inline-block"
                >
                  {translations.aboutSection.button[language]}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Certifications Section */}
      <Certifications language={language} />
    </div>
  );
};

export default Home;
