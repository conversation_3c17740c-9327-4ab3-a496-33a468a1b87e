(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))i(r);new MutationObserver(r=>{for(const s of r)if(s.type==="childList")for(const a of s.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&i(a)}).observe(document,{childList:!0,subtree:!0});function n(r){const s={};return r.integrity&&(s.integrity=r.integrity),r.referrerPolicy&&(s.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?s.credentials="include":r.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function i(r){if(r.ep)return;r.ep=!0;const s=n(r);fetch(r.href,s)}})();function Sf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ku={exports:{}},Kr={},bu={exports:{}},z={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ci=Symbol.for("react.element"),Ef=Symbol.for("react.portal"),Cf=Symbol.for("react.fragment"),Nf=Symbol.for("react.strict_mode"),Tf=Symbol.for("react.profiler"),jf=Symbol.for("react.provider"),Ff=Symbol.for("react.context"),Rf=Symbol.for("react.forward_ref"),If=Symbol.for("react.suspense"),Pf=Symbol.for("react.memo"),Df=Symbol.for("react.lazy"),Jo=Symbol.iterator;function Lf(e){return e===null||typeof e!="object"?null:(e=Jo&&e[Jo]||e["@@iterator"],typeof e=="function"?e:null)}var Su={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Eu=Object.assign,Cu={};function Pn(e,t,n){this.props=e,this.context=t,this.refs=Cu,this.updater=n||Su}Pn.prototype.isReactComponent={};Pn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Pn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Nu(){}Nu.prototype=Pn.prototype;function Ka(e,t,n){this.props=e,this.context=t,this.refs=Cu,this.updater=n||Su}var qa=Ka.prototype=new Nu;qa.constructor=Ka;Eu(qa,Pn.prototype);qa.isPureReactComponent=!0;var el=Array.isArray,Tu=Object.prototype.hasOwnProperty,Za={current:null},ju={key:!0,ref:!0,__self:!0,__source:!0};function Fu(e,t,n){var i,r={},s=null,a=null;if(t!=null)for(i in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(s=""+t.key),t)Tu.call(t,i)&&!ju.hasOwnProperty(i)&&(r[i]=t[i]);var o=arguments.length-2;if(o===1)r.children=n;else if(1<o){for(var l=Array(o),c=0;c<o;c++)l[c]=arguments[c+2];r.children=l}if(e&&e.defaultProps)for(i in o=e.defaultProps,o)r[i]===void 0&&(r[i]=o[i]);return{$$typeof:Ci,type:e,key:s,ref:a,props:r,_owner:Za.current}}function Mf(e,t){return{$$typeof:Ci,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Qa(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ci}function Of(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var tl=/\/+/g;function hs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Of(""+e.key):t.toString(36)}function nr(e,t,n,i,r){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(s){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case Ci:case Ef:a=!0}}if(a)return a=e,r=r(a),e=i===""?"."+hs(a,0):i,el(r)?(n="",e!=null&&(n=e.replace(tl,"$&/")+"/"),nr(r,t,n,"",function(c){return c})):r!=null&&(Qa(r)&&(r=Mf(r,n+(!r.key||a&&a.key===r.key?"":(""+r.key).replace(tl,"$&/")+"/")+e)),t.push(r)),1;if(a=0,i=i===""?".":i+":",el(e))for(var o=0;o<e.length;o++){s=e[o];var l=i+hs(s,o);a+=nr(s,t,n,l,r)}else if(l=Lf(e),typeof l=="function")for(e=l.call(e),o=0;!(s=e.next()).done;)s=s.value,l=i+hs(s,o++),a+=nr(s,t,n,l,r);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function Oi(e,t,n){if(e==null)return e;var i=[],r=0;return nr(e,i,"","",function(s){return t.call(n,s,r++)}),i}function Af(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var we={current:null},ir={transition:null},zf={ReactCurrentDispatcher:we,ReactCurrentBatchConfig:ir,ReactCurrentOwner:Za};function Ru(){throw Error("act(...) is not supported in production builds of React.")}z.Children={map:Oi,forEach:function(e,t,n){Oi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Oi(e,function(){t++}),t},toArray:function(e){return Oi(e,function(t){return t})||[]},only:function(e){if(!Qa(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};z.Component=Pn;z.Fragment=Cf;z.Profiler=Tf;z.PureComponent=Ka;z.StrictMode=Nf;z.Suspense=If;z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=zf;z.act=Ru;z.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var i=Eu({},e.props),r=e.key,s=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,a=Za.current),t.key!==void 0&&(r=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(l in t)Tu.call(t,l)&&!ju.hasOwnProperty(l)&&(i[l]=t[l]===void 0&&o!==void 0?o[l]:t[l])}var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){o=Array(l);for(var c=0;c<l;c++)o[c]=arguments[c+2];i.children=o}return{$$typeof:Ci,type:e.type,key:r,ref:s,props:i,_owner:a}};z.createContext=function(e){return e={$$typeof:Ff,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:jf,_context:e},e.Consumer=e};z.createElement=Fu;z.createFactory=function(e){var t=Fu.bind(null,e);return t.type=e,t};z.createRef=function(){return{current:null}};z.forwardRef=function(e){return{$$typeof:Rf,render:e}};z.isValidElement=Qa;z.lazy=function(e){return{$$typeof:Df,_payload:{_status:-1,_result:e},_init:Af}};z.memo=function(e,t){return{$$typeof:Pf,type:e,compare:t===void 0?null:t}};z.startTransition=function(e){var t=ir.transition;ir.transition={};try{e()}finally{ir.transition=t}};z.unstable_act=Ru;z.useCallback=function(e,t){return we.current.useCallback(e,t)};z.useContext=function(e){return we.current.useContext(e)};z.useDebugValue=function(){};z.useDeferredValue=function(e){return we.current.useDeferredValue(e)};z.useEffect=function(e,t){return we.current.useEffect(e,t)};z.useId=function(){return we.current.useId()};z.useImperativeHandle=function(e,t,n){return we.current.useImperativeHandle(e,t,n)};z.useInsertionEffect=function(e,t){return we.current.useInsertionEffect(e,t)};z.useLayoutEffect=function(e,t){return we.current.useLayoutEffect(e,t)};z.useMemo=function(e,t){return we.current.useMemo(e,t)};z.useReducer=function(e,t,n){return we.current.useReducer(e,t,n)};z.useRef=function(e){return we.current.useRef(e)};z.useState=function(e){return we.current.useState(e)};z.useSyncExternalStore=function(e,t,n){return we.current.useSyncExternalStore(e,t,n)};z.useTransition=function(){return we.current.useTransition()};z.version="18.3.1";bu.exports=z;var k=bu.exports;const ie=Sf(k);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _f=k,Hf=Symbol.for("react.element"),Uf=Symbol.for("react.fragment"),Bf=Object.prototype.hasOwnProperty,Vf=_f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Gf={key:!0,ref:!0,__self:!0,__source:!0};function Iu(e,t,n){var i,r={},s=null,a=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(a=t.ref);for(i in t)Bf.call(t,i)&&!Gf.hasOwnProperty(i)&&(r[i]=t[i]);if(e&&e.defaultProps)for(i in t=e.defaultProps,t)r[i]===void 0&&(r[i]=t[i]);return{$$typeof:Hf,type:e,key:s,ref:a,props:r,_owner:Vf.current}}Kr.Fragment=Uf;Kr.jsx=Iu;Kr.jsxs=Iu;ku.exports=Kr;var u=ku.exports,Pu={exports:{}},De={},Du={exports:{}},Lu={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(I,P){var M=I.length;I.push(P);e:for(;0<M;){var Y=M-1>>>1,te=I[Y];if(0<r(te,P))I[Y]=P,I[M]=te,M=Y;else break e}}function n(I){return I.length===0?null:I[0]}function i(I){if(I.length===0)return null;var P=I[0],M=I.pop();if(M!==P){I[0]=M;e:for(var Y=0,te=I.length,Li=te>>>1;Y<Li;){var _t=2*(Y+1)-1,fs=I[_t],Ht=_t+1,Mi=I[Ht];if(0>r(fs,M))Ht<te&&0>r(Mi,fs)?(I[Y]=Mi,I[Ht]=M,Y=Ht):(I[Y]=fs,I[_t]=M,Y=_t);else if(Ht<te&&0>r(Mi,M))I[Y]=Mi,I[Ht]=M,Y=Ht;else break e}}return P}function r(I,P){var M=I.sortIndex-P.sortIndex;return M!==0?M:I.id-P.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var a=Date,o=a.now();e.unstable_now=function(){return a.now()-o}}var l=[],c=[],d=1,h=null,g=3,y=!1,v=!1,x=!1,S=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,f=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(I){for(var P=n(c);P!==null;){if(P.callback===null)i(c);else if(P.startTime<=I)i(c),P.sortIndex=P.expirationTime,t(l,P);else break;P=n(c)}}function w(I){if(x=!1,m(I),!v)if(n(l)!==null)v=!0,A(b);else{var P=n(c);P!==null&&X(w,P.startTime-I)}}function b(I,P){v=!1,x&&(x=!1,p(T),T=-1),y=!0;var M=g;try{for(m(P),h=n(l);h!==null&&(!(h.expirationTime>P)||I&&!R());){var Y=h.callback;if(typeof Y=="function"){h.callback=null,g=h.priorityLevel;var te=Y(h.expirationTime<=P);P=e.unstable_now(),typeof te=="function"?h.callback=te:h===n(l)&&i(l),m(P)}else i(l);h=n(l)}if(h!==null)var Li=!0;else{var _t=n(c);_t!==null&&X(w,_t.startTime-P),Li=!1}return Li}finally{h=null,g=M,y=!1}}var E=!1,N=null,T=-1,j=5,C=-1;function R(){return!(e.unstable_now()-C<j)}function L(){if(N!==null){var I=e.unstable_now();C=I;var P=!0;try{P=N(!0,I)}finally{P?O():(E=!1,N=null)}}else E=!1}var O;if(typeof f=="function")O=function(){f(L)};else if(typeof MessageChannel<"u"){var H=new MessageChannel,U=H.port2;H.port1.onmessage=L,O=function(){U.postMessage(null)}}else O=function(){S(L,0)};function A(I){N=I,E||(E=!0,O())}function X(I,P){T=S(function(){I(e.unstable_now())},P)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(I){I.callback=null},e.unstable_continueExecution=function(){v||y||(v=!0,A(b))},e.unstable_forceFrameRate=function(I){0>I||125<I?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<I?Math.floor(1e3/I):5},e.unstable_getCurrentPriorityLevel=function(){return g},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(I){switch(g){case 1:case 2:case 3:var P=3;break;default:P=g}var M=g;g=P;try{return I()}finally{g=M}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(I,P){switch(I){case 1:case 2:case 3:case 4:case 5:break;default:I=3}var M=g;g=I;try{return P()}finally{g=M}},e.unstable_scheduleCallback=function(I,P,M){var Y=e.unstable_now();switch(typeof M=="object"&&M!==null?(M=M.delay,M=typeof M=="number"&&0<M?Y+M:Y):M=Y,I){case 1:var te=-1;break;case 2:te=250;break;case 5:te=**********;break;case 4:te=1e4;break;default:te=5e3}return te=M+te,I={id:d++,callback:P,priorityLevel:I,startTime:M,expirationTime:te,sortIndex:-1},M>Y?(I.sortIndex=M,t(c,I),n(l)===null&&I===n(c)&&(x?(p(T),T=-1):x=!0,X(w,M-Y))):(I.sortIndex=te,t(l,I),v||y||(v=!0,A(b))),I},e.unstable_shouldYield=R,e.unstable_wrapCallback=function(I){var P=g;return function(){var M=g;g=P;try{return I.apply(this,arguments)}finally{g=M}}}})(Lu);Du.exports=Lu;var $f=Du.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wf=k,Pe=$f;function F(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Mu=new Set,oi={};function en(e,t){Cn(e,t),Cn(e+"Capture",t)}function Cn(e,t){for(oi[e]=t,e=0;e<t.length;e++)Mu.add(t[e])}var ct=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Zs=Object.prototype.hasOwnProperty,Yf=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,nl={},il={};function Kf(e){return Zs.call(il,e)?!0:Zs.call(nl,e)?!1:Yf.test(e)?il[e]=!0:(nl[e]=!0,!1)}function qf(e,t,n,i){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return i?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Zf(e,t,n,i){if(t===null||typeof t>"u"||qf(e,t,n,i))return!0;if(i)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ke(e,t,n,i,r,s,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=i,this.attributeNamespace=r,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=a}var fe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){fe[e]=new ke(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];fe[t]=new ke(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){fe[e]=new ke(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){fe[e]=new ke(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){fe[e]=new ke(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){fe[e]=new ke(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){fe[e]=new ke(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){fe[e]=new ke(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){fe[e]=new ke(e,5,!1,e.toLowerCase(),null,!1,!1)});var Xa=/[\-:]([a-z])/g;function Ja(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Xa,Ja);fe[t]=new ke(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Xa,Ja);fe[t]=new ke(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Xa,Ja);fe[t]=new ke(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){fe[e]=new ke(e,1,!1,e.toLowerCase(),null,!1,!1)});fe.xlinkHref=new ke("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){fe[e]=new ke(e,1,!1,e.toLowerCase(),null,!0,!0)});function eo(e,t,n,i){var r=fe.hasOwnProperty(t)?fe[t]:null;(r!==null?r.type!==0:i||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Zf(t,n,r,i)&&(n=null),i||r===null?Kf(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):r.mustUseProperty?e[r.propertyName]=n===null?r.type===3?!1:"":n:(t=r.attributeName,i=r.attributeNamespace,n===null?e.removeAttribute(t):(r=r.type,n=r===3||r===4&&n===!0?"":""+n,i?e.setAttributeNS(i,t,n):e.setAttribute(t,n))))}var mt=Wf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ai=Symbol.for("react.element"),sn=Symbol.for("react.portal"),an=Symbol.for("react.fragment"),to=Symbol.for("react.strict_mode"),Qs=Symbol.for("react.profiler"),Ou=Symbol.for("react.provider"),Au=Symbol.for("react.context"),no=Symbol.for("react.forward_ref"),Xs=Symbol.for("react.suspense"),Js=Symbol.for("react.suspense_list"),io=Symbol.for("react.memo"),yt=Symbol.for("react.lazy"),zu=Symbol.for("react.offscreen"),rl=Symbol.iterator;function An(e){return e===null||typeof e!="object"?null:(e=rl&&e[rl]||e["@@iterator"],typeof e=="function"?e:null)}var Q=Object.assign,ps;function Yn(e){if(ps===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);ps=t&&t[1]||""}return`
`+ps+e}var ms=!1;function gs(e,t){if(!e||ms)return"";ms=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var i=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){i=c}e.call(t.prototype)}else{try{throw Error()}catch(c){i=c}e()}}catch(c){if(c&&i&&typeof c.stack=="string"){for(var r=c.stack.split(`
`),s=i.stack.split(`
`),a=r.length-1,o=s.length-1;1<=a&&0<=o&&r[a]!==s[o];)o--;for(;1<=a&&0<=o;a--,o--)if(r[a]!==s[o]){if(a!==1||o!==1)do if(a--,o--,0>o||r[a]!==s[o]){var l=`
`+r[a].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=a&&0<=o);break}}}finally{ms=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Yn(e):""}function Qf(e){switch(e.tag){case 5:return Yn(e.type);case 16:return Yn("Lazy");case 13:return Yn("Suspense");case 19:return Yn("SuspenseList");case 0:case 2:case 15:return e=gs(e.type,!1),e;case 11:return e=gs(e.type.render,!1),e;case 1:return e=gs(e.type,!0),e;default:return""}}function ea(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case an:return"Fragment";case sn:return"Portal";case Qs:return"Profiler";case to:return"StrictMode";case Xs:return"Suspense";case Js:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Au:return(e.displayName||"Context")+".Consumer";case Ou:return(e._context.displayName||"Context")+".Provider";case no:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case io:return t=e.displayName||null,t!==null?t:ea(e.type)||"Memo";case yt:t=e._payload,e=e._init;try{return ea(e(t))}catch{}}return null}function Xf(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ea(t);case 8:return t===to?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Dt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function _u(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Jf(e){var t=_u(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var r=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return r.call(this)},set:function(a){i=""+a,s.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(a){i=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function zi(e){e._valueTracker||(e._valueTracker=Jf(e))}function Hu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),i="";return e&&(i=_u(e)?e.checked?"true":"false":e.value),e=i,e!==n?(t.setValue(e),!0):!1}function vr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ta(e,t){var n=t.checked;return Q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function sl(e,t){var n=t.defaultValue==null?"":t.defaultValue,i=t.checked!=null?t.checked:t.defaultChecked;n=Dt(t.value!=null?t.value:n),e._wrapperState={initialChecked:i,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Uu(e,t){t=t.checked,t!=null&&eo(e,"checked",t,!1)}function na(e,t){Uu(e,t);var n=Dt(t.value),i=t.type;if(n!=null)i==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(i==="submit"||i==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ia(e,t.type,n):t.hasOwnProperty("defaultValue")&&ia(e,t.type,Dt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function al(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var i=t.type;if(!(i!=="submit"&&i!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ia(e,t,n){(t!=="number"||vr(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Kn=Array.isArray;function yn(e,t,n,i){if(e=e.options,t){t={};for(var r=0;r<n.length;r++)t["$"+n[r]]=!0;for(n=0;n<e.length;n++)r=t.hasOwnProperty("$"+e[n].value),e[n].selected!==r&&(e[n].selected=r),r&&i&&(e[n].defaultSelected=!0)}else{for(n=""+Dt(n),t=null,r=0;r<e.length;r++){if(e[r].value===n){e[r].selected=!0,i&&(e[r].defaultSelected=!0);return}t!==null||e[r].disabled||(t=e[r])}t!==null&&(t.selected=!0)}}function ra(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(F(91));return Q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ol(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(F(92));if(Kn(n)){if(1<n.length)throw Error(F(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Dt(n)}}function Bu(e,t){var n=Dt(t.value),i=Dt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),i!=null&&(e.defaultValue=""+i)}function ll(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Vu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function sa(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Vu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var _i,Gu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,i,r){MSApp.execUnsafeLocalFunction(function(){return e(t,n,i,r)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(_i=_i||document.createElement("div"),_i.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=_i.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function li(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Qn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},eh=["Webkit","ms","Moz","O"];Object.keys(Qn).forEach(function(e){eh.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Qn[t]=Qn[e]})});function $u(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Qn.hasOwnProperty(e)&&Qn[e]?(""+t).trim():t+"px"}function Wu(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var i=n.indexOf("--")===0,r=$u(n,t[n],i);n==="float"&&(n="cssFloat"),i?e.setProperty(n,r):e[n]=r}}var th=Q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function aa(e,t){if(t){if(th[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(F(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(F(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(F(61))}if(t.style!=null&&typeof t.style!="object")throw Error(F(62))}}function oa(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var la=null;function ro(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ua=null,xn=null,wn=null;function ul(e){if(e=ji(e)){if(typeof ua!="function")throw Error(F(280));var t=e.stateNode;t&&(t=Jr(t),ua(e.stateNode,e.type,t))}}function Yu(e){xn?wn?wn.push(e):wn=[e]:xn=e}function Ku(){if(xn){var e=xn,t=wn;if(wn=xn=null,ul(e),t)for(e=0;e<t.length;e++)ul(t[e])}}function qu(e,t){return e(t)}function Zu(){}var vs=!1;function Qu(e,t,n){if(vs)return e(t,n);vs=!0;try{return qu(e,t,n)}finally{vs=!1,(xn!==null||wn!==null)&&(Zu(),Ku())}}function ui(e,t){var n=e.stateNode;if(n===null)return null;var i=Jr(n);if(i===null)return null;n=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(F(231,t,typeof n));return n}var ca=!1;if(ct)try{var zn={};Object.defineProperty(zn,"passive",{get:function(){ca=!0}}),window.addEventListener("test",zn,zn),window.removeEventListener("test",zn,zn)}catch{ca=!1}function nh(e,t,n,i,r,s,a,o,l){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(d){this.onError(d)}}var Xn=!1,yr=null,xr=!1,da=null,ih={onError:function(e){Xn=!0,yr=e}};function rh(e,t,n,i,r,s,a,o,l){Xn=!1,yr=null,nh.apply(ih,arguments)}function sh(e,t,n,i,r,s,a,o,l){if(rh.apply(this,arguments),Xn){if(Xn){var c=yr;Xn=!1,yr=null}else throw Error(F(198));xr||(xr=!0,da=c)}}function tn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Xu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function cl(e){if(tn(e)!==e)throw Error(F(188))}function ah(e){var t=e.alternate;if(!t){if(t=tn(e),t===null)throw Error(F(188));return t!==e?null:e}for(var n=e,i=t;;){var r=n.return;if(r===null)break;var s=r.alternate;if(s===null){if(i=r.return,i!==null){n=i;continue}break}if(r.child===s.child){for(s=r.child;s;){if(s===n)return cl(r),e;if(s===i)return cl(r),t;s=s.sibling}throw Error(F(188))}if(n.return!==i.return)n=r,i=s;else{for(var a=!1,o=r.child;o;){if(o===n){a=!0,n=r,i=s;break}if(o===i){a=!0,i=r,n=s;break}o=o.sibling}if(!a){for(o=s.child;o;){if(o===n){a=!0,n=s,i=r;break}if(o===i){a=!0,i=s,n=r;break}o=o.sibling}if(!a)throw Error(F(189))}}if(n.alternate!==i)throw Error(F(190))}if(n.tag!==3)throw Error(F(188));return n.stateNode.current===n?e:t}function Ju(e){return e=ah(e),e!==null?ec(e):null}function ec(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=ec(e);if(t!==null)return t;e=e.sibling}return null}var tc=Pe.unstable_scheduleCallback,dl=Pe.unstable_cancelCallback,oh=Pe.unstable_shouldYield,lh=Pe.unstable_requestPaint,ee=Pe.unstable_now,uh=Pe.unstable_getCurrentPriorityLevel,so=Pe.unstable_ImmediatePriority,nc=Pe.unstable_UserBlockingPriority,wr=Pe.unstable_NormalPriority,ch=Pe.unstable_LowPriority,ic=Pe.unstable_IdlePriority,qr=null,Xe=null;function dh(e){if(Xe&&typeof Xe.onCommitFiberRoot=="function")try{Xe.onCommitFiberRoot(qr,e,void 0,(e.current.flags&128)===128)}catch{}}var $e=Math.clz32?Math.clz32:ph,fh=Math.log,hh=Math.LN2;function ph(e){return e>>>=0,e===0?32:31-(fh(e)/hh|0)|0}var Hi=64,Ui=4194304;function qn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function kr(e,t){var n=e.pendingLanes;if(n===0)return 0;var i=0,r=e.suspendedLanes,s=e.pingedLanes,a=n&268435455;if(a!==0){var o=a&~r;o!==0?i=qn(o):(s&=a,s!==0&&(i=qn(s)))}else a=n&~r,a!==0?i=qn(a):s!==0&&(i=qn(s));if(i===0)return 0;if(t!==0&&t!==i&&!(t&r)&&(r=i&-i,s=t&-t,r>=s||r===16&&(s&4194240)!==0))return t;if(i&4&&(i|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=i;0<t;)n=31-$e(t),r=1<<n,i|=e[n],t&=~r;return i}function mh(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function gh(e,t){for(var n=e.suspendedLanes,i=e.pingedLanes,r=e.expirationTimes,s=e.pendingLanes;0<s;){var a=31-$e(s),o=1<<a,l=r[a];l===-1?(!(o&n)||o&i)&&(r[a]=mh(o,t)):l<=t&&(e.expiredLanes|=o),s&=~o}}function fa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function rc(){var e=Hi;return Hi<<=1,!(Hi&4194240)&&(Hi=64),e}function ys(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ni(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-$e(t),e[t]=n}function vh(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var i=e.eventTimes;for(e=e.expirationTimes;0<n;){var r=31-$e(n),s=1<<r;t[r]=0,i[r]=-1,e[r]=-1,n&=~s}}function ao(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var i=31-$e(n),r=1<<i;r&t|e[i]&t&&(e[i]|=t),n&=~r}}var B=0;function sc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var ac,oo,oc,lc,uc,ha=!1,Bi=[],Ct=null,Nt=null,Tt=null,ci=new Map,di=new Map,wt=[],yh="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function fl(e,t){switch(e){case"focusin":case"focusout":Ct=null;break;case"dragenter":case"dragleave":Nt=null;break;case"mouseover":case"mouseout":Tt=null;break;case"pointerover":case"pointerout":ci.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":di.delete(t.pointerId)}}function _n(e,t,n,i,r,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:i,nativeEvent:s,targetContainers:[r]},t!==null&&(t=ji(t),t!==null&&oo(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,r!==null&&t.indexOf(r)===-1&&t.push(r),e)}function xh(e,t,n,i,r){switch(t){case"focusin":return Ct=_n(Ct,e,t,n,i,r),!0;case"dragenter":return Nt=_n(Nt,e,t,n,i,r),!0;case"mouseover":return Tt=_n(Tt,e,t,n,i,r),!0;case"pointerover":var s=r.pointerId;return ci.set(s,_n(ci.get(s)||null,e,t,n,i,r)),!0;case"gotpointercapture":return s=r.pointerId,di.set(s,_n(di.get(s)||null,e,t,n,i,r)),!0}return!1}function cc(e){var t=Vt(e.target);if(t!==null){var n=tn(t);if(n!==null){if(t=n.tag,t===13){if(t=Xu(n),t!==null){e.blockedOn=t,uc(e.priority,function(){oc(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function rr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=pa(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var i=new n.constructor(n.type,n);la=i,n.target.dispatchEvent(i),la=null}else return t=ji(n),t!==null&&oo(t),e.blockedOn=n,!1;t.shift()}return!0}function hl(e,t,n){rr(e)&&n.delete(t)}function wh(){ha=!1,Ct!==null&&rr(Ct)&&(Ct=null),Nt!==null&&rr(Nt)&&(Nt=null),Tt!==null&&rr(Tt)&&(Tt=null),ci.forEach(hl),di.forEach(hl)}function Hn(e,t){e.blockedOn===t&&(e.blockedOn=null,ha||(ha=!0,Pe.unstable_scheduleCallback(Pe.unstable_NormalPriority,wh)))}function fi(e){function t(r){return Hn(r,e)}if(0<Bi.length){Hn(Bi[0],e);for(var n=1;n<Bi.length;n++){var i=Bi[n];i.blockedOn===e&&(i.blockedOn=null)}}for(Ct!==null&&Hn(Ct,e),Nt!==null&&Hn(Nt,e),Tt!==null&&Hn(Tt,e),ci.forEach(t),di.forEach(t),n=0;n<wt.length;n++)i=wt[n],i.blockedOn===e&&(i.blockedOn=null);for(;0<wt.length&&(n=wt[0],n.blockedOn===null);)cc(n),n.blockedOn===null&&wt.shift()}var kn=mt.ReactCurrentBatchConfig,br=!0;function kh(e,t,n,i){var r=B,s=kn.transition;kn.transition=null;try{B=1,lo(e,t,n,i)}finally{B=r,kn.transition=s}}function bh(e,t,n,i){var r=B,s=kn.transition;kn.transition=null;try{B=4,lo(e,t,n,i)}finally{B=r,kn.transition=s}}function lo(e,t,n,i){if(br){var r=pa(e,t,n,i);if(r===null)js(e,t,i,Sr,n),fl(e,i);else if(xh(r,e,t,n,i))i.stopPropagation();else if(fl(e,i),t&4&&-1<yh.indexOf(e)){for(;r!==null;){var s=ji(r);if(s!==null&&ac(s),s=pa(e,t,n,i),s===null&&js(e,t,i,Sr,n),s===r)break;r=s}r!==null&&i.stopPropagation()}else js(e,t,i,null,n)}}var Sr=null;function pa(e,t,n,i){if(Sr=null,e=ro(i),e=Vt(e),e!==null)if(t=tn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Xu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Sr=e,null}function dc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(uh()){case so:return 1;case nc:return 4;case wr:case ch:return 16;case ic:return 536870912;default:return 16}default:return 16}}var bt=null,uo=null,sr=null;function fc(){if(sr)return sr;var e,t=uo,n=t.length,i,r="value"in bt?bt.value:bt.textContent,s=r.length;for(e=0;e<n&&t[e]===r[e];e++);var a=n-e;for(i=1;i<=a&&t[n-i]===r[s-i];i++);return sr=r.slice(e,1<i?1-i:void 0)}function ar(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Vi(){return!0}function pl(){return!1}function Le(e){function t(n,i,r,s,a){this._reactName=n,this._targetInst=r,this.type=i,this.nativeEvent=s,this.target=a,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(n=e[o],this[o]=n?n(s):s[o]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Vi:pl,this.isPropagationStopped=pl,this}return Q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Vi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Vi)},persist:function(){},isPersistent:Vi}),t}var Dn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},co=Le(Dn),Ti=Q({},Dn,{view:0,detail:0}),Sh=Le(Ti),xs,ws,Un,Zr=Q({},Ti,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:fo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Un&&(Un&&e.type==="mousemove"?(xs=e.screenX-Un.screenX,ws=e.screenY-Un.screenY):ws=xs=0,Un=e),xs)},movementY:function(e){return"movementY"in e?e.movementY:ws}}),ml=Le(Zr),Eh=Q({},Zr,{dataTransfer:0}),Ch=Le(Eh),Nh=Q({},Ti,{relatedTarget:0}),ks=Le(Nh),Th=Q({},Dn,{animationName:0,elapsedTime:0,pseudoElement:0}),jh=Le(Th),Fh=Q({},Dn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Rh=Le(Fh),Ih=Q({},Dn,{data:0}),gl=Le(Ih),Ph={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Dh={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Lh={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Mh(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Lh[e])?!!t[e]:!1}function fo(){return Mh}var Oh=Q({},Ti,{key:function(e){if(e.key){var t=Ph[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ar(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Dh[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:fo,charCode:function(e){return e.type==="keypress"?ar(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ar(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Ah=Le(Oh),zh=Q({},Zr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),vl=Le(zh),_h=Q({},Ti,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:fo}),Hh=Le(_h),Uh=Q({},Dn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Bh=Le(Uh),Vh=Q({},Zr,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Gh=Le(Vh),$h=[9,13,27,32],ho=ct&&"CompositionEvent"in window,Jn=null;ct&&"documentMode"in document&&(Jn=document.documentMode);var Wh=ct&&"TextEvent"in window&&!Jn,hc=ct&&(!ho||Jn&&8<Jn&&11>=Jn),yl=" ",xl=!1;function pc(e,t){switch(e){case"keyup":return $h.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function mc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var on=!1;function Yh(e,t){switch(e){case"compositionend":return mc(t);case"keypress":return t.which!==32?null:(xl=!0,yl);case"textInput":return e=t.data,e===yl&&xl?null:e;default:return null}}function Kh(e,t){if(on)return e==="compositionend"||!ho&&pc(e,t)?(e=fc(),sr=uo=bt=null,on=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return hc&&t.locale!=="ko"?null:t.data;default:return null}}var qh={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function wl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!qh[e.type]:t==="textarea"}function gc(e,t,n,i){Yu(i),t=Er(t,"onChange"),0<t.length&&(n=new co("onChange","change",null,n,i),e.push({event:n,listeners:t}))}var ei=null,hi=null;function Zh(e){Tc(e,0)}function Qr(e){var t=cn(e);if(Hu(t))return e}function Qh(e,t){if(e==="change")return t}var vc=!1;if(ct){var bs;if(ct){var Ss="oninput"in document;if(!Ss){var kl=document.createElement("div");kl.setAttribute("oninput","return;"),Ss=typeof kl.oninput=="function"}bs=Ss}else bs=!1;vc=bs&&(!document.documentMode||9<document.documentMode)}function bl(){ei&&(ei.detachEvent("onpropertychange",yc),hi=ei=null)}function yc(e){if(e.propertyName==="value"&&Qr(hi)){var t=[];gc(t,hi,e,ro(e)),Qu(Zh,t)}}function Xh(e,t,n){e==="focusin"?(bl(),ei=t,hi=n,ei.attachEvent("onpropertychange",yc)):e==="focusout"&&bl()}function Jh(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Qr(hi)}function ep(e,t){if(e==="click")return Qr(t)}function tp(e,t){if(e==="input"||e==="change")return Qr(t)}function np(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ye=typeof Object.is=="function"?Object.is:np;function pi(e,t){if(Ye(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var r=n[i];if(!Zs.call(t,r)||!Ye(e[r],t[r]))return!1}return!0}function Sl(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function El(e,t){var n=Sl(e);e=0;for(var i;n;){if(n.nodeType===3){if(i=e+n.textContent.length,e<=t&&i>=t)return{node:n,offset:t-e};e=i}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Sl(n)}}function xc(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?xc(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function wc(){for(var e=window,t=vr();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=vr(e.document)}return t}function po(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function ip(e){var t=wc(),n=e.focusedElem,i=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&xc(n.ownerDocument.documentElement,n)){if(i!==null&&po(n)){if(t=i.start,e=i.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var r=n.textContent.length,s=Math.min(i.start,r);i=i.end===void 0?s:Math.min(i.end,r),!e.extend&&s>i&&(r=i,i=s,s=r),r=El(n,s);var a=El(n,i);r&&a&&(e.rangeCount!==1||e.anchorNode!==r.node||e.anchorOffset!==r.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(r.node,r.offset),e.removeAllRanges(),s>i?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var rp=ct&&"documentMode"in document&&11>=document.documentMode,ln=null,ma=null,ti=null,ga=!1;function Cl(e,t,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ga||ln==null||ln!==vr(i)||(i=ln,"selectionStart"in i&&po(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),ti&&pi(ti,i)||(ti=i,i=Er(ma,"onSelect"),0<i.length&&(t=new co("onSelect","select",null,t,n),e.push({event:t,listeners:i}),t.target=ln)))}function Gi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var un={animationend:Gi("Animation","AnimationEnd"),animationiteration:Gi("Animation","AnimationIteration"),animationstart:Gi("Animation","AnimationStart"),transitionend:Gi("Transition","TransitionEnd")},Es={},kc={};ct&&(kc=document.createElement("div").style,"AnimationEvent"in window||(delete un.animationend.animation,delete un.animationiteration.animation,delete un.animationstart.animation),"TransitionEvent"in window||delete un.transitionend.transition);function Xr(e){if(Es[e])return Es[e];if(!un[e])return e;var t=un[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in kc)return Es[e]=t[n];return e}var bc=Xr("animationend"),Sc=Xr("animationiteration"),Ec=Xr("animationstart"),Cc=Xr("transitionend"),Nc=new Map,Nl="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Mt(e,t){Nc.set(e,t),en(t,[e])}for(var Cs=0;Cs<Nl.length;Cs++){var Ns=Nl[Cs],sp=Ns.toLowerCase(),ap=Ns[0].toUpperCase()+Ns.slice(1);Mt(sp,"on"+ap)}Mt(bc,"onAnimationEnd");Mt(Sc,"onAnimationIteration");Mt(Ec,"onAnimationStart");Mt("dblclick","onDoubleClick");Mt("focusin","onFocus");Mt("focusout","onBlur");Mt(Cc,"onTransitionEnd");Cn("onMouseEnter",["mouseout","mouseover"]);Cn("onMouseLeave",["mouseout","mouseover"]);Cn("onPointerEnter",["pointerout","pointerover"]);Cn("onPointerLeave",["pointerout","pointerover"]);en("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));en("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));en("onBeforeInput",["compositionend","keypress","textInput","paste"]);en("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));en("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));en("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Zn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),op=new Set("cancel close invalid load scroll toggle".split(" ").concat(Zn));function Tl(e,t,n){var i=e.type||"unknown-event";e.currentTarget=n,sh(i,t,void 0,e),e.currentTarget=null}function Tc(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var i=e[n],r=i.event;i=i.listeners;e:{var s=void 0;if(t)for(var a=i.length-1;0<=a;a--){var o=i[a],l=o.instance,c=o.currentTarget;if(o=o.listener,l!==s&&r.isPropagationStopped())break e;Tl(r,o,c),s=l}else for(a=0;a<i.length;a++){if(o=i[a],l=o.instance,c=o.currentTarget,o=o.listener,l!==s&&r.isPropagationStopped())break e;Tl(r,o,c),s=l}}}if(xr)throw e=da,xr=!1,da=null,e}function G(e,t){var n=t[ka];n===void 0&&(n=t[ka]=new Set);var i=e+"__bubble";n.has(i)||(jc(t,e,2,!1),n.add(i))}function Ts(e,t,n){var i=0;t&&(i|=4),jc(n,e,i,t)}var $i="_reactListening"+Math.random().toString(36).slice(2);function mi(e){if(!e[$i]){e[$i]=!0,Mu.forEach(function(n){n!=="selectionchange"&&(op.has(n)||Ts(n,!1,e),Ts(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[$i]||(t[$i]=!0,Ts("selectionchange",!1,t))}}function jc(e,t,n,i){switch(dc(t)){case 1:var r=kh;break;case 4:r=bh;break;default:r=lo}n=r.bind(null,t,n,e),r=void 0,!ca||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(r=!0),i?r!==void 0?e.addEventListener(t,n,{capture:!0,passive:r}):e.addEventListener(t,n,!0):r!==void 0?e.addEventListener(t,n,{passive:r}):e.addEventListener(t,n,!1)}function js(e,t,n,i,r){var s=i;if(!(t&1)&&!(t&2)&&i!==null)e:for(;;){if(i===null)return;var a=i.tag;if(a===3||a===4){var o=i.stateNode.containerInfo;if(o===r||o.nodeType===8&&o.parentNode===r)break;if(a===4)for(a=i.return;a!==null;){var l=a.tag;if((l===3||l===4)&&(l=a.stateNode.containerInfo,l===r||l.nodeType===8&&l.parentNode===r))return;a=a.return}for(;o!==null;){if(a=Vt(o),a===null)return;if(l=a.tag,l===5||l===6){i=s=a;continue e}o=o.parentNode}}i=i.return}Qu(function(){var c=s,d=ro(n),h=[];e:{var g=Nc.get(e);if(g!==void 0){var y=co,v=e;switch(e){case"keypress":if(ar(n)===0)break e;case"keydown":case"keyup":y=Ah;break;case"focusin":v="focus",y=ks;break;case"focusout":v="blur",y=ks;break;case"beforeblur":case"afterblur":y=ks;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=ml;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=Ch;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=Hh;break;case bc:case Sc:case Ec:y=jh;break;case Cc:y=Bh;break;case"scroll":y=Sh;break;case"wheel":y=Gh;break;case"copy":case"cut":case"paste":y=Rh;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=vl}var x=(t&4)!==0,S=!x&&e==="scroll",p=x?g!==null?g+"Capture":null:g;x=[];for(var f=c,m;f!==null;){m=f;var w=m.stateNode;if(m.tag===5&&w!==null&&(m=w,p!==null&&(w=ui(f,p),w!=null&&x.push(gi(f,w,m)))),S)break;f=f.return}0<x.length&&(g=new y(g,v,null,n,d),h.push({event:g,listeners:x}))}}if(!(t&7)){e:{if(g=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",g&&n!==la&&(v=n.relatedTarget||n.fromElement)&&(Vt(v)||v[dt]))break e;if((y||g)&&(g=d.window===d?d:(g=d.ownerDocument)?g.defaultView||g.parentWindow:window,y?(v=n.relatedTarget||n.toElement,y=c,v=v?Vt(v):null,v!==null&&(S=tn(v),v!==S||v.tag!==5&&v.tag!==6)&&(v=null)):(y=null,v=c),y!==v)){if(x=ml,w="onMouseLeave",p="onMouseEnter",f="mouse",(e==="pointerout"||e==="pointerover")&&(x=vl,w="onPointerLeave",p="onPointerEnter",f="pointer"),S=y==null?g:cn(y),m=v==null?g:cn(v),g=new x(w,f+"leave",y,n,d),g.target=S,g.relatedTarget=m,w=null,Vt(d)===c&&(x=new x(p,f+"enter",v,n,d),x.target=m,x.relatedTarget=S,w=x),S=w,y&&v)t:{for(x=y,p=v,f=0,m=x;m;m=rn(m))f++;for(m=0,w=p;w;w=rn(w))m++;for(;0<f-m;)x=rn(x),f--;for(;0<m-f;)p=rn(p),m--;for(;f--;){if(x===p||p!==null&&x===p.alternate)break t;x=rn(x),p=rn(p)}x=null}else x=null;y!==null&&jl(h,g,y,x,!1),v!==null&&S!==null&&jl(h,S,v,x,!0)}}e:{if(g=c?cn(c):window,y=g.nodeName&&g.nodeName.toLowerCase(),y==="select"||y==="input"&&g.type==="file")var b=Qh;else if(wl(g))if(vc)b=tp;else{b=Jh;var E=Xh}else(y=g.nodeName)&&y.toLowerCase()==="input"&&(g.type==="checkbox"||g.type==="radio")&&(b=ep);if(b&&(b=b(e,c))){gc(h,b,n,d);break e}E&&E(e,g,c),e==="focusout"&&(E=g._wrapperState)&&E.controlled&&g.type==="number"&&ia(g,"number",g.value)}switch(E=c?cn(c):window,e){case"focusin":(wl(E)||E.contentEditable==="true")&&(ln=E,ma=c,ti=null);break;case"focusout":ti=ma=ln=null;break;case"mousedown":ga=!0;break;case"contextmenu":case"mouseup":case"dragend":ga=!1,Cl(h,n,d);break;case"selectionchange":if(rp)break;case"keydown":case"keyup":Cl(h,n,d)}var N;if(ho)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else on?pc(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(hc&&n.locale!=="ko"&&(on||T!=="onCompositionStart"?T==="onCompositionEnd"&&on&&(N=fc()):(bt=d,uo="value"in bt?bt.value:bt.textContent,on=!0)),E=Er(c,T),0<E.length&&(T=new gl(T,e,null,n,d),h.push({event:T,listeners:E}),N?T.data=N:(N=mc(n),N!==null&&(T.data=N)))),(N=Wh?Yh(e,n):Kh(e,n))&&(c=Er(c,"onBeforeInput"),0<c.length&&(d=new gl("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:c}),d.data=N))}Tc(h,t)})}function gi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Er(e,t){for(var n=t+"Capture",i=[];e!==null;){var r=e,s=r.stateNode;r.tag===5&&s!==null&&(r=s,s=ui(e,n),s!=null&&i.unshift(gi(e,s,r)),s=ui(e,t),s!=null&&i.push(gi(e,s,r))),e=e.return}return i}function rn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function jl(e,t,n,i,r){for(var s=t._reactName,a=[];n!==null&&n!==i;){var o=n,l=o.alternate,c=o.stateNode;if(l!==null&&l===i)break;o.tag===5&&c!==null&&(o=c,r?(l=ui(n,s),l!=null&&a.unshift(gi(n,l,o))):r||(l=ui(n,s),l!=null&&a.push(gi(n,l,o)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var lp=/\r\n?/g,up=/\u0000|\uFFFD/g;function Fl(e){return(typeof e=="string"?e:""+e).replace(lp,`
`).replace(up,"")}function Wi(e,t,n){if(t=Fl(t),Fl(e)!==t&&n)throw Error(F(425))}function Cr(){}var va=null,ya=null;function xa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var wa=typeof setTimeout=="function"?setTimeout:void 0,cp=typeof clearTimeout=="function"?clearTimeout:void 0,Rl=typeof Promise=="function"?Promise:void 0,dp=typeof queueMicrotask=="function"?queueMicrotask:typeof Rl<"u"?function(e){return Rl.resolve(null).then(e).catch(fp)}:wa;function fp(e){setTimeout(function(){throw e})}function Fs(e,t){var n=t,i=0;do{var r=n.nextSibling;if(e.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(i===0){e.removeChild(r),fi(t);return}i--}else n!=="$"&&n!=="$?"&&n!=="$!"||i++;n=r}while(n);fi(t)}function jt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Il(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Ln=Math.random().toString(36).slice(2),Ze="__reactFiber$"+Ln,vi="__reactProps$"+Ln,dt="__reactContainer$"+Ln,ka="__reactEvents$"+Ln,hp="__reactListeners$"+Ln,pp="__reactHandles$"+Ln;function Vt(e){var t=e[Ze];if(t)return t;for(var n=e.parentNode;n;){if(t=n[dt]||n[Ze]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Il(e);e!==null;){if(n=e[Ze])return n;e=Il(e)}return t}e=n,n=e.parentNode}return null}function ji(e){return e=e[Ze]||e[dt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function cn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(F(33))}function Jr(e){return e[vi]||null}var ba=[],dn=-1;function Ot(e){return{current:e}}function $(e){0>dn||(e.current=ba[dn],ba[dn]=null,dn--)}function V(e,t){dn++,ba[dn]=e.current,e.current=t}var Lt={},ve=Ot(Lt),Ce=Ot(!1),Kt=Lt;function Nn(e,t){var n=e.type.contextTypes;if(!n)return Lt;var i=e.stateNode;if(i&&i.__reactInternalMemoizedUnmaskedChildContext===t)return i.__reactInternalMemoizedMaskedChildContext;var r={},s;for(s in n)r[s]=t[s];return i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=r),r}function Ne(e){return e=e.childContextTypes,e!=null}function Nr(){$(Ce),$(ve)}function Pl(e,t,n){if(ve.current!==Lt)throw Error(F(168));V(ve,t),V(Ce,n)}function Fc(e,t,n){var i=e.stateNode;if(t=t.childContextTypes,typeof i.getChildContext!="function")return n;i=i.getChildContext();for(var r in i)if(!(r in t))throw Error(F(108,Xf(e)||"Unknown",r));return Q({},n,i)}function Tr(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Lt,Kt=ve.current,V(ve,e),V(Ce,Ce.current),!0}function Dl(e,t,n){var i=e.stateNode;if(!i)throw Error(F(169));n?(e=Fc(e,t,Kt),i.__reactInternalMemoizedMergedChildContext=e,$(Ce),$(ve),V(ve,e)):$(Ce),V(Ce,n)}var st=null,es=!1,Rs=!1;function Rc(e){st===null?st=[e]:st.push(e)}function mp(e){es=!0,Rc(e)}function At(){if(!Rs&&st!==null){Rs=!0;var e=0,t=B;try{var n=st;for(B=1;e<n.length;e++){var i=n[e];do i=i(!0);while(i!==null)}st=null,es=!1}catch(r){throw st!==null&&(st=st.slice(e+1)),tc(so,At),r}finally{B=t,Rs=!1}}return null}var fn=[],hn=0,jr=null,Fr=0,Me=[],Oe=0,qt=null,at=1,ot="";function Ut(e,t){fn[hn++]=Fr,fn[hn++]=jr,jr=e,Fr=t}function Ic(e,t,n){Me[Oe++]=at,Me[Oe++]=ot,Me[Oe++]=qt,qt=e;var i=at;e=ot;var r=32-$e(i)-1;i&=~(1<<r),n+=1;var s=32-$e(t)+r;if(30<s){var a=r-r%5;s=(i&(1<<a)-1).toString(32),i>>=a,r-=a,at=1<<32-$e(t)+r|n<<r|i,ot=s+e}else at=1<<s|n<<r|i,ot=e}function mo(e){e.return!==null&&(Ut(e,1),Ic(e,1,0))}function go(e){for(;e===jr;)jr=fn[--hn],fn[hn]=null,Fr=fn[--hn],fn[hn]=null;for(;e===qt;)qt=Me[--Oe],Me[Oe]=null,ot=Me[--Oe],Me[Oe]=null,at=Me[--Oe],Me[Oe]=null}var Ie=null,Re=null,W=!1,Ge=null;function Pc(e,t){var n=Ae(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Ll(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ie=e,Re=jt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ie=e,Re=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=qt!==null?{id:at,overflow:ot}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ae(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ie=e,Re=null,!0):!1;default:return!1}}function Sa(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ea(e){if(W){var t=Re;if(t){var n=t;if(!Ll(e,t)){if(Sa(e))throw Error(F(418));t=jt(n.nextSibling);var i=Ie;t&&Ll(e,t)?Pc(i,n):(e.flags=e.flags&-4097|2,W=!1,Ie=e)}}else{if(Sa(e))throw Error(F(418));e.flags=e.flags&-4097|2,W=!1,Ie=e}}}function Ml(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ie=e}function Yi(e){if(e!==Ie)return!1;if(!W)return Ml(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!xa(e.type,e.memoizedProps)),t&&(t=Re)){if(Sa(e))throw Dc(),Error(F(418));for(;t;)Pc(e,t),t=jt(t.nextSibling)}if(Ml(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(F(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Re=jt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Re=null}}else Re=Ie?jt(e.stateNode.nextSibling):null;return!0}function Dc(){for(var e=Re;e;)e=jt(e.nextSibling)}function Tn(){Re=Ie=null,W=!1}function vo(e){Ge===null?Ge=[e]:Ge.push(e)}var gp=mt.ReactCurrentBatchConfig;function Bn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(F(309));var i=n.stateNode}if(!i)throw Error(F(147,e));var r=i,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(a){var o=r.refs;a===null?delete o[s]:o[s]=a},t._stringRef=s,t)}if(typeof e!="string")throw Error(F(284));if(!n._owner)throw Error(F(290,e))}return e}function Ki(e,t){throw e=Object.prototype.toString.call(t),Error(F(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ol(e){var t=e._init;return t(e._payload)}function Lc(e){function t(p,f){if(e){var m=p.deletions;m===null?(p.deletions=[f],p.flags|=16):m.push(f)}}function n(p,f){if(!e)return null;for(;f!==null;)t(p,f),f=f.sibling;return null}function i(p,f){for(p=new Map;f!==null;)f.key!==null?p.set(f.key,f):p.set(f.index,f),f=f.sibling;return p}function r(p,f){return p=Pt(p,f),p.index=0,p.sibling=null,p}function s(p,f,m){return p.index=m,e?(m=p.alternate,m!==null?(m=m.index,m<f?(p.flags|=2,f):m):(p.flags|=2,f)):(p.flags|=1048576,f)}function a(p){return e&&p.alternate===null&&(p.flags|=2),p}function o(p,f,m,w){return f===null||f.tag!==6?(f=As(m,p.mode,w),f.return=p,f):(f=r(f,m),f.return=p,f)}function l(p,f,m,w){var b=m.type;return b===an?d(p,f,m.props.children,w,m.key):f!==null&&(f.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===yt&&Ol(b)===f.type)?(w=r(f,m.props),w.ref=Bn(p,f,m),w.return=p,w):(w=hr(m.type,m.key,m.props,null,p.mode,w),w.ref=Bn(p,f,m),w.return=p,w)}function c(p,f,m,w){return f===null||f.tag!==4||f.stateNode.containerInfo!==m.containerInfo||f.stateNode.implementation!==m.implementation?(f=zs(m,p.mode,w),f.return=p,f):(f=r(f,m.children||[]),f.return=p,f)}function d(p,f,m,w,b){return f===null||f.tag!==7?(f=Yt(m,p.mode,w,b),f.return=p,f):(f=r(f,m),f.return=p,f)}function h(p,f,m){if(typeof f=="string"&&f!==""||typeof f=="number")return f=As(""+f,p.mode,m),f.return=p,f;if(typeof f=="object"&&f!==null){switch(f.$$typeof){case Ai:return m=hr(f.type,f.key,f.props,null,p.mode,m),m.ref=Bn(p,null,f),m.return=p,m;case sn:return f=zs(f,p.mode,m),f.return=p,f;case yt:var w=f._init;return h(p,w(f._payload),m)}if(Kn(f)||An(f))return f=Yt(f,p.mode,m,null),f.return=p,f;Ki(p,f)}return null}function g(p,f,m,w){var b=f!==null?f.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return b!==null?null:o(p,f,""+m,w);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Ai:return m.key===b?l(p,f,m,w):null;case sn:return m.key===b?c(p,f,m,w):null;case yt:return b=m._init,g(p,f,b(m._payload),w)}if(Kn(m)||An(m))return b!==null?null:d(p,f,m,w,null);Ki(p,m)}return null}function y(p,f,m,w,b){if(typeof w=="string"&&w!==""||typeof w=="number")return p=p.get(m)||null,o(f,p,""+w,b);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case Ai:return p=p.get(w.key===null?m:w.key)||null,l(f,p,w,b);case sn:return p=p.get(w.key===null?m:w.key)||null,c(f,p,w,b);case yt:var E=w._init;return y(p,f,m,E(w._payload),b)}if(Kn(w)||An(w))return p=p.get(m)||null,d(f,p,w,b,null);Ki(f,w)}return null}function v(p,f,m,w){for(var b=null,E=null,N=f,T=f=0,j=null;N!==null&&T<m.length;T++){N.index>T?(j=N,N=null):j=N.sibling;var C=g(p,N,m[T],w);if(C===null){N===null&&(N=j);break}e&&N&&C.alternate===null&&t(p,N),f=s(C,f,T),E===null?b=C:E.sibling=C,E=C,N=j}if(T===m.length)return n(p,N),W&&Ut(p,T),b;if(N===null){for(;T<m.length;T++)N=h(p,m[T],w),N!==null&&(f=s(N,f,T),E===null?b=N:E.sibling=N,E=N);return W&&Ut(p,T),b}for(N=i(p,N);T<m.length;T++)j=y(N,p,T,m[T],w),j!==null&&(e&&j.alternate!==null&&N.delete(j.key===null?T:j.key),f=s(j,f,T),E===null?b=j:E.sibling=j,E=j);return e&&N.forEach(function(R){return t(p,R)}),W&&Ut(p,T),b}function x(p,f,m,w){var b=An(m);if(typeof b!="function")throw Error(F(150));if(m=b.call(m),m==null)throw Error(F(151));for(var E=b=null,N=f,T=f=0,j=null,C=m.next();N!==null&&!C.done;T++,C=m.next()){N.index>T?(j=N,N=null):j=N.sibling;var R=g(p,N,C.value,w);if(R===null){N===null&&(N=j);break}e&&N&&R.alternate===null&&t(p,N),f=s(R,f,T),E===null?b=R:E.sibling=R,E=R,N=j}if(C.done)return n(p,N),W&&Ut(p,T),b;if(N===null){for(;!C.done;T++,C=m.next())C=h(p,C.value,w),C!==null&&(f=s(C,f,T),E===null?b=C:E.sibling=C,E=C);return W&&Ut(p,T),b}for(N=i(p,N);!C.done;T++,C=m.next())C=y(N,p,T,C.value,w),C!==null&&(e&&C.alternate!==null&&N.delete(C.key===null?T:C.key),f=s(C,f,T),E===null?b=C:E.sibling=C,E=C);return e&&N.forEach(function(L){return t(p,L)}),W&&Ut(p,T),b}function S(p,f,m,w){if(typeof m=="object"&&m!==null&&m.type===an&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case Ai:e:{for(var b=m.key,E=f;E!==null;){if(E.key===b){if(b=m.type,b===an){if(E.tag===7){n(p,E.sibling),f=r(E,m.props.children),f.return=p,p=f;break e}}else if(E.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===yt&&Ol(b)===E.type){n(p,E.sibling),f=r(E,m.props),f.ref=Bn(p,E,m),f.return=p,p=f;break e}n(p,E);break}else t(p,E);E=E.sibling}m.type===an?(f=Yt(m.props.children,p.mode,w,m.key),f.return=p,p=f):(w=hr(m.type,m.key,m.props,null,p.mode,w),w.ref=Bn(p,f,m),w.return=p,p=w)}return a(p);case sn:e:{for(E=m.key;f!==null;){if(f.key===E)if(f.tag===4&&f.stateNode.containerInfo===m.containerInfo&&f.stateNode.implementation===m.implementation){n(p,f.sibling),f=r(f,m.children||[]),f.return=p,p=f;break e}else{n(p,f);break}else t(p,f);f=f.sibling}f=zs(m,p.mode,w),f.return=p,p=f}return a(p);case yt:return E=m._init,S(p,f,E(m._payload),w)}if(Kn(m))return v(p,f,m,w);if(An(m))return x(p,f,m,w);Ki(p,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,f!==null&&f.tag===6?(n(p,f.sibling),f=r(f,m),f.return=p,p=f):(n(p,f),f=As(m,p.mode,w),f.return=p,p=f),a(p)):n(p,f)}return S}var jn=Lc(!0),Mc=Lc(!1),Rr=Ot(null),Ir=null,pn=null,yo=null;function xo(){yo=pn=Ir=null}function wo(e){var t=Rr.current;$(Rr),e._currentValue=t}function Ca(e,t,n){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===n)break;e=e.return}}function bn(e,t){Ir=e,yo=pn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ee=!0),e.firstContext=null)}function _e(e){var t=e._currentValue;if(yo!==e)if(e={context:e,memoizedValue:t,next:null},pn===null){if(Ir===null)throw Error(F(308));pn=e,Ir.dependencies={lanes:0,firstContext:e}}else pn=pn.next=e;return t}var Gt=null;function ko(e){Gt===null?Gt=[e]:Gt.push(e)}function Oc(e,t,n,i){var r=t.interleaved;return r===null?(n.next=n,ko(t)):(n.next=r.next,r.next=n),t.interleaved=n,ft(e,i)}function ft(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var xt=!1;function bo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ac(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function lt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ft(e,t,n){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,_&2){var r=i.pending;return r===null?t.next=t:(t.next=r.next,r.next=t),i.pending=t,ft(e,n)}return r=i.interleaved,r===null?(t.next=t,ko(i)):(t.next=r.next,r.next=t),i.interleaved=t,ft(e,n)}function or(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,ao(e,n)}}function Al(e,t){var n=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var r=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?r=s=a:s=s.next=a,n=n.next}while(n!==null);s===null?r=s=t:s=s.next=t}else r=s=t;n={baseState:i.baseState,firstBaseUpdate:r,lastBaseUpdate:s,shared:i.shared,effects:i.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Pr(e,t,n,i){var r=e.updateQueue;xt=!1;var s=r.firstBaseUpdate,a=r.lastBaseUpdate,o=r.shared.pending;if(o!==null){r.shared.pending=null;var l=o,c=l.next;l.next=null,a===null?s=c:a.next=c,a=l;var d=e.alternate;d!==null&&(d=d.updateQueue,o=d.lastBaseUpdate,o!==a&&(o===null?d.firstBaseUpdate=c:o.next=c,d.lastBaseUpdate=l))}if(s!==null){var h=r.baseState;a=0,d=c=l=null,o=s;do{var g=o.lane,y=o.eventTime;if((i&g)===g){d!==null&&(d=d.next={eventTime:y,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var v=e,x=o;switch(g=t,y=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){h=v.call(y,h,g);break e}h=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,g=typeof v=="function"?v.call(y,h,g):v,g==null)break e;h=Q({},h,g);break e;case 2:xt=!0}}o.callback!==null&&o.lane!==0&&(e.flags|=64,g=r.effects,g===null?r.effects=[o]:g.push(o))}else y={eventTime:y,lane:g,tag:o.tag,payload:o.payload,callback:o.callback,next:null},d===null?(c=d=y,l=h):d=d.next=y,a|=g;if(o=o.next,o===null){if(o=r.shared.pending,o===null)break;g=o,o=g.next,g.next=null,r.lastBaseUpdate=g,r.shared.pending=null}}while(!0);if(d===null&&(l=h),r.baseState=l,r.firstBaseUpdate=c,r.lastBaseUpdate=d,t=r.shared.interleaved,t!==null){r=t;do a|=r.lane,r=r.next;while(r!==t)}else s===null&&(r.shared.lanes=0);Qt|=a,e.lanes=a,e.memoizedState=h}}function zl(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var i=e[t],r=i.callback;if(r!==null){if(i.callback=null,i=n,typeof r!="function")throw Error(F(191,r));r.call(i)}}}var Fi={},Je=Ot(Fi),yi=Ot(Fi),xi=Ot(Fi);function $t(e){if(e===Fi)throw Error(F(174));return e}function So(e,t){switch(V(xi,t),V(yi,e),V(Je,Fi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:sa(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=sa(t,e)}$(Je),V(Je,t)}function Fn(){$(Je),$(yi),$(xi)}function zc(e){$t(xi.current);var t=$t(Je.current),n=sa(t,e.type);t!==n&&(V(yi,e),V(Je,n))}function Eo(e){yi.current===e&&($(Je),$(yi))}var K=Ot(0);function Dr(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Is=[];function Co(){for(var e=0;e<Is.length;e++)Is[e]._workInProgressVersionPrimary=null;Is.length=0}var lr=mt.ReactCurrentDispatcher,Ps=mt.ReactCurrentBatchConfig,Zt=0,q=null,re=null,oe=null,Lr=!1,ni=!1,wi=0,vp=0;function he(){throw Error(F(321))}function No(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ye(e[n],t[n]))return!1;return!0}function To(e,t,n,i,r,s){if(Zt=s,q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,lr.current=e===null||e.memoizedState===null?kp:bp,e=n(i,r),ni){s=0;do{if(ni=!1,wi=0,25<=s)throw Error(F(301));s+=1,oe=re=null,t.updateQueue=null,lr.current=Sp,e=n(i,r)}while(ni)}if(lr.current=Mr,t=re!==null&&re.next!==null,Zt=0,oe=re=q=null,Lr=!1,t)throw Error(F(300));return e}function jo(){var e=wi!==0;return wi=0,e}function qe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return oe===null?q.memoizedState=oe=e:oe=oe.next=e,oe}function He(){if(re===null){var e=q.alternate;e=e!==null?e.memoizedState:null}else e=re.next;var t=oe===null?q.memoizedState:oe.next;if(t!==null)oe=t,re=e;else{if(e===null)throw Error(F(310));re=e,e={memoizedState:re.memoizedState,baseState:re.baseState,baseQueue:re.baseQueue,queue:re.queue,next:null},oe===null?q.memoizedState=oe=e:oe=oe.next=e}return oe}function ki(e,t){return typeof t=="function"?t(e):t}function Ds(e){var t=He(),n=t.queue;if(n===null)throw Error(F(311));n.lastRenderedReducer=e;var i=re,r=i.baseQueue,s=n.pending;if(s!==null){if(r!==null){var a=r.next;r.next=s.next,s.next=a}i.baseQueue=r=s,n.pending=null}if(r!==null){s=r.next,i=i.baseState;var o=a=null,l=null,c=s;do{var d=c.lane;if((Zt&d)===d)l!==null&&(l=l.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),i=c.hasEagerState?c.eagerState:e(i,c.action);else{var h={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};l===null?(o=l=h,a=i):l=l.next=h,q.lanes|=d,Qt|=d}c=c.next}while(c!==null&&c!==s);l===null?a=i:l.next=o,Ye(i,t.memoizedState)||(Ee=!0),t.memoizedState=i,t.baseState=a,t.baseQueue=l,n.lastRenderedState=i}if(e=n.interleaved,e!==null){r=e;do s=r.lane,q.lanes|=s,Qt|=s,r=r.next;while(r!==e)}else r===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ls(e){var t=He(),n=t.queue;if(n===null)throw Error(F(311));n.lastRenderedReducer=e;var i=n.dispatch,r=n.pending,s=t.memoizedState;if(r!==null){n.pending=null;var a=r=r.next;do s=e(s,a.action),a=a.next;while(a!==r);Ye(s,t.memoizedState)||(Ee=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,i]}function _c(){}function Hc(e,t){var n=q,i=He(),r=t(),s=!Ye(i.memoizedState,r);if(s&&(i.memoizedState=r,Ee=!0),i=i.queue,Fo(Vc.bind(null,n,i,e),[e]),i.getSnapshot!==t||s||oe!==null&&oe.memoizedState.tag&1){if(n.flags|=2048,bi(9,Bc.bind(null,n,i,r,t),void 0,null),le===null)throw Error(F(349));Zt&30||Uc(n,t,r)}return r}function Uc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=q.updateQueue,t===null?(t={lastEffect:null,stores:null},q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Bc(e,t,n,i){t.value=n,t.getSnapshot=i,Gc(t)&&$c(e)}function Vc(e,t,n){return n(function(){Gc(t)&&$c(e)})}function Gc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ye(e,n)}catch{return!0}}function $c(e){var t=ft(e,1);t!==null&&We(t,e,1,-1)}function _l(e){var t=qe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ki,lastRenderedState:e},t.queue=e,e=e.dispatch=wp.bind(null,q,e),[t.memoizedState,e]}function bi(e,t,n,i){return e={tag:e,create:t,destroy:n,deps:i,next:null},t=q.updateQueue,t===null?(t={lastEffect:null,stores:null},q.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(i=n.next,n.next=e,e.next=i,t.lastEffect=e)),e}function Wc(){return He().memoizedState}function ur(e,t,n,i){var r=qe();q.flags|=e,r.memoizedState=bi(1|t,n,void 0,i===void 0?null:i)}function ts(e,t,n,i){var r=He();i=i===void 0?null:i;var s=void 0;if(re!==null){var a=re.memoizedState;if(s=a.destroy,i!==null&&No(i,a.deps)){r.memoizedState=bi(t,n,s,i);return}}q.flags|=e,r.memoizedState=bi(1|t,n,s,i)}function Hl(e,t){return ur(8390656,8,e,t)}function Fo(e,t){return ts(2048,8,e,t)}function Yc(e,t){return ts(4,2,e,t)}function Kc(e,t){return ts(4,4,e,t)}function qc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Zc(e,t,n){return n=n!=null?n.concat([e]):null,ts(4,4,qc.bind(null,t,e),n)}function Ro(){}function Qc(e,t){var n=He();t=t===void 0?null:t;var i=n.memoizedState;return i!==null&&t!==null&&No(t,i[1])?i[0]:(n.memoizedState=[e,t],e)}function Xc(e,t){var n=He();t=t===void 0?null:t;var i=n.memoizedState;return i!==null&&t!==null&&No(t,i[1])?i[0]:(e=e(),n.memoizedState=[e,t],e)}function Jc(e,t,n){return Zt&21?(Ye(n,t)||(n=rc(),q.lanes|=n,Qt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ee=!0),e.memoizedState=n)}function yp(e,t){var n=B;B=n!==0&&4>n?n:4,e(!0);var i=Ps.transition;Ps.transition={};try{e(!1),t()}finally{B=n,Ps.transition=i}}function ed(){return He().memoizedState}function xp(e,t,n){var i=It(e);if(n={lane:i,action:n,hasEagerState:!1,eagerState:null,next:null},td(e))nd(t,n);else if(n=Oc(e,t,n,i),n!==null){var r=xe();We(n,e,i,r),id(n,t,i)}}function wp(e,t,n){var i=It(e),r={lane:i,action:n,hasEagerState:!1,eagerState:null,next:null};if(td(e))nd(t,r);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var a=t.lastRenderedState,o=s(a,n);if(r.hasEagerState=!0,r.eagerState=o,Ye(o,a)){var l=t.interleaved;l===null?(r.next=r,ko(t)):(r.next=l.next,l.next=r),t.interleaved=r;return}}catch{}finally{}n=Oc(e,t,r,i),n!==null&&(r=xe(),We(n,e,i,r),id(n,t,i))}}function td(e){var t=e.alternate;return e===q||t!==null&&t===q}function nd(e,t){ni=Lr=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function id(e,t,n){if(n&4194240){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,ao(e,n)}}var Mr={readContext:_e,useCallback:he,useContext:he,useEffect:he,useImperativeHandle:he,useInsertionEffect:he,useLayoutEffect:he,useMemo:he,useReducer:he,useRef:he,useState:he,useDebugValue:he,useDeferredValue:he,useTransition:he,useMutableSource:he,useSyncExternalStore:he,useId:he,unstable_isNewReconciler:!1},kp={readContext:_e,useCallback:function(e,t){return qe().memoizedState=[e,t===void 0?null:t],e},useContext:_e,useEffect:Hl,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ur(4194308,4,qc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ur(4194308,4,e,t)},useInsertionEffect:function(e,t){return ur(4,2,e,t)},useMemo:function(e,t){var n=qe();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var i=qe();return t=n!==void 0?n(t):t,i.memoizedState=i.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},i.queue=e,e=e.dispatch=xp.bind(null,q,e),[i.memoizedState,e]},useRef:function(e){var t=qe();return e={current:e},t.memoizedState=e},useState:_l,useDebugValue:Ro,useDeferredValue:function(e){return qe().memoizedState=e},useTransition:function(){var e=_l(!1),t=e[0];return e=yp.bind(null,e[1]),qe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var i=q,r=qe();if(W){if(n===void 0)throw Error(F(407));n=n()}else{if(n=t(),le===null)throw Error(F(349));Zt&30||Uc(i,t,n)}r.memoizedState=n;var s={value:n,getSnapshot:t};return r.queue=s,Hl(Vc.bind(null,i,s,e),[e]),i.flags|=2048,bi(9,Bc.bind(null,i,s,n,t),void 0,null),n},useId:function(){var e=qe(),t=le.identifierPrefix;if(W){var n=ot,i=at;n=(i&~(1<<32-$e(i)-1)).toString(32)+n,t=":"+t+"R"+n,n=wi++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=vp++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},bp={readContext:_e,useCallback:Qc,useContext:_e,useEffect:Fo,useImperativeHandle:Zc,useInsertionEffect:Yc,useLayoutEffect:Kc,useMemo:Xc,useReducer:Ds,useRef:Wc,useState:function(){return Ds(ki)},useDebugValue:Ro,useDeferredValue:function(e){var t=He();return Jc(t,re.memoizedState,e)},useTransition:function(){var e=Ds(ki)[0],t=He().memoizedState;return[e,t]},useMutableSource:_c,useSyncExternalStore:Hc,useId:ed,unstable_isNewReconciler:!1},Sp={readContext:_e,useCallback:Qc,useContext:_e,useEffect:Fo,useImperativeHandle:Zc,useInsertionEffect:Yc,useLayoutEffect:Kc,useMemo:Xc,useReducer:Ls,useRef:Wc,useState:function(){return Ls(ki)},useDebugValue:Ro,useDeferredValue:function(e){var t=He();return re===null?t.memoizedState=e:Jc(t,re.memoizedState,e)},useTransition:function(){var e=Ls(ki)[0],t=He().memoizedState;return[e,t]},useMutableSource:_c,useSyncExternalStore:Hc,useId:ed,unstable_isNewReconciler:!1};function Be(e,t){if(e&&e.defaultProps){t=Q({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Na(e,t,n,i){t=e.memoizedState,n=n(i,t),n=n==null?t:Q({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ns={isMounted:function(e){return(e=e._reactInternals)?tn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var i=xe(),r=It(e),s=lt(i,r);s.payload=t,n!=null&&(s.callback=n),t=Ft(e,s,r),t!==null&&(We(t,e,r,i),or(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var i=xe(),r=It(e),s=lt(i,r);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Ft(e,s,r),t!==null&&(We(t,e,r,i),or(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=xe(),i=It(e),r=lt(n,i);r.tag=2,t!=null&&(r.callback=t),t=Ft(e,r,i),t!==null&&(We(t,e,i,n),or(t,e,i))}};function Ul(e,t,n,i,r,s,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,s,a):t.prototype&&t.prototype.isPureReactComponent?!pi(n,i)||!pi(r,s):!0}function rd(e,t,n){var i=!1,r=Lt,s=t.contextType;return typeof s=="object"&&s!==null?s=_e(s):(r=Ne(t)?Kt:ve.current,i=t.contextTypes,s=(i=i!=null)?Nn(e,r):Lt),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ns,e.stateNode=t,t._reactInternals=e,i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=r,e.__reactInternalMemoizedMaskedChildContext=s),t}function Bl(e,t,n,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,i),t.state!==e&&ns.enqueueReplaceState(t,t.state,null)}function Ta(e,t,n,i){var r=e.stateNode;r.props=n,r.state=e.memoizedState,r.refs={},bo(e);var s=t.contextType;typeof s=="object"&&s!==null?r.context=_e(s):(s=Ne(t)?Kt:ve.current,r.context=Nn(e,s)),r.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(Na(e,t,s,n),r.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(t=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),t!==r.state&&ns.enqueueReplaceState(r,r.state,null),Pr(e,n,r,i),r.state=e.memoizedState),typeof r.componentDidMount=="function"&&(e.flags|=4194308)}function Rn(e,t){try{var n="",i=t;do n+=Qf(i),i=i.return;while(i);var r=n}catch(s){r=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:r,digest:null}}function Ms(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ja(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Ep=typeof WeakMap=="function"?WeakMap:Map;function sd(e,t,n){n=lt(-1,n),n.tag=3,n.payload={element:null};var i=t.value;return n.callback=function(){Ar||(Ar=!0,za=i),ja(e,t)},n}function ad(e,t,n){n=lt(-1,n),n.tag=3;var i=e.type.getDerivedStateFromError;if(typeof i=="function"){var r=t.value;n.payload=function(){return i(r)},n.callback=function(){ja(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){ja(e,t),typeof i!="function"&&(Rt===null?Rt=new Set([this]):Rt.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function Vl(e,t,n){var i=e.pingCache;if(i===null){i=e.pingCache=new Ep;var r=new Set;i.set(t,r)}else r=i.get(t),r===void 0&&(r=new Set,i.set(t,r));r.has(n)||(r.add(n),e=zp.bind(null,e,t,n),t.then(e,e))}function Gl(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function $l(e,t,n,i,r){return e.mode&1?(e.flags|=65536,e.lanes=r,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=lt(-1,1),t.tag=2,Ft(n,t,1))),n.lanes|=1),e)}var Cp=mt.ReactCurrentOwner,Ee=!1;function ye(e,t,n,i){t.child=e===null?Mc(t,null,n,i):jn(t,e.child,n,i)}function Wl(e,t,n,i,r){n=n.render;var s=t.ref;return bn(t,r),i=To(e,t,n,i,s,r),n=jo(),e!==null&&!Ee?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~r,ht(e,t,r)):(W&&n&&mo(t),t.flags|=1,ye(e,t,i,r),t.child)}function Yl(e,t,n,i,r){if(e===null){var s=n.type;return typeof s=="function"&&!zo(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,od(e,t,s,i,r)):(e=hr(n.type,null,i,t,t.mode,r),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&r)){var a=s.memoizedProps;if(n=n.compare,n=n!==null?n:pi,n(a,i)&&e.ref===t.ref)return ht(e,t,r)}return t.flags|=1,e=Pt(s,i),e.ref=t.ref,e.return=t,t.child=e}function od(e,t,n,i,r){if(e!==null){var s=e.memoizedProps;if(pi(s,i)&&e.ref===t.ref)if(Ee=!1,t.pendingProps=i=s,(e.lanes&r)!==0)e.flags&131072&&(Ee=!0);else return t.lanes=e.lanes,ht(e,t,r)}return Fa(e,t,n,i,r)}function ld(e,t,n){var i=t.pendingProps,r=i.children,s=e!==null?e.memoizedState:null;if(i.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},V(gn,je),je|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,V(gn,je),je|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},i=s!==null?s.baseLanes:n,V(gn,je),je|=i}else s!==null?(i=s.baseLanes|n,t.memoizedState=null):i=n,V(gn,je),je|=i;return ye(e,t,r,n),t.child}function ud(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Fa(e,t,n,i,r){var s=Ne(n)?Kt:ve.current;return s=Nn(t,s),bn(t,r),n=To(e,t,n,i,s,r),i=jo(),e!==null&&!Ee?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~r,ht(e,t,r)):(W&&i&&mo(t),t.flags|=1,ye(e,t,n,r),t.child)}function Kl(e,t,n,i,r){if(Ne(n)){var s=!0;Tr(t)}else s=!1;if(bn(t,r),t.stateNode===null)cr(e,t),rd(t,n,i),Ta(t,n,i,r),i=!0;else if(e===null){var a=t.stateNode,o=t.memoizedProps;a.props=o;var l=a.context,c=n.contextType;typeof c=="object"&&c!==null?c=_e(c):(c=Ne(n)?Kt:ve.current,c=Nn(t,c));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof a.getSnapshotBeforeUpdate=="function";h||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(o!==i||l!==c)&&Bl(t,a,i,c),xt=!1;var g=t.memoizedState;a.state=g,Pr(t,i,a,r),l=t.memoizedState,o!==i||g!==l||Ce.current||xt?(typeof d=="function"&&(Na(t,n,d,i),l=t.memoizedState),(o=xt||Ul(t,n,o,i,g,l,c))?(h||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=l),a.props=i,a.state=l,a.context=c,i=o):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{a=t.stateNode,Ac(e,t),o=t.memoizedProps,c=t.type===t.elementType?o:Be(t.type,o),a.props=c,h=t.pendingProps,g=a.context,l=n.contextType,typeof l=="object"&&l!==null?l=_e(l):(l=Ne(n)?Kt:ve.current,l=Nn(t,l));var y=n.getDerivedStateFromProps;(d=typeof y=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(o!==h||g!==l)&&Bl(t,a,i,l),xt=!1,g=t.memoizedState,a.state=g,Pr(t,i,a,r);var v=t.memoizedState;o!==h||g!==v||Ce.current||xt?(typeof y=="function"&&(Na(t,n,y,i),v=t.memoizedState),(c=xt||Ul(t,n,c,i,g,v,l)||!1)?(d||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(i,v,l),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(i,v,l)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||o===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=v),a.props=i,a.state=v,a.context=l,i=c):(typeof a.componentDidUpdate!="function"||o===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),i=!1)}return Ra(e,t,n,i,s,r)}function Ra(e,t,n,i,r,s){ud(e,t);var a=(t.flags&128)!==0;if(!i&&!a)return r&&Dl(t,n,!1),ht(e,t,s);i=t.stateNode,Cp.current=t;var o=a&&typeof n.getDerivedStateFromError!="function"?null:i.render();return t.flags|=1,e!==null&&a?(t.child=jn(t,e.child,null,s),t.child=jn(t,null,o,s)):ye(e,t,o,s),t.memoizedState=i.state,r&&Dl(t,n,!0),t.child}function cd(e){var t=e.stateNode;t.pendingContext?Pl(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Pl(e,t.context,!1),So(e,t.containerInfo)}function ql(e,t,n,i,r){return Tn(),vo(r),t.flags|=256,ye(e,t,n,i),t.child}var Ia={dehydrated:null,treeContext:null,retryLane:0};function Pa(e){return{baseLanes:e,cachePool:null,transitions:null}}function dd(e,t,n){var i=t.pendingProps,r=K.current,s=!1,a=(t.flags&128)!==0,o;if((o=a)||(o=e!==null&&e.memoizedState===null?!1:(r&2)!==0),o?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(r|=1),V(K,r&1),e===null)return Ea(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=i.children,e=i.fallback,s?(i=t.mode,s=t.child,a={mode:"hidden",children:a},!(i&1)&&s!==null?(s.childLanes=0,s.pendingProps=a):s=ss(a,i,0,null),e=Yt(e,i,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Pa(n),t.memoizedState=Ia,e):Io(t,a));if(r=e.memoizedState,r!==null&&(o=r.dehydrated,o!==null))return Np(e,t,a,i,o,r,n);if(s){s=i.fallback,a=t.mode,r=e.child,o=r.sibling;var l={mode:"hidden",children:i.children};return!(a&1)&&t.child!==r?(i=t.child,i.childLanes=0,i.pendingProps=l,t.deletions=null):(i=Pt(r,l),i.subtreeFlags=r.subtreeFlags&14680064),o!==null?s=Pt(o,s):(s=Yt(s,a,n,null),s.flags|=2),s.return=t,i.return=t,i.sibling=s,t.child=i,i=s,s=t.child,a=e.child.memoizedState,a=a===null?Pa(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},s.memoizedState=a,s.childLanes=e.childLanes&~n,t.memoizedState=Ia,i}return s=e.child,e=s.sibling,i=Pt(s,{mode:"visible",children:i.children}),!(t.mode&1)&&(i.lanes=n),i.return=t,i.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=i,t.memoizedState=null,i}function Io(e,t){return t=ss({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function qi(e,t,n,i){return i!==null&&vo(i),jn(t,e.child,null,n),e=Io(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Np(e,t,n,i,r,s,a){if(n)return t.flags&256?(t.flags&=-257,i=Ms(Error(F(422))),qi(e,t,a,i)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=i.fallback,r=t.mode,i=ss({mode:"visible",children:i.children},r,0,null),s=Yt(s,r,a,null),s.flags|=2,i.return=t,s.return=t,i.sibling=s,t.child=i,t.mode&1&&jn(t,e.child,null,a),t.child.memoizedState=Pa(a),t.memoizedState=Ia,s);if(!(t.mode&1))return qi(e,t,a,null);if(r.data==="$!"){if(i=r.nextSibling&&r.nextSibling.dataset,i)var o=i.dgst;return i=o,s=Error(F(419)),i=Ms(s,i,void 0),qi(e,t,a,i)}if(o=(a&e.childLanes)!==0,Ee||o){if(i=le,i!==null){switch(a&-a){case 4:r=2;break;case 16:r=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:r=32;break;case 536870912:r=268435456;break;default:r=0}r=r&(i.suspendedLanes|a)?0:r,r!==0&&r!==s.retryLane&&(s.retryLane=r,ft(e,r),We(i,e,r,-1))}return Ao(),i=Ms(Error(F(421))),qi(e,t,a,i)}return r.data==="$?"?(t.flags|=128,t.child=e.child,t=_p.bind(null,e),r._reactRetry=t,null):(e=s.treeContext,Re=jt(r.nextSibling),Ie=t,W=!0,Ge=null,e!==null&&(Me[Oe++]=at,Me[Oe++]=ot,Me[Oe++]=qt,at=e.id,ot=e.overflow,qt=t),t=Io(t,i.children),t.flags|=4096,t)}function Zl(e,t,n){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),Ca(e.return,t,n)}function Os(e,t,n,i,r){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:r}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=i,s.tail=n,s.tailMode=r)}function fd(e,t,n){var i=t.pendingProps,r=i.revealOrder,s=i.tail;if(ye(e,t,i.children,n),i=K.current,i&2)i=i&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Zl(e,n,t);else if(e.tag===19)Zl(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}if(V(K,i),!(t.mode&1))t.memoizedState=null;else switch(r){case"forwards":for(n=t.child,r=null;n!==null;)e=n.alternate,e!==null&&Dr(e)===null&&(r=n),n=n.sibling;n=r,n===null?(r=t.child,t.child=null):(r=n.sibling,n.sibling=null),Os(t,!1,r,n,s);break;case"backwards":for(n=null,r=t.child,t.child=null;r!==null;){if(e=r.alternate,e!==null&&Dr(e)===null){t.child=r;break}e=r.sibling,r.sibling=n,n=r,r=e}Os(t,!0,n,null,s);break;case"together":Os(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function cr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ht(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Qt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(F(153));if(t.child!==null){for(e=t.child,n=Pt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Pt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Tp(e,t,n){switch(t.tag){case 3:cd(t),Tn();break;case 5:zc(t);break;case 1:Ne(t.type)&&Tr(t);break;case 4:So(t,t.stateNode.containerInfo);break;case 10:var i=t.type._context,r=t.memoizedProps.value;V(Rr,i._currentValue),i._currentValue=r;break;case 13:if(i=t.memoizedState,i!==null)return i.dehydrated!==null?(V(K,K.current&1),t.flags|=128,null):n&t.child.childLanes?dd(e,t,n):(V(K,K.current&1),e=ht(e,t,n),e!==null?e.sibling:null);V(K,K.current&1);break;case 19:if(i=(n&t.childLanes)!==0,e.flags&128){if(i)return fd(e,t,n);t.flags|=128}if(r=t.memoizedState,r!==null&&(r.rendering=null,r.tail=null,r.lastEffect=null),V(K,K.current),i)break;return null;case 22:case 23:return t.lanes=0,ld(e,t,n)}return ht(e,t,n)}var hd,Da,pd,md;hd=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Da=function(){};pd=function(e,t,n,i){var r=e.memoizedProps;if(r!==i){e=t.stateNode,$t(Je.current);var s=null;switch(n){case"input":r=ta(e,r),i=ta(e,i),s=[];break;case"select":r=Q({},r,{value:void 0}),i=Q({},i,{value:void 0}),s=[];break;case"textarea":r=ra(e,r),i=ra(e,i),s=[];break;default:typeof r.onClick!="function"&&typeof i.onClick=="function"&&(e.onclick=Cr)}aa(n,i);var a;n=null;for(c in r)if(!i.hasOwnProperty(c)&&r.hasOwnProperty(c)&&r[c]!=null)if(c==="style"){var o=r[c];for(a in o)o.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(oi.hasOwnProperty(c)?s||(s=[]):(s=s||[]).push(c,null));for(c in i){var l=i[c];if(o=r!=null?r[c]:void 0,i.hasOwnProperty(c)&&l!==o&&(l!=null||o!=null))if(c==="style")if(o){for(a in o)!o.hasOwnProperty(a)||l&&l.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in l)l.hasOwnProperty(a)&&o[a]!==l[a]&&(n||(n={}),n[a]=l[a])}else n||(s||(s=[]),s.push(c,n)),n=l;else c==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,o=o?o.__html:void 0,l!=null&&o!==l&&(s=s||[]).push(c,l)):c==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(c,""+l):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(oi.hasOwnProperty(c)?(l!=null&&c==="onScroll"&&G("scroll",e),s||o===l||(s=[])):(s=s||[]).push(c,l))}n&&(s=s||[]).push("style",n);var c=s;(t.updateQueue=c)&&(t.flags|=4)}};md=function(e,t,n,i){n!==i&&(t.flags|=4)};function Vn(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function pe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,i=0;if(t)for(var r=e.child;r!==null;)n|=r.lanes|r.childLanes,i|=r.subtreeFlags&14680064,i|=r.flags&14680064,r.return=e,r=r.sibling;else for(r=e.child;r!==null;)n|=r.lanes|r.childLanes,i|=r.subtreeFlags,i|=r.flags,r.return=e,r=r.sibling;return e.subtreeFlags|=i,e.childLanes=n,t}function jp(e,t,n){var i=t.pendingProps;switch(go(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return pe(t),null;case 1:return Ne(t.type)&&Nr(),pe(t),null;case 3:return i=t.stateNode,Fn(),$(Ce),$(ve),Co(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(e===null||e.child===null)&&(Yi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ge!==null&&(Ua(Ge),Ge=null))),Da(e,t),pe(t),null;case 5:Eo(t);var r=$t(xi.current);if(n=t.type,e!==null&&t.stateNode!=null)pd(e,t,n,i,r),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!i){if(t.stateNode===null)throw Error(F(166));return pe(t),null}if(e=$t(Je.current),Yi(t)){i=t.stateNode,n=t.type;var s=t.memoizedProps;switch(i[Ze]=t,i[vi]=s,e=(t.mode&1)!==0,n){case"dialog":G("cancel",i),G("close",i);break;case"iframe":case"object":case"embed":G("load",i);break;case"video":case"audio":for(r=0;r<Zn.length;r++)G(Zn[r],i);break;case"source":G("error",i);break;case"img":case"image":case"link":G("error",i),G("load",i);break;case"details":G("toggle",i);break;case"input":sl(i,s),G("invalid",i);break;case"select":i._wrapperState={wasMultiple:!!s.multiple},G("invalid",i);break;case"textarea":ol(i,s),G("invalid",i)}aa(n,s),r=null;for(var a in s)if(s.hasOwnProperty(a)){var o=s[a];a==="children"?typeof o=="string"?i.textContent!==o&&(s.suppressHydrationWarning!==!0&&Wi(i.textContent,o,e),r=["children",o]):typeof o=="number"&&i.textContent!==""+o&&(s.suppressHydrationWarning!==!0&&Wi(i.textContent,o,e),r=["children",""+o]):oi.hasOwnProperty(a)&&o!=null&&a==="onScroll"&&G("scroll",i)}switch(n){case"input":zi(i),al(i,s,!0);break;case"textarea":zi(i),ll(i);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(i.onclick=Cr)}i=r,t.updateQueue=i,i!==null&&(t.flags|=4)}else{a=r.nodeType===9?r:r.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Vu(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof i.is=="string"?e=a.createElement(n,{is:i.is}):(e=a.createElement(n),n==="select"&&(a=e,i.multiple?a.multiple=!0:i.size&&(a.size=i.size))):e=a.createElementNS(e,n),e[Ze]=t,e[vi]=i,hd(e,t,!1,!1),t.stateNode=e;e:{switch(a=oa(n,i),n){case"dialog":G("cancel",e),G("close",e),r=i;break;case"iframe":case"object":case"embed":G("load",e),r=i;break;case"video":case"audio":for(r=0;r<Zn.length;r++)G(Zn[r],e);r=i;break;case"source":G("error",e),r=i;break;case"img":case"image":case"link":G("error",e),G("load",e),r=i;break;case"details":G("toggle",e),r=i;break;case"input":sl(e,i),r=ta(e,i),G("invalid",e);break;case"option":r=i;break;case"select":e._wrapperState={wasMultiple:!!i.multiple},r=Q({},i,{value:void 0}),G("invalid",e);break;case"textarea":ol(e,i),r=ra(e,i),G("invalid",e);break;default:r=i}aa(n,r),o=r;for(s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="style"?Wu(e,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Gu(e,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&li(e,l):typeof l=="number"&&li(e,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(oi.hasOwnProperty(s)?l!=null&&s==="onScroll"&&G("scroll",e):l!=null&&eo(e,s,l,a))}switch(n){case"input":zi(e),al(e,i,!1);break;case"textarea":zi(e),ll(e);break;case"option":i.value!=null&&e.setAttribute("value",""+Dt(i.value));break;case"select":e.multiple=!!i.multiple,s=i.value,s!=null?yn(e,!!i.multiple,s,!1):i.defaultValue!=null&&yn(e,!!i.multiple,i.defaultValue,!0);break;default:typeof r.onClick=="function"&&(e.onclick=Cr)}switch(n){case"button":case"input":case"select":case"textarea":i=!!i.autoFocus;break e;case"img":i=!0;break e;default:i=!1}}i&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return pe(t),null;case 6:if(e&&t.stateNode!=null)md(e,t,e.memoizedProps,i);else{if(typeof i!="string"&&t.stateNode===null)throw Error(F(166));if(n=$t(xi.current),$t(Je.current),Yi(t)){if(i=t.stateNode,n=t.memoizedProps,i[Ze]=t,(s=i.nodeValue!==n)&&(e=Ie,e!==null))switch(e.tag){case 3:Wi(i.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Wi(i.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else i=(n.nodeType===9?n:n.ownerDocument).createTextNode(i),i[Ze]=t,t.stateNode=i}return pe(t),null;case 13:if($(K),i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&Re!==null&&t.mode&1&&!(t.flags&128))Dc(),Tn(),t.flags|=98560,s=!1;else if(s=Yi(t),i!==null&&i.dehydrated!==null){if(e===null){if(!s)throw Error(F(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(F(317));s[Ze]=t}else Tn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;pe(t),s=!1}else Ge!==null&&(Ua(Ge),Ge=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(i=i!==null,i!==(e!==null&&e.memoizedState!==null)&&i&&(t.child.flags|=8192,t.mode&1&&(e===null||K.current&1?se===0&&(se=3):Ao())),t.updateQueue!==null&&(t.flags|=4),pe(t),null);case 4:return Fn(),Da(e,t),e===null&&mi(t.stateNode.containerInfo),pe(t),null;case 10:return wo(t.type._context),pe(t),null;case 17:return Ne(t.type)&&Nr(),pe(t),null;case 19:if($(K),s=t.memoizedState,s===null)return pe(t),null;if(i=(t.flags&128)!==0,a=s.rendering,a===null)if(i)Vn(s,!1);else{if(se!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=Dr(e),a!==null){for(t.flags|=128,Vn(s,!1),i=a.updateQueue,i!==null&&(t.updateQueue=i,t.flags|=4),t.subtreeFlags=0,i=n,n=t.child;n!==null;)s=n,e=i,s.flags&=14680066,a=s.alternate,a===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=a.childLanes,s.lanes=a.lanes,s.child=a.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=a.memoizedProps,s.memoizedState=a.memoizedState,s.updateQueue=a.updateQueue,s.type=a.type,e=a.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return V(K,K.current&1|2),t.child}e=e.sibling}s.tail!==null&&ee()>In&&(t.flags|=128,i=!0,Vn(s,!1),t.lanes=4194304)}else{if(!i)if(e=Dr(a),e!==null){if(t.flags|=128,i=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Vn(s,!0),s.tail===null&&s.tailMode==="hidden"&&!a.alternate&&!W)return pe(t),null}else 2*ee()-s.renderingStartTime>In&&n!==1073741824&&(t.flags|=128,i=!0,Vn(s,!1),t.lanes=4194304);s.isBackwards?(a.sibling=t.child,t.child=a):(n=s.last,n!==null?n.sibling=a:t.child=a,s.last=a)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=ee(),t.sibling=null,n=K.current,V(K,i?n&1|2:n&1),t):(pe(t),null);case 22:case 23:return Oo(),i=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==i&&(t.flags|=8192),i&&t.mode&1?je&1073741824&&(pe(t),t.subtreeFlags&6&&(t.flags|=8192)):pe(t),null;case 24:return null;case 25:return null}throw Error(F(156,t.tag))}function Fp(e,t){switch(go(t),t.tag){case 1:return Ne(t.type)&&Nr(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Fn(),$(Ce),$(ve),Co(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Eo(t),null;case 13:if($(K),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(F(340));Tn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return $(K),null;case 4:return Fn(),null;case 10:return wo(t.type._context),null;case 22:case 23:return Oo(),null;case 24:return null;default:return null}}var Zi=!1,ge=!1,Rp=typeof WeakSet=="function"?WeakSet:Set,D=null;function mn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(i){J(e,t,i)}else n.current=null}function La(e,t,n){try{n()}catch(i){J(e,t,i)}}var Ql=!1;function Ip(e,t){if(va=br,e=wc(),po(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var r=i.anchorOffset,s=i.focusNode;i=i.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var a=0,o=-1,l=-1,c=0,d=0,h=e,g=null;t:for(;;){for(var y;h!==n||r!==0&&h.nodeType!==3||(o=a+r),h!==s||i!==0&&h.nodeType!==3||(l=a+i),h.nodeType===3&&(a+=h.nodeValue.length),(y=h.firstChild)!==null;)g=h,h=y;for(;;){if(h===e)break t;if(g===n&&++c===r&&(o=a),g===s&&++d===i&&(l=a),(y=h.nextSibling)!==null)break;h=g,g=h.parentNode}h=y}n=o===-1||l===-1?null:{start:o,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(ya={focusedElem:e,selectionRange:n},br=!1,D=t;D!==null;)if(t=D,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,D=e;else for(;D!==null;){t=D;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,S=v.memoizedState,p=t.stateNode,f=p.getSnapshotBeforeUpdate(t.elementType===t.type?x:Be(t.type,x),S);p.__reactInternalSnapshotBeforeUpdate=f}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(F(163))}}catch(w){J(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,D=e;break}D=t.return}return v=Ql,Ql=!1,v}function ii(e,t,n){var i=t.updateQueue;if(i=i!==null?i.lastEffect:null,i!==null){var r=i=i.next;do{if((r.tag&e)===e){var s=r.destroy;r.destroy=void 0,s!==void 0&&La(t,n,s)}r=r.next}while(r!==i)}}function is(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var i=n.create;n.destroy=i()}n=n.next}while(n!==t)}}function Ma(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function gd(e){var t=e.alternate;t!==null&&(e.alternate=null,gd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ze],delete t[vi],delete t[ka],delete t[hp],delete t[pp])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function vd(e){return e.tag===5||e.tag===3||e.tag===4}function Xl(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||vd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Oa(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Cr));else if(i!==4&&(e=e.child,e!==null))for(Oa(e,t,n),e=e.sibling;e!==null;)Oa(e,t,n),e=e.sibling}function Aa(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(i!==4&&(e=e.child,e!==null))for(Aa(e,t,n),e=e.sibling;e!==null;)Aa(e,t,n),e=e.sibling}var ue=null,Ve=!1;function gt(e,t,n){for(n=n.child;n!==null;)yd(e,t,n),n=n.sibling}function yd(e,t,n){if(Xe&&typeof Xe.onCommitFiberUnmount=="function")try{Xe.onCommitFiberUnmount(qr,n)}catch{}switch(n.tag){case 5:ge||mn(n,t);case 6:var i=ue,r=Ve;ue=null,gt(e,t,n),ue=i,Ve=r,ue!==null&&(Ve?(e=ue,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ue.removeChild(n.stateNode));break;case 18:ue!==null&&(Ve?(e=ue,n=n.stateNode,e.nodeType===8?Fs(e.parentNode,n):e.nodeType===1&&Fs(e,n),fi(e)):Fs(ue,n.stateNode));break;case 4:i=ue,r=Ve,ue=n.stateNode.containerInfo,Ve=!0,gt(e,t,n),ue=i,Ve=r;break;case 0:case 11:case 14:case 15:if(!ge&&(i=n.updateQueue,i!==null&&(i=i.lastEffect,i!==null))){r=i=i.next;do{var s=r,a=s.destroy;s=s.tag,a!==void 0&&(s&2||s&4)&&La(n,t,a),r=r.next}while(r!==i)}gt(e,t,n);break;case 1:if(!ge&&(mn(n,t),i=n.stateNode,typeof i.componentWillUnmount=="function"))try{i.props=n.memoizedProps,i.state=n.memoizedState,i.componentWillUnmount()}catch(o){J(n,t,o)}gt(e,t,n);break;case 21:gt(e,t,n);break;case 22:n.mode&1?(ge=(i=ge)||n.memoizedState!==null,gt(e,t,n),ge=i):gt(e,t,n);break;default:gt(e,t,n)}}function Jl(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Rp),t.forEach(function(i){var r=Hp.bind(null,e,i);n.has(i)||(n.add(i),i.then(r,r))})}}function Ue(e,t){var n=t.deletions;if(n!==null)for(var i=0;i<n.length;i++){var r=n[i];try{var s=e,a=t,o=a;e:for(;o!==null;){switch(o.tag){case 5:ue=o.stateNode,Ve=!1;break e;case 3:ue=o.stateNode.containerInfo,Ve=!0;break e;case 4:ue=o.stateNode.containerInfo,Ve=!0;break e}o=o.return}if(ue===null)throw Error(F(160));yd(s,a,r),ue=null,Ve=!1;var l=r.alternate;l!==null&&(l.return=null),r.return=null}catch(c){J(r,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)xd(t,e),t=t.sibling}function xd(e,t){var n=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ue(t,e),Ke(e),i&4){try{ii(3,e,e.return),is(3,e)}catch(x){J(e,e.return,x)}try{ii(5,e,e.return)}catch(x){J(e,e.return,x)}}break;case 1:Ue(t,e),Ke(e),i&512&&n!==null&&mn(n,n.return);break;case 5:if(Ue(t,e),Ke(e),i&512&&n!==null&&mn(n,n.return),e.flags&32){var r=e.stateNode;try{li(r,"")}catch(x){J(e,e.return,x)}}if(i&4&&(r=e.stateNode,r!=null)){var s=e.memoizedProps,a=n!==null?n.memoizedProps:s,o=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{o==="input"&&s.type==="radio"&&s.name!=null&&Uu(r,s),oa(o,a);var c=oa(o,s);for(a=0;a<l.length;a+=2){var d=l[a],h=l[a+1];d==="style"?Wu(r,h):d==="dangerouslySetInnerHTML"?Gu(r,h):d==="children"?li(r,h):eo(r,d,h,c)}switch(o){case"input":na(r,s);break;case"textarea":Bu(r,s);break;case"select":var g=r._wrapperState.wasMultiple;r._wrapperState.wasMultiple=!!s.multiple;var y=s.value;y!=null?yn(r,!!s.multiple,y,!1):g!==!!s.multiple&&(s.defaultValue!=null?yn(r,!!s.multiple,s.defaultValue,!0):yn(r,!!s.multiple,s.multiple?[]:"",!1))}r[vi]=s}catch(x){J(e,e.return,x)}}break;case 6:if(Ue(t,e),Ke(e),i&4){if(e.stateNode===null)throw Error(F(162));r=e.stateNode,s=e.memoizedProps;try{r.nodeValue=s}catch(x){J(e,e.return,x)}}break;case 3:if(Ue(t,e),Ke(e),i&4&&n!==null&&n.memoizedState.isDehydrated)try{fi(t.containerInfo)}catch(x){J(e,e.return,x)}break;case 4:Ue(t,e),Ke(e);break;case 13:Ue(t,e),Ke(e),r=e.child,r.flags&8192&&(s=r.memoizedState!==null,r.stateNode.isHidden=s,!s||r.alternate!==null&&r.alternate.memoizedState!==null||(Lo=ee())),i&4&&Jl(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(ge=(c=ge)||d,Ue(t,e),ge=c):Ue(t,e),Ke(e),i&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!d&&e.mode&1)for(D=e,d=e.child;d!==null;){for(h=D=d;D!==null;){switch(g=D,y=g.child,g.tag){case 0:case 11:case 14:case 15:ii(4,g,g.return);break;case 1:mn(g,g.return);var v=g.stateNode;if(typeof v.componentWillUnmount=="function"){i=g,n=g.return;try{t=i,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){J(i,n,x)}}break;case 5:mn(g,g.return);break;case 22:if(g.memoizedState!==null){tu(h);continue}}y!==null?(y.return=g,D=y):tu(h)}d=d.sibling}e:for(d=null,h=e;;){if(h.tag===5){if(d===null){d=h;try{r=h.stateNode,c?(s=r.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(o=h.stateNode,l=h.memoizedProps.style,a=l!=null&&l.hasOwnProperty("display")?l.display:null,o.style.display=$u("display",a))}catch(x){J(e,e.return,x)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=c?"":h.memoizedProps}catch(x){J(e,e.return,x)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:Ue(t,e),Ke(e),i&4&&Jl(e);break;case 21:break;default:Ue(t,e),Ke(e)}}function Ke(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(vd(n)){var i=n;break e}n=n.return}throw Error(F(160))}switch(i.tag){case 5:var r=i.stateNode;i.flags&32&&(li(r,""),i.flags&=-33);var s=Xl(e);Aa(e,s,r);break;case 3:case 4:var a=i.stateNode.containerInfo,o=Xl(e);Oa(e,o,a);break;default:throw Error(F(161))}}catch(l){J(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Pp(e,t,n){D=e,wd(e)}function wd(e,t,n){for(var i=(e.mode&1)!==0;D!==null;){var r=D,s=r.child;if(r.tag===22&&i){var a=r.memoizedState!==null||Zi;if(!a){var o=r.alternate,l=o!==null&&o.memoizedState!==null||ge;o=Zi;var c=ge;if(Zi=a,(ge=l)&&!c)for(D=r;D!==null;)a=D,l=a.child,a.tag===22&&a.memoizedState!==null?nu(r):l!==null?(l.return=a,D=l):nu(r);for(;s!==null;)D=s,wd(s),s=s.sibling;D=r,Zi=o,ge=c}eu(e)}else r.subtreeFlags&8772&&s!==null?(s.return=r,D=s):eu(e)}}function eu(e){for(;D!==null;){var t=D;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ge||is(5,t);break;case 1:var i=t.stateNode;if(t.flags&4&&!ge)if(n===null)i.componentDidMount();else{var r=t.elementType===t.type?n.memoizedProps:Be(t.type,n.memoizedProps);i.componentDidUpdate(r,n.memoizedState,i.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&zl(t,s,i);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}zl(t,a,n)}break;case 5:var o=t.stateNode;if(n===null&&t.flags&4){n=o;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var d=c.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&fi(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(F(163))}ge||t.flags&512&&Ma(t)}catch(g){J(t,t.return,g)}}if(t===e){D=null;break}if(n=t.sibling,n!==null){n.return=t.return,D=n;break}D=t.return}}function tu(e){for(;D!==null;){var t=D;if(t===e){D=null;break}var n=t.sibling;if(n!==null){n.return=t.return,D=n;break}D=t.return}}function nu(e){for(;D!==null;){var t=D;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{is(4,t)}catch(l){J(t,n,l)}break;case 1:var i=t.stateNode;if(typeof i.componentDidMount=="function"){var r=t.return;try{i.componentDidMount()}catch(l){J(t,r,l)}}var s=t.return;try{Ma(t)}catch(l){J(t,s,l)}break;case 5:var a=t.return;try{Ma(t)}catch(l){J(t,a,l)}}}catch(l){J(t,t.return,l)}if(t===e){D=null;break}var o=t.sibling;if(o!==null){o.return=t.return,D=o;break}D=t.return}}var Dp=Math.ceil,Or=mt.ReactCurrentDispatcher,Po=mt.ReactCurrentOwner,ze=mt.ReactCurrentBatchConfig,_=0,le=null,ne=null,de=0,je=0,gn=Ot(0),se=0,Si=null,Qt=0,rs=0,Do=0,ri=null,Se=null,Lo=0,In=1/0,rt=null,Ar=!1,za=null,Rt=null,Qi=!1,St=null,zr=0,si=0,_a=null,dr=-1,fr=0;function xe(){return _&6?ee():dr!==-1?dr:dr=ee()}function It(e){return e.mode&1?_&2&&de!==0?de&-de:gp.transition!==null?(fr===0&&(fr=rc()),fr):(e=B,e!==0||(e=window.event,e=e===void 0?16:dc(e.type)),e):1}function We(e,t,n,i){if(50<si)throw si=0,_a=null,Error(F(185));Ni(e,n,i),(!(_&2)||e!==le)&&(e===le&&(!(_&2)&&(rs|=n),se===4&&kt(e,de)),Te(e,i),n===1&&_===0&&!(t.mode&1)&&(In=ee()+500,es&&At()))}function Te(e,t){var n=e.callbackNode;gh(e,t);var i=kr(e,e===le?de:0);if(i===0)n!==null&&dl(n),e.callbackNode=null,e.callbackPriority=0;else if(t=i&-i,e.callbackPriority!==t){if(n!=null&&dl(n),t===1)e.tag===0?mp(iu.bind(null,e)):Rc(iu.bind(null,e)),dp(function(){!(_&6)&&At()}),n=null;else{switch(sc(i)){case 1:n=so;break;case 4:n=nc;break;case 16:n=wr;break;case 536870912:n=ic;break;default:n=wr}n=jd(n,kd.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function kd(e,t){if(dr=-1,fr=0,_&6)throw Error(F(327));var n=e.callbackNode;if(Sn()&&e.callbackNode!==n)return null;var i=kr(e,e===le?de:0);if(i===0)return null;if(i&30||i&e.expiredLanes||t)t=_r(e,i);else{t=i;var r=_;_|=2;var s=Sd();(le!==e||de!==t)&&(rt=null,In=ee()+500,Wt(e,t));do try{Op();break}catch(o){bd(e,o)}while(!0);xo(),Or.current=s,_=r,ne!==null?t=0:(le=null,de=0,t=se)}if(t!==0){if(t===2&&(r=fa(e),r!==0&&(i=r,t=Ha(e,r))),t===1)throw n=Si,Wt(e,0),kt(e,i),Te(e,ee()),n;if(t===6)kt(e,i);else{if(r=e.current.alternate,!(i&30)&&!Lp(r)&&(t=_r(e,i),t===2&&(s=fa(e),s!==0&&(i=s,t=Ha(e,s))),t===1))throw n=Si,Wt(e,0),kt(e,i),Te(e,ee()),n;switch(e.finishedWork=r,e.finishedLanes=i,t){case 0:case 1:throw Error(F(345));case 2:Bt(e,Se,rt);break;case 3:if(kt(e,i),(i&130023424)===i&&(t=Lo+500-ee(),10<t)){if(kr(e,0)!==0)break;if(r=e.suspendedLanes,(r&i)!==i){xe(),e.pingedLanes|=e.suspendedLanes&r;break}e.timeoutHandle=wa(Bt.bind(null,e,Se,rt),t);break}Bt(e,Se,rt);break;case 4:if(kt(e,i),(i&4194240)===i)break;for(t=e.eventTimes,r=-1;0<i;){var a=31-$e(i);s=1<<a,a=t[a],a>r&&(r=a),i&=~s}if(i=r,i=ee()-i,i=(120>i?120:480>i?480:1080>i?1080:1920>i?1920:3e3>i?3e3:4320>i?4320:1960*Dp(i/1960))-i,10<i){e.timeoutHandle=wa(Bt.bind(null,e,Se,rt),i);break}Bt(e,Se,rt);break;case 5:Bt(e,Se,rt);break;default:throw Error(F(329))}}}return Te(e,ee()),e.callbackNode===n?kd.bind(null,e):null}function Ha(e,t){var n=ri;return e.current.memoizedState.isDehydrated&&(Wt(e,t).flags|=256),e=_r(e,t),e!==2&&(t=Se,Se=n,t!==null&&Ua(t)),e}function Ua(e){Se===null?Se=e:Se.push.apply(Se,e)}function Lp(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var i=0;i<n.length;i++){var r=n[i],s=r.getSnapshot;r=r.value;try{if(!Ye(s(),r))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function kt(e,t){for(t&=~Do,t&=~rs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-$e(t),i=1<<n;e[n]=-1,t&=~i}}function iu(e){if(_&6)throw Error(F(327));Sn();var t=kr(e,0);if(!(t&1))return Te(e,ee()),null;var n=_r(e,t);if(e.tag!==0&&n===2){var i=fa(e);i!==0&&(t=i,n=Ha(e,i))}if(n===1)throw n=Si,Wt(e,0),kt(e,t),Te(e,ee()),n;if(n===6)throw Error(F(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Bt(e,Se,rt),Te(e,ee()),null}function Mo(e,t){var n=_;_|=1;try{return e(t)}finally{_=n,_===0&&(In=ee()+500,es&&At())}}function Xt(e){St!==null&&St.tag===0&&!(_&6)&&Sn();var t=_;_|=1;var n=ze.transition,i=B;try{if(ze.transition=null,B=1,e)return e()}finally{B=i,ze.transition=n,_=t,!(_&6)&&At()}}function Oo(){je=gn.current,$(gn)}function Wt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,cp(n)),ne!==null)for(n=ne.return;n!==null;){var i=n;switch(go(i),i.tag){case 1:i=i.type.childContextTypes,i!=null&&Nr();break;case 3:Fn(),$(Ce),$(ve),Co();break;case 5:Eo(i);break;case 4:Fn();break;case 13:$(K);break;case 19:$(K);break;case 10:wo(i.type._context);break;case 22:case 23:Oo()}n=n.return}if(le=e,ne=e=Pt(e.current,null),de=je=t,se=0,Si=null,Do=rs=Qt=0,Se=ri=null,Gt!==null){for(t=0;t<Gt.length;t++)if(n=Gt[t],i=n.interleaved,i!==null){n.interleaved=null;var r=i.next,s=n.pending;if(s!==null){var a=s.next;s.next=r,i.next=a}n.pending=i}Gt=null}return e}function bd(e,t){do{var n=ne;try{if(xo(),lr.current=Mr,Lr){for(var i=q.memoizedState;i!==null;){var r=i.queue;r!==null&&(r.pending=null),i=i.next}Lr=!1}if(Zt=0,oe=re=q=null,ni=!1,wi=0,Po.current=null,n===null||n.return===null){se=1,Si=t,ne=null;break}e:{var s=e,a=n.return,o=n,l=t;if(t=de,o.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var c=l,d=o,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var g=d.alternate;g?(d.updateQueue=g.updateQueue,d.memoizedState=g.memoizedState,d.lanes=g.lanes):(d.updateQueue=null,d.memoizedState=null)}var y=Gl(a);if(y!==null){y.flags&=-257,$l(y,a,o,s,t),y.mode&1&&Vl(s,c,t),t=y,l=c;var v=t.updateQueue;if(v===null){var x=new Set;x.add(l),t.updateQueue=x}else v.add(l);break e}else{if(!(t&1)){Vl(s,c,t),Ao();break e}l=Error(F(426))}}else if(W&&o.mode&1){var S=Gl(a);if(S!==null){!(S.flags&65536)&&(S.flags|=256),$l(S,a,o,s,t),vo(Rn(l,o));break e}}s=l=Rn(l,o),se!==4&&(se=2),ri===null?ri=[s]:ri.push(s),s=a;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var p=sd(s,l,t);Al(s,p);break e;case 1:o=l;var f=s.type,m=s.stateNode;if(!(s.flags&128)&&(typeof f.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Rt===null||!Rt.has(m)))){s.flags|=65536,t&=-t,s.lanes|=t;var w=ad(s,o,t);Al(s,w);break e}}s=s.return}while(s!==null)}Cd(n)}catch(b){t=b,ne===n&&n!==null&&(ne=n=n.return);continue}break}while(!0)}function Sd(){var e=Or.current;return Or.current=Mr,e===null?Mr:e}function Ao(){(se===0||se===3||se===2)&&(se=4),le===null||!(Qt&268435455)&&!(rs&268435455)||kt(le,de)}function _r(e,t){var n=_;_|=2;var i=Sd();(le!==e||de!==t)&&(rt=null,Wt(e,t));do try{Mp();break}catch(r){bd(e,r)}while(!0);if(xo(),_=n,Or.current=i,ne!==null)throw Error(F(261));return le=null,de=0,se}function Mp(){for(;ne!==null;)Ed(ne)}function Op(){for(;ne!==null&&!oh();)Ed(ne)}function Ed(e){var t=Td(e.alternate,e,je);e.memoizedProps=e.pendingProps,t===null?Cd(e):ne=t,Po.current=null}function Cd(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Fp(n,t),n!==null){n.flags&=32767,ne=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{se=6,ne=null;return}}else if(n=jp(n,t,je),n!==null){ne=n;return}if(t=t.sibling,t!==null){ne=t;return}ne=t=e}while(t!==null);se===0&&(se=5)}function Bt(e,t,n){var i=B,r=ze.transition;try{ze.transition=null,B=1,Ap(e,t,n,i)}finally{ze.transition=r,B=i}return null}function Ap(e,t,n,i){do Sn();while(St!==null);if(_&6)throw Error(F(327));n=e.finishedWork;var r=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(F(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(vh(e,s),e===le&&(ne=le=null,de=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Qi||(Qi=!0,jd(wr,function(){return Sn(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=ze.transition,ze.transition=null;var a=B;B=1;var o=_;_|=4,Po.current=null,Ip(e,n),xd(n,e),ip(ya),br=!!va,ya=va=null,e.current=n,Pp(n),lh(),_=o,B=a,ze.transition=s}else e.current=n;if(Qi&&(Qi=!1,St=e,zr=r),s=e.pendingLanes,s===0&&(Rt=null),dh(n.stateNode),Te(e,ee()),t!==null)for(i=e.onRecoverableError,n=0;n<t.length;n++)r=t[n],i(r.value,{componentStack:r.stack,digest:r.digest});if(Ar)throw Ar=!1,e=za,za=null,e;return zr&1&&e.tag!==0&&Sn(),s=e.pendingLanes,s&1?e===_a?si++:(si=0,_a=e):si=0,At(),null}function Sn(){if(St!==null){var e=sc(zr),t=ze.transition,n=B;try{if(ze.transition=null,B=16>e?16:e,St===null)var i=!1;else{if(e=St,St=null,zr=0,_&6)throw Error(F(331));var r=_;for(_|=4,D=e.current;D!==null;){var s=D,a=s.child;if(D.flags&16){var o=s.deletions;if(o!==null){for(var l=0;l<o.length;l++){var c=o[l];for(D=c;D!==null;){var d=D;switch(d.tag){case 0:case 11:case 15:ii(8,d,s)}var h=d.child;if(h!==null)h.return=d,D=h;else for(;D!==null;){d=D;var g=d.sibling,y=d.return;if(gd(d),d===c){D=null;break}if(g!==null){g.return=y,D=g;break}D=y}}}var v=s.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var S=x.sibling;x.sibling=null,x=S}while(x!==null)}}D=s}}if(s.subtreeFlags&2064&&a!==null)a.return=s,D=a;else e:for(;D!==null;){if(s=D,s.flags&2048)switch(s.tag){case 0:case 11:case 15:ii(9,s,s.return)}var p=s.sibling;if(p!==null){p.return=s.return,D=p;break e}D=s.return}}var f=e.current;for(D=f;D!==null;){a=D;var m=a.child;if(a.subtreeFlags&2064&&m!==null)m.return=a,D=m;else e:for(a=f;D!==null;){if(o=D,o.flags&2048)try{switch(o.tag){case 0:case 11:case 15:is(9,o)}}catch(b){J(o,o.return,b)}if(o===a){D=null;break e}var w=o.sibling;if(w!==null){w.return=o.return,D=w;break e}D=o.return}}if(_=r,At(),Xe&&typeof Xe.onPostCommitFiberRoot=="function")try{Xe.onPostCommitFiberRoot(qr,e)}catch{}i=!0}return i}finally{B=n,ze.transition=t}}return!1}function ru(e,t,n){t=Rn(n,t),t=sd(e,t,1),e=Ft(e,t,1),t=xe(),e!==null&&(Ni(e,1,t),Te(e,t))}function J(e,t,n){if(e.tag===3)ru(e,e,n);else for(;t!==null;){if(t.tag===3){ru(t,e,n);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Rt===null||!Rt.has(i))){e=Rn(n,e),e=ad(t,e,1),t=Ft(t,e,1),e=xe(),t!==null&&(Ni(t,1,e),Te(t,e));break}}t=t.return}}function zp(e,t,n){var i=e.pingCache;i!==null&&i.delete(t),t=xe(),e.pingedLanes|=e.suspendedLanes&n,le===e&&(de&n)===n&&(se===4||se===3&&(de&130023424)===de&&500>ee()-Lo?Wt(e,0):Do|=n),Te(e,t)}function Nd(e,t){t===0&&(e.mode&1?(t=Ui,Ui<<=1,!(Ui&130023424)&&(Ui=4194304)):t=1);var n=xe();e=ft(e,t),e!==null&&(Ni(e,t,n),Te(e,n))}function _p(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Nd(e,n)}function Hp(e,t){var n=0;switch(e.tag){case 13:var i=e.stateNode,r=e.memoizedState;r!==null&&(n=r.retryLane);break;case 19:i=e.stateNode;break;default:throw Error(F(314))}i!==null&&i.delete(t),Nd(e,n)}var Td;Td=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ce.current)Ee=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ee=!1,Tp(e,t,n);Ee=!!(e.flags&131072)}else Ee=!1,W&&t.flags&1048576&&Ic(t,Fr,t.index);switch(t.lanes=0,t.tag){case 2:var i=t.type;cr(e,t),e=t.pendingProps;var r=Nn(t,ve.current);bn(t,n),r=To(null,t,i,e,r,n);var s=jo();return t.flags|=1,typeof r=="object"&&r!==null&&typeof r.render=="function"&&r.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ne(i)?(s=!0,Tr(t)):s=!1,t.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,bo(t),r.updater=ns,t.stateNode=r,r._reactInternals=t,Ta(t,i,e,n),t=Ra(null,t,i,!0,s,n)):(t.tag=0,W&&s&&mo(t),ye(null,t,r,n),t=t.child),t;case 16:i=t.elementType;e:{switch(cr(e,t),e=t.pendingProps,r=i._init,i=r(i._payload),t.type=i,r=t.tag=Bp(i),e=Be(i,e),r){case 0:t=Fa(null,t,i,e,n);break e;case 1:t=Kl(null,t,i,e,n);break e;case 11:t=Wl(null,t,i,e,n);break e;case 14:t=Yl(null,t,i,Be(i.type,e),n);break e}throw Error(F(306,i,""))}return t;case 0:return i=t.type,r=t.pendingProps,r=t.elementType===i?r:Be(i,r),Fa(e,t,i,r,n);case 1:return i=t.type,r=t.pendingProps,r=t.elementType===i?r:Be(i,r),Kl(e,t,i,r,n);case 3:e:{if(cd(t),e===null)throw Error(F(387));i=t.pendingProps,s=t.memoizedState,r=s.element,Ac(e,t),Pr(t,i,null,n);var a=t.memoizedState;if(i=a.element,s.isDehydrated)if(s={element:i,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){r=Rn(Error(F(423)),t),t=ql(e,t,i,n,r);break e}else if(i!==r){r=Rn(Error(F(424)),t),t=ql(e,t,i,n,r);break e}else for(Re=jt(t.stateNode.containerInfo.firstChild),Ie=t,W=!0,Ge=null,n=Mc(t,null,i,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Tn(),i===r){t=ht(e,t,n);break e}ye(e,t,i,n)}t=t.child}return t;case 5:return zc(t),e===null&&Ea(t),i=t.type,r=t.pendingProps,s=e!==null?e.memoizedProps:null,a=r.children,xa(i,r)?a=null:s!==null&&xa(i,s)&&(t.flags|=32),ud(e,t),ye(e,t,a,n),t.child;case 6:return e===null&&Ea(t),null;case 13:return dd(e,t,n);case 4:return So(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=jn(t,null,i,n):ye(e,t,i,n),t.child;case 11:return i=t.type,r=t.pendingProps,r=t.elementType===i?r:Be(i,r),Wl(e,t,i,r,n);case 7:return ye(e,t,t.pendingProps,n),t.child;case 8:return ye(e,t,t.pendingProps.children,n),t.child;case 12:return ye(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(i=t.type._context,r=t.pendingProps,s=t.memoizedProps,a=r.value,V(Rr,i._currentValue),i._currentValue=a,s!==null)if(Ye(s.value,a)){if(s.children===r.children&&!Ce.current){t=ht(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var o=s.dependencies;if(o!==null){a=s.child;for(var l=o.firstContext;l!==null;){if(l.context===i){if(s.tag===1){l=lt(-1,n&-n),l.tag=2;var c=s.updateQueue;if(c!==null){c=c.shared;var d=c.pending;d===null?l.next=l:(l.next=d.next,d.next=l),c.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Ca(s.return,n,t),o.lanes|=n;break}l=l.next}}else if(s.tag===10)a=s.type===t.type?null:s.child;else if(s.tag===18){if(a=s.return,a===null)throw Error(F(341));a.lanes|=n,o=a.alternate,o!==null&&(o.lanes|=n),Ca(a,n,t),a=s.sibling}else a=s.child;if(a!==null)a.return=s;else for(a=s;a!==null;){if(a===t){a=null;break}if(s=a.sibling,s!==null){s.return=a.return,a=s;break}a=a.return}s=a}ye(e,t,r.children,n),t=t.child}return t;case 9:return r=t.type,i=t.pendingProps.children,bn(t,n),r=_e(r),i=i(r),t.flags|=1,ye(e,t,i,n),t.child;case 14:return i=t.type,r=Be(i,t.pendingProps),r=Be(i.type,r),Yl(e,t,i,r,n);case 15:return od(e,t,t.type,t.pendingProps,n);case 17:return i=t.type,r=t.pendingProps,r=t.elementType===i?r:Be(i,r),cr(e,t),t.tag=1,Ne(i)?(e=!0,Tr(t)):e=!1,bn(t,n),rd(t,i,r),Ta(t,i,r,n),Ra(null,t,i,!0,e,n);case 19:return fd(e,t,n);case 22:return ld(e,t,n)}throw Error(F(156,t.tag))};function jd(e,t){return tc(e,t)}function Up(e,t,n,i){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ae(e,t,n,i){return new Up(e,t,n,i)}function zo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Bp(e){if(typeof e=="function")return zo(e)?1:0;if(e!=null){if(e=e.$$typeof,e===no)return 11;if(e===io)return 14}return 2}function Pt(e,t){var n=e.alternate;return n===null?(n=Ae(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function hr(e,t,n,i,r,s){var a=2;if(i=e,typeof e=="function")zo(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case an:return Yt(n.children,r,s,t);case to:a=8,r|=8;break;case Qs:return e=Ae(12,n,t,r|2),e.elementType=Qs,e.lanes=s,e;case Xs:return e=Ae(13,n,t,r),e.elementType=Xs,e.lanes=s,e;case Js:return e=Ae(19,n,t,r),e.elementType=Js,e.lanes=s,e;case zu:return ss(n,r,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Ou:a=10;break e;case Au:a=9;break e;case no:a=11;break e;case io:a=14;break e;case yt:a=16,i=null;break e}throw Error(F(130,e==null?e:typeof e,""))}return t=Ae(a,n,t,r),t.elementType=e,t.type=i,t.lanes=s,t}function Yt(e,t,n,i){return e=Ae(7,e,i,t),e.lanes=n,e}function ss(e,t,n,i){return e=Ae(22,e,i,t),e.elementType=zu,e.lanes=n,e.stateNode={isHidden:!1},e}function As(e,t,n){return e=Ae(6,e,null,t),e.lanes=n,e}function zs(e,t,n){return t=Ae(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Vp(e,t,n,i,r){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ys(0),this.expirationTimes=ys(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ys(0),this.identifierPrefix=i,this.onRecoverableError=r,this.mutableSourceEagerHydrationData=null}function _o(e,t,n,i,r,s,a,o,l){return e=new Vp(e,t,n,o,l),t===1?(t=1,s===!0&&(t|=8)):t=0,s=Ae(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:i,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},bo(s),e}function Gp(e,t,n){var i=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:sn,key:i==null?null:""+i,children:e,containerInfo:t,implementation:n}}function Fd(e){if(!e)return Lt;e=e._reactInternals;e:{if(tn(e)!==e||e.tag!==1)throw Error(F(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ne(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(F(171))}if(e.tag===1){var n=e.type;if(Ne(n))return Fc(e,n,t)}return t}function Rd(e,t,n,i,r,s,a,o,l){return e=_o(n,i,!0,e,r,s,a,o,l),e.context=Fd(null),n=e.current,i=xe(),r=It(n),s=lt(i,r),s.callback=t??null,Ft(n,s,r),e.current.lanes=r,Ni(e,r,i),Te(e,i),e}function as(e,t,n,i){var r=t.current,s=xe(),a=It(r);return n=Fd(n),t.context===null?t.context=n:t.pendingContext=n,t=lt(s,a),t.payload={element:e},i=i===void 0?null:i,i!==null&&(t.callback=i),e=Ft(r,t,a),e!==null&&(We(e,r,a,s),or(e,r,a)),a}function Hr(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function su(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Ho(e,t){su(e,t),(e=e.alternate)&&su(e,t)}function $p(){return null}var Id=typeof reportError=="function"?reportError:function(e){console.error(e)};function Uo(e){this._internalRoot=e}os.prototype.render=Uo.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(F(409));as(e,t,null,null)};os.prototype.unmount=Uo.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Xt(function(){as(null,e,null,null)}),t[dt]=null}};function os(e){this._internalRoot=e}os.prototype.unstable_scheduleHydration=function(e){if(e){var t=lc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<wt.length&&t!==0&&t<wt[n].priority;n++);wt.splice(n,0,e),n===0&&cc(e)}};function Bo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ls(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function au(){}function Wp(e,t,n,i,r){if(r){if(typeof i=="function"){var s=i;i=function(){var c=Hr(a);s.call(c)}}var a=Rd(t,i,e,0,null,!1,!1,"",au);return e._reactRootContainer=a,e[dt]=a.current,mi(e.nodeType===8?e.parentNode:e),Xt(),a}for(;r=e.lastChild;)e.removeChild(r);if(typeof i=="function"){var o=i;i=function(){var c=Hr(l);o.call(c)}}var l=_o(e,0,!1,null,null,!1,!1,"",au);return e._reactRootContainer=l,e[dt]=l.current,mi(e.nodeType===8?e.parentNode:e),Xt(function(){as(t,l,n,i)}),l}function us(e,t,n,i,r){var s=n._reactRootContainer;if(s){var a=s;if(typeof r=="function"){var o=r;r=function(){var l=Hr(a);o.call(l)}}as(t,a,e,r)}else a=Wp(n,t,e,r,i);return Hr(a)}ac=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=qn(t.pendingLanes);n!==0&&(ao(t,n|1),Te(t,ee()),!(_&6)&&(In=ee()+500,At()))}break;case 13:Xt(function(){var i=ft(e,1);if(i!==null){var r=xe();We(i,e,1,r)}}),Ho(e,1)}};oo=function(e){if(e.tag===13){var t=ft(e,134217728);if(t!==null){var n=xe();We(t,e,134217728,n)}Ho(e,134217728)}};oc=function(e){if(e.tag===13){var t=It(e),n=ft(e,t);if(n!==null){var i=xe();We(n,e,t,i)}Ho(e,t)}};lc=function(){return B};uc=function(e,t){var n=B;try{return B=e,t()}finally{B=n}};ua=function(e,t,n){switch(t){case"input":if(na(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var i=n[t];if(i!==e&&i.form===e.form){var r=Jr(i);if(!r)throw Error(F(90));Hu(i),na(i,r)}}}break;case"textarea":Bu(e,n);break;case"select":t=n.value,t!=null&&yn(e,!!n.multiple,t,!1)}};qu=Mo;Zu=Xt;var Yp={usingClientEntryPoint:!1,Events:[ji,cn,Jr,Yu,Ku,Mo]},Gn={findFiberByHostInstance:Vt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Kp={bundleType:Gn.bundleType,version:Gn.version,rendererPackageName:Gn.rendererPackageName,rendererConfig:Gn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:mt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Ju(e),e===null?null:e.stateNode},findFiberByHostInstance:Gn.findFiberByHostInstance||$p,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Xi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Xi.isDisabled&&Xi.supportsFiber)try{qr=Xi.inject(Kp),Xe=Xi}catch{}}De.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Yp;De.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Bo(t))throw Error(F(200));return Gp(e,t,null,n)};De.createRoot=function(e,t){if(!Bo(e))throw Error(F(299));var n=!1,i="",r=Id;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onRecoverableError!==void 0&&(r=t.onRecoverableError)),t=_o(e,1,!1,null,null,n,!1,i,r),e[dt]=t.current,mi(e.nodeType===8?e.parentNode:e),new Uo(t)};De.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(F(188)):(e=Object.keys(e).join(","),Error(F(268,e)));return e=Ju(t),e=e===null?null:e.stateNode,e};De.flushSync=function(e){return Xt(e)};De.hydrate=function(e,t,n){if(!ls(t))throw Error(F(200));return us(null,e,t,!0,n)};De.hydrateRoot=function(e,t,n){if(!Bo(e))throw Error(F(405));var i=n!=null&&n.hydratedSources||null,r=!1,s="",a=Id;if(n!=null&&(n.unstable_strictMode===!0&&(r=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=Rd(t,null,e,1,n??null,r,!1,s,a),e[dt]=t.current,mi(e),i)for(e=0;e<i.length;e++)n=i[e],r=n._getVersion,r=r(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,r]:t.mutableSourceEagerHydrationData.push(n,r);return new os(t)};De.render=function(e,t,n){if(!ls(t))throw Error(F(200));return us(null,e,t,!1,n)};De.unmountComponentAtNode=function(e){if(!ls(e))throw Error(F(40));return e._reactRootContainer?(Xt(function(){us(null,null,e,!1,function(){e._reactRootContainer=null,e[dt]=null})}),!0):!1};De.unstable_batchedUpdates=Mo;De.unstable_renderSubtreeIntoContainer=function(e,t,n,i){if(!ls(n))throw Error(F(200));if(e==null||e._reactInternals===void 0)throw Error(F(38));return us(e,t,n,!1,i)};De.version="18.3.1-next-f1338f8080-20240426";function Pd(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Pd)}catch(e){console.error(e)}}Pd(),Pu.exports=De;var qp=Pu.exports,Dd,ou=qp;Dd=ou.createRoot,ou.hydrateRoot;var Vo={};Object.defineProperty(Vo,"__esModule",{value:!0});Vo.parse=nm;Vo.serialize=im;const Zp=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,Qp=/^[\u0021-\u003A\u003C-\u007E]*$/,Xp=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,Jp=/^[\u0020-\u003A\u003D-\u007E]*$/,em=Object.prototype.toString,tm=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function nm(e,t){const n=new tm,i=e.length;if(i<2)return n;const r=(t==null?void 0:t.decode)||rm;let s=0;do{const a=e.indexOf("=",s);if(a===-1)break;const o=e.indexOf(";",s),l=o===-1?i:o;if(a>l){s=e.lastIndexOf(";",a-1)+1;continue}const c=lu(e,s,a),d=uu(e,a,c),h=e.slice(c,d);if(n[h]===void 0){let g=lu(e,a+1,l),y=uu(e,l,g);const v=r(e.slice(g,y));n[h]=v}s=l+1}while(s<i);return n}function lu(e,t,n){do{const i=e.charCodeAt(t);if(i!==32&&i!==9)return t}while(++t<n);return n}function uu(e,t,n){for(;t>n;){const i=e.charCodeAt(--t);if(i!==32&&i!==9)return t+1}return n}function im(e,t,n){const i=(n==null?void 0:n.encode)||encodeURIComponent;if(!Zp.test(e))throw new TypeError(`argument name is invalid: ${e}`);const r=i(t);if(!Qp.test(r))throw new TypeError(`argument val is invalid: ${t}`);let s=e+"="+r;if(!n)return s;if(n.maxAge!==void 0){if(!Number.isInteger(n.maxAge))throw new TypeError(`option maxAge is invalid: ${n.maxAge}`);s+="; Max-Age="+n.maxAge}if(n.domain){if(!Xp.test(n.domain))throw new TypeError(`option domain is invalid: ${n.domain}`);s+="; Domain="+n.domain}if(n.path){if(!Jp.test(n.path))throw new TypeError(`option path is invalid: ${n.path}`);s+="; Path="+n.path}if(n.expires){if(!sm(n.expires)||!Number.isFinite(n.expires.valueOf()))throw new TypeError(`option expires is invalid: ${n.expires}`);s+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(s+="; HttpOnly"),n.secure&&(s+="; Secure"),n.partitioned&&(s+="; Partitioned"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():void 0){case"low":s+="; Priority=Low";break;case"medium":s+="; Priority=Medium";break;case"high":s+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${n.priority}`)}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":s+="; SameSite=Strict";break;case"lax":s+="; SameSite=Lax";break;case"none":s+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${n.sameSite}`)}return s}function rm(e){if(e.indexOf("%")===-1)return e;try{return decodeURIComponent(e)}catch{return e}}function sm(e){return em.call(e)==="[object Date]"}var cu="popstate";function am(e={}){function t(i,r){let{pathname:s,search:a,hash:o}=i.location;return Ba("",{pathname:s,search:a,hash:o},r.state&&r.state.usr||null,r.state&&r.state.key||"default")}function n(i,r){return typeof r=="string"?r:Ei(r)}return lm(t,n,null,e)}function Z(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function et(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function om(){return Math.random().toString(36).substring(2,10)}function du(e,t){return{usr:e.state,key:e.key,idx:t}}function Ba(e,t,n=null,i){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Mn(t):t,state:n,key:t&&t.key||i||om()}}function Ei({pathname:e="/",search:t="",hash:n=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),n&&n!=="#"&&(e+=n.charAt(0)==="#"?n:"#"+n),e}function Mn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let i=e.indexOf("?");i>=0&&(t.search=e.substring(i),e=e.substring(0,i)),e&&(t.pathname=e)}return t}function lm(e,t,n,i={}){let{window:r=document.defaultView,v5Compat:s=!1}=i,a=r.history,o="POP",l=null,c=d();c==null&&(c=0,a.replaceState({...a.state,idx:c},""));function d(){return(a.state||{idx:null}).idx}function h(){o="POP";let S=d(),p=S==null?null:S-c;c=S,l&&l({action:o,location:x.location,delta:p})}function g(S,p){o="PUSH";let f=Ba(x.location,S,p);c=d()+1;let m=du(f,c),w=x.createHref(f);try{a.pushState(m,"",w)}catch(b){if(b instanceof DOMException&&b.name==="DataCloneError")throw b;r.location.assign(w)}s&&l&&l({action:o,location:x.location,delta:1})}function y(S,p){o="REPLACE";let f=Ba(x.location,S,p);c=d();let m=du(f,c),w=x.createHref(f);a.replaceState(m,"",w),s&&l&&l({action:o,location:x.location,delta:0})}function v(S){return um(S)}let x={get action(){return o},get location(){return e(r,a)},listen(S){if(l)throw new Error("A history only accepts one active listener");return r.addEventListener(cu,h),l=S,()=>{r.removeEventListener(cu,h),l=null}},createHref(S){return t(r,S)},createURL:v,encodeLocation(S){let p=v(S);return{pathname:p.pathname,search:p.search,hash:p.hash}},push:g,replace:y,go(S){return a.go(S)}};return x}function um(e,t=!1){let n="http://localhost";typeof window<"u"&&(n=window.location.origin!=="null"?window.location.origin:window.location.href),Z(n,"No window.location.(origin|href) available to create URL");let i=typeof e=="string"?e:Ei(e);return i=i.replace(/ $/,"%20"),!t&&i.startsWith("//")&&(i=n+i),new URL(i,n)}function Ld(e,t,n="/"){return cm(e,t,n,!1)}function cm(e,t,n,i){let r=typeof t=="string"?Mn(t):t,s=pt(r.pathname||"/",n);if(s==null)return null;let a=Md(e);dm(a);let o=null;for(let l=0;o==null&&l<a.length;++l){let c=bm(s);o=wm(a[l],c,i)}return o}function Md(e,t=[],n=[],i=""){let r=(s,a,o)=>{let l={relativePath:o===void 0?s.path||"":o,caseSensitive:s.caseSensitive===!0,childrenIndex:a,route:s};l.relativePath.startsWith("/")&&(Z(l.relativePath.startsWith(i),`Absolute route path "${l.relativePath}" nested under path "${i}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),l.relativePath=l.relativePath.slice(i.length));let c=ut([i,l.relativePath]),d=n.concat(l);s.children&&s.children.length>0&&(Z(s.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${c}".`),Md(s.children,t,d,c)),!(s.path==null&&!s.index)&&t.push({path:c,score:ym(c,s.index),routesMeta:d})};return e.forEach((s,a)=>{var o;if(s.path===""||!((o=s.path)!=null&&o.includes("?")))r(s,a);else for(let l of Od(s.path))r(s,a,l)}),t}function Od(e){let t=e.split("/");if(t.length===0)return[];let[n,...i]=t,r=n.endsWith("?"),s=n.replace(/\?$/,"");if(i.length===0)return r?[s,""]:[s];let a=Od(i.join("/")),o=[];return o.push(...a.map(l=>l===""?s:[s,l].join("/"))),r&&o.push(...a),o.map(l=>e.startsWith("/")&&l===""?"/":l)}function dm(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:xm(t.routesMeta.map(i=>i.childrenIndex),n.routesMeta.map(i=>i.childrenIndex)))}var fm=/^:[\w-]+$/,hm=3,pm=2,mm=1,gm=10,vm=-2,fu=e=>e==="*";function ym(e,t){let n=e.split("/"),i=n.length;return n.some(fu)&&(i+=vm),t&&(i+=pm),n.filter(r=>!fu(r)).reduce((r,s)=>r+(fm.test(s)?hm:s===""?mm:gm),i)}function xm(e,t){return e.length===t.length&&e.slice(0,-1).every((i,r)=>i===t[r])?e[e.length-1]-t[t.length-1]:0}function wm(e,t,n=!1){let{routesMeta:i}=e,r={},s="/",a=[];for(let o=0;o<i.length;++o){let l=i[o],c=o===i.length-1,d=s==="/"?t:t.slice(s.length)||"/",h=Ur({path:l.relativePath,caseSensitive:l.caseSensitive,end:c},d),g=l.route;if(!h&&c&&n&&!i[i.length-1].route.index&&(h=Ur({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},d)),!h)return null;Object.assign(r,h.params),a.push({params:r,pathname:ut([s,h.pathname]),pathnameBase:Nm(ut([s,h.pathnameBase])),route:g}),h.pathnameBase!=="/"&&(s=ut([s,h.pathnameBase]))}return a}function Ur(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,i]=km(e.path,e.caseSensitive,e.end),r=t.match(n);if(!r)return null;let s=r[0],a=s.replace(/(.)\/+$/,"$1"),o=r.slice(1);return{params:i.reduce((c,{paramName:d,isOptional:h},g)=>{if(d==="*"){let v=o[g]||"";a=s.slice(0,s.length-v.length).replace(/(.)\/+$/,"$1")}const y=o[g];return h&&!y?c[d]=void 0:c[d]=(y||"").replace(/%2F/g,"/"),c},{}),pathname:s,pathnameBase:a,pattern:e}}function km(e,t=!1,n=!0){et(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let i=[],r="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,o,l)=>(i.push({paramName:o,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(i.push({paramName:"*"}),r+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?r+="\\/*$":e!==""&&e!=="/"&&(r+="(?:(?=\\/|$))"),[new RegExp(r,t?void 0:"i"),i]}function bm(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return et(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function pt(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,i=e.charAt(n);return i&&i!=="/"?null:e.slice(n)||"/"}function Sm(e,t="/"){let{pathname:n,search:i="",hash:r=""}=typeof e=="string"?Mn(e):e;return{pathname:n?n.startsWith("/")?n:Em(n,t):t,search:Tm(i),hash:jm(r)}}function Em(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(r=>{r===".."?n.length>1&&n.pop():r!=="."&&n.push(r)}),n.length>1?n.join("/"):"/"}function _s(e,t,n,i){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(i)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Cm(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Ad(e){let t=Cm(e);return t.map((n,i)=>i===t.length-1?n.pathname:n.pathnameBase)}function zd(e,t,n,i=!1){let r;typeof e=="string"?r=Mn(e):(r={...e},Z(!r.pathname||!r.pathname.includes("?"),_s("?","pathname","search",r)),Z(!r.pathname||!r.pathname.includes("#"),_s("#","pathname","hash",r)),Z(!r.search||!r.search.includes("#"),_s("#","search","hash",r)));let s=e===""||r.pathname==="",a=s?"/":r.pathname,o;if(a==null)o=n;else{let h=t.length-1;if(!i&&a.startsWith("..")){let g=a.split("/");for(;g[0]==="..";)g.shift(),h-=1;r.pathname=g.join("/")}o=h>=0?t[h]:"/"}let l=Sm(r,o),c=a&&a!=="/"&&a.endsWith("/"),d=(s||a===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(c||d)&&(l.pathname+="/"),l}var ut=e=>e.join("/").replace(/\/\/+/g,"/"),Nm=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Tm=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,jm=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Fm(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var _d=["POST","PUT","PATCH","DELETE"];new Set(_d);var Rm=["GET",..._d];new Set(Rm);var On=k.createContext(null);On.displayName="DataRouter";var cs=k.createContext(null);cs.displayName="DataRouterState";var Hd=k.createContext({isTransitioning:!1});Hd.displayName="ViewTransition";var Im=k.createContext(new Map);Im.displayName="Fetchers";var Pm=k.createContext(null);Pm.displayName="Await";var tt=k.createContext(null);tt.displayName="Navigation";var Ri=k.createContext(null);Ri.displayName="Location";var nt=k.createContext({outlet:null,matches:[],isDataRoute:!1});nt.displayName="Route";var Go=k.createContext(null);Go.displayName="RouteError";function Dm(e,{relative:t}={}){Z(Ii(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:i}=k.useContext(tt),{hash:r,pathname:s,search:a}=Pi(e,{relative:t}),o=s;return n!=="/"&&(o=s==="/"?n:ut([n,s])),i.createHref({pathname:o,search:a,hash:r})}function Ii(){return k.useContext(Ri)!=null}function zt(){return Z(Ii(),"useLocation() may be used only in the context of a <Router> component."),k.useContext(Ri).location}var Ud="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Bd(e){k.useContext(tt).static||k.useLayoutEffect(e)}function $o(){let{isDataRoute:e}=k.useContext(nt);return e?Ym():Lm()}function Lm(){Z(Ii(),"useNavigate() may be used only in the context of a <Router> component.");let e=k.useContext(On),{basename:t,navigator:n}=k.useContext(tt),{matches:i}=k.useContext(nt),{pathname:r}=zt(),s=JSON.stringify(Ad(i)),a=k.useRef(!1);return Bd(()=>{a.current=!0}),k.useCallback((l,c={})=>{if(et(a.current,Ud),!a.current)return;if(typeof l=="number"){n.go(l);return}let d=zd(l,JSON.parse(s),r,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:ut([t,d.pathname])),(c.replace?n.replace:n.push)(d,c.state,c)},[t,n,s,r,e])}k.createContext(null);function Mm(){let{matches:e}=k.useContext(nt),t=e[e.length-1];return t?t.params:{}}function Pi(e,{relative:t}={}){let{matches:n}=k.useContext(nt),{pathname:i}=zt(),r=JSON.stringify(Ad(n));return k.useMemo(()=>zd(e,JSON.parse(r),i,t==="path"),[e,r,i,t])}function Om(e,t){return Vd(e,t)}function Vd(e,t,n,i){var p;Z(Ii(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:r}=k.useContext(tt),{matches:s}=k.useContext(nt),a=s[s.length-1],o=a?a.params:{},l=a?a.pathname:"/",c=a?a.pathnameBase:"/",d=a&&a.route;{let f=d&&d.path||"";Gd(l,!d||f.endsWith("*")||f.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${l}" (under <Route path="${f}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${f}"> to <Route path="${f==="/"?"*":`${f}/*`}">.`)}let h=zt(),g;if(t){let f=typeof t=="string"?Mn(t):t;Z(c==="/"||((p=f.pathname)==null?void 0:p.startsWith(c)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${c}" but pathname "${f.pathname}" was given in the \`location\` prop.`),g=f}else g=h;let y=g.pathname||"/",v=y;if(c!=="/"){let f=c.replace(/^\//,"").split("/");v="/"+y.replace(/^\//,"").split("/").slice(f.length).join("/")}let x=Ld(e,{pathname:v});et(d||x!=null,`No routes matched location "${g.pathname}${g.search}${g.hash}" `),et(x==null||x[x.length-1].route.element!==void 0||x[x.length-1].route.Component!==void 0||x[x.length-1].route.lazy!==void 0,`Matched leaf route at location "${g.pathname}${g.search}${g.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let S=Um(x&&x.map(f=>Object.assign({},f,{params:Object.assign({},o,f.params),pathname:ut([c,r.encodeLocation?r.encodeLocation(f.pathname).pathname:f.pathname]),pathnameBase:f.pathnameBase==="/"?c:ut([c,r.encodeLocation?r.encodeLocation(f.pathnameBase).pathname:f.pathnameBase])})),s,n,i);return t&&S?k.createElement(Ri.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...g},navigationType:"POP"}},S):S}function Am(){let e=Wm(),t=Fm(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i="rgba(200,200,200, 0.5)",r={padding:"0.5rem",backgroundColor:i},s={padding:"2px 4px",backgroundColor:i},a=null;return console.error("Error handled by React Router default ErrorBoundary:",e),a=k.createElement(k.Fragment,null,k.createElement("p",null,"💿 Hey developer 👋"),k.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",k.createElement("code",{style:s},"ErrorBoundary")," or"," ",k.createElement("code",{style:s},"errorElement")," prop on your route.")),k.createElement(k.Fragment,null,k.createElement("h2",null,"Unexpected Application Error!"),k.createElement("h3",{style:{fontStyle:"italic"}},t),n?k.createElement("pre",{style:r},n):null,a)}var zm=k.createElement(Am,null),_m=class extends k.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?k.createElement(nt.Provider,{value:this.props.routeContext},k.createElement(Go.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Hm({routeContext:e,match:t,children:n}){let i=k.useContext(On);return i&&i.static&&i.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=t.route.id),k.createElement(nt.Provider,{value:e},n)}function Um(e,t=[],n=null,i=null){if(e==null){if(!n)return null;if(n.errors)e=n.matches;else if(t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let r=e,s=n==null?void 0:n.errors;if(s!=null){let l=r.findIndex(c=>c.route.id&&(s==null?void 0:s[c.route.id])!==void 0);Z(l>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(s).join(",")}`),r=r.slice(0,Math.min(r.length,l+1))}let a=!1,o=-1;if(n)for(let l=0;l<r.length;l++){let c=r[l];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(o=l),c.route.id){let{loaderData:d,errors:h}=n,g=c.route.loader&&!d.hasOwnProperty(c.route.id)&&(!h||h[c.route.id]===void 0);if(c.route.lazy||g){a=!0,o>=0?r=r.slice(0,o+1):r=[r[0]];break}}}return r.reduceRight((l,c,d)=>{let h,g=!1,y=null,v=null;n&&(h=s&&c.route.id?s[c.route.id]:void 0,y=c.route.errorElement||zm,a&&(o<0&&d===0?(Gd("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),g=!0,v=null):o===d&&(g=!0,v=c.route.hydrateFallbackElement||null)));let x=t.concat(r.slice(0,d+1)),S=()=>{let p;return h?p=y:g?p=v:c.route.Component?p=k.createElement(c.route.Component,null):c.route.element?p=c.route.element:p=l,k.createElement(Hm,{match:c,routeContext:{outlet:l,matches:x,isDataRoute:n!=null},children:p})};return n&&(c.route.ErrorBoundary||c.route.errorElement||d===0)?k.createElement(_m,{location:n.location,revalidation:n.revalidation,component:y,error:h,children:S(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):S()},null)}function Wo(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Bm(e){let t=k.useContext(On);return Z(t,Wo(e)),t}function Vm(e){let t=k.useContext(cs);return Z(t,Wo(e)),t}function Gm(e){let t=k.useContext(nt);return Z(t,Wo(e)),t}function Yo(e){let t=Gm(e),n=t.matches[t.matches.length-1];return Z(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function $m(){return Yo("useRouteId")}function Wm(){var i;let e=k.useContext(Go),t=Vm("useRouteError"),n=Yo("useRouteError");return e!==void 0?e:(i=t.errors)==null?void 0:i[n]}function Ym(){let{router:e}=Bm("useNavigate"),t=Yo("useNavigate"),n=k.useRef(!1);return Bd(()=>{n.current=!0}),k.useCallback(async(r,s={})=>{et(n.current,Ud),n.current&&(typeof r=="number"?e.navigate(r):await e.navigate(r,{fromRouteId:t,...s}))},[e,t])}var hu={};function Gd(e,t,n){!t&&!hu[e]&&(hu[e]=!0,et(!1,n))}k.memo(Km);function Km({routes:e,future:t,state:n}){return Vd(e,void 0,n,t)}function vt(e){Z(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function qm({basename:e="/",children:t=null,location:n,navigationType:i="POP",navigator:r,static:s=!1}){Z(!Ii(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let a=e.replace(/^\/*/,"/"),o=k.useMemo(()=>({basename:a,navigator:r,static:s,future:{}}),[a,r,s]);typeof n=="string"&&(n=Mn(n));let{pathname:l="/",search:c="",hash:d="",state:h=null,key:g="default"}=n,y=k.useMemo(()=>{let v=pt(l,a);return v==null?null:{location:{pathname:v,search:c,hash:d,state:h,key:g},navigationType:i}},[a,l,c,d,h,g,i]);return et(y!=null,`<Router basename="${a}"> is not able to match the URL "${l}${c}${d}" because it does not start with the basename, so the <Router> won't render anything.`),y==null?null:k.createElement(tt.Provider,{value:o},k.createElement(Ri.Provider,{children:t,value:y}))}function Zm({children:e,location:t}){return Om(Va(e),t)}function Va(e,t=[]){let n=[];return k.Children.forEach(e,(i,r)=>{if(!k.isValidElement(i))return;let s=[...t,r];if(i.type===k.Fragment){n.push.apply(n,Va(i.props.children,s));return}Z(i.type===vt,`[${typeof i.type=="string"?i.type:i.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Z(!i.props.index||!i.props.children,"An index route cannot have child routes.");let a={id:i.props.id||s.join("-"),caseSensitive:i.props.caseSensitive,element:i.props.element,Component:i.props.Component,index:i.props.index,path:i.props.path,loader:i.props.loader,action:i.props.action,hydrateFallbackElement:i.props.hydrateFallbackElement,HydrateFallback:i.props.HydrateFallback,errorElement:i.props.errorElement,ErrorBoundary:i.props.ErrorBoundary,hasErrorBoundary:i.props.hasErrorBoundary===!0||i.props.ErrorBoundary!=null||i.props.errorElement!=null,shouldRevalidate:i.props.shouldRevalidate,handle:i.props.handle,lazy:i.props.lazy};i.props.children&&(a.children=Va(i.props.children,s)),n.push(a)}),n}var pr="get",mr="application/x-www-form-urlencoded";function ds(e){return e!=null&&typeof e.tagName=="string"}function Qm(e){return ds(e)&&e.tagName.toLowerCase()==="button"}function Xm(e){return ds(e)&&e.tagName.toLowerCase()==="form"}function Jm(e){return ds(e)&&e.tagName.toLowerCase()==="input"}function eg(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function tg(e,t){return e.button===0&&(!t||t==="_self")&&!eg(e)}var Ji=null;function ng(){if(Ji===null)try{new FormData(document.createElement("form"),0),Ji=!1}catch{Ji=!0}return Ji}var ig=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Hs(e){return e!=null&&!ig.has(e)?(et(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${mr}"`),null):e}function rg(e,t){let n,i,r,s,a;if(Xm(e)){let o=e.getAttribute("action");i=o?pt(o,t):null,n=e.getAttribute("method")||pr,r=Hs(e.getAttribute("enctype"))||mr,s=new FormData(e)}else if(Qm(e)||Jm(e)&&(e.type==="submit"||e.type==="image")){let o=e.form;if(o==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||o.getAttribute("action");if(i=l?pt(l,t):null,n=e.getAttribute("formmethod")||o.getAttribute("method")||pr,r=Hs(e.getAttribute("formenctype"))||Hs(o.getAttribute("enctype"))||mr,s=new FormData(o,e),!ng()){let{name:c,type:d,value:h}=e;if(d==="image"){let g=c?`${c}.`:"";s.append(`${g}x`,"0"),s.append(`${g}y`,"0")}else c&&s.append(c,h)}}else{if(ds(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=pr,i=null,r=mr,a=e}return s&&r==="text/plain"&&(a=s,s=void 0),{action:i,method:n.toLowerCase(),encType:r,formData:s,body:a}}function Ko(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function sg(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function ag(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function og(e,t,n){let i=await Promise.all(e.map(async r=>{let s=t.routes[r.route.id];if(s){let a=await sg(s,n);return a.links?a.links():[]}return[]}));return dg(i.flat(1).filter(ag).filter(r=>r.rel==="stylesheet"||r.rel==="preload").map(r=>r.rel==="stylesheet"?{...r,rel:"prefetch",as:"style"}:{...r,rel:"prefetch"}))}function pu(e,t,n,i,r,s){let a=(l,c)=>n[c]?l.route.id!==n[c].route.id:!0,o=(l,c)=>{var d;return n[c].pathname!==l.pathname||((d=n[c].route.path)==null?void 0:d.endsWith("*"))&&n[c].params["*"]!==l.params["*"]};return s==="assets"?t.filter((l,c)=>a(l,c)||o(l,c)):s==="data"?t.filter((l,c)=>{var h;let d=i.routes[l.route.id];if(!d||!d.hasLoader)return!1;if(a(l,c)||o(l,c))return!0;if(l.route.shouldRevalidate){let g=l.route.shouldRevalidate({currentUrl:new URL(r.pathname+r.search+r.hash,window.origin),currentParams:((h=n[0])==null?void 0:h.params)||{},nextUrl:new URL(e,window.origin),nextParams:l.params,defaultShouldRevalidate:!0});if(typeof g=="boolean")return g}return!0}):[]}function lg(e,t,{includeHydrateFallback:n}={}){return ug(e.map(i=>{let r=t.routes[i.route.id];if(!r)return[];let s=[r.module];return r.clientActionModule&&(s=s.concat(r.clientActionModule)),r.clientLoaderModule&&(s=s.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(s=s.concat(r.hydrateFallbackModule)),r.imports&&(s=s.concat(r.imports)),s}).flat(1))}function ug(e){return[...new Set(e)]}function cg(e){let t={},n=Object.keys(e).sort();for(let i of n)t[i]=e[i];return t}function dg(e,t){let n=new Set;return new Set(t),e.reduce((i,r)=>{let s=JSON.stringify(cg(r));return n.has(s)||(n.add(s),i.push({key:s,link:r})),i},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var fg=new Set([100,101,204,205]);function hg(e,t){let n=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return n.pathname==="/"?n.pathname="_root.data":t&&pt(n.pathname,t)==="/"?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}function $d(){let e=k.useContext(On);return Ko(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function pg(){let e=k.useContext(cs);return Ko(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var qo=k.createContext(void 0);qo.displayName="FrameworkContext";function Wd(){let e=k.useContext(qo);return Ko(e,"You must render this element inside a <HydratedRouter> element"),e}function mg(e,t){let n=k.useContext(qo),[i,r]=k.useState(!1),[s,a]=k.useState(!1),{onFocus:o,onBlur:l,onMouseEnter:c,onMouseLeave:d,onTouchStart:h}=t,g=k.useRef(null);k.useEffect(()=>{if(e==="render"&&a(!0),e==="viewport"){let x=p=>{p.forEach(f=>{a(f.isIntersecting)})},S=new IntersectionObserver(x,{threshold:.5});return g.current&&S.observe(g.current),()=>{S.disconnect()}}},[e]),k.useEffect(()=>{if(i){let x=setTimeout(()=>{a(!0)},100);return()=>{clearTimeout(x)}}},[i]);let y=()=>{r(!0)},v=()=>{r(!1),a(!1)};return n?e!=="intent"?[s,g,{}]:[s,g,{onFocus:$n(o,y),onBlur:$n(l,v),onMouseEnter:$n(c,y),onMouseLeave:$n(d,v),onTouchStart:$n(h,y)}]:[!1,g,{}]}function $n(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function gg({page:e,...t}){let{router:n}=$d(),i=k.useMemo(()=>Ld(n.routes,e,n.basename),[n.routes,e,n.basename]);return i?k.createElement(yg,{page:e,matches:i,...t}):null}function vg(e){let{manifest:t,routeModules:n}=Wd(),[i,r]=k.useState([]);return k.useEffect(()=>{let s=!1;return og(e,t,n).then(a=>{s||r(a)}),()=>{s=!0}},[e,t,n]),i}function yg({page:e,matches:t,...n}){let i=zt(),{manifest:r,routeModules:s}=Wd(),{basename:a}=$d(),{loaderData:o,matches:l}=pg(),c=k.useMemo(()=>pu(e,t,l,r,i,"data"),[e,t,l,r,i]),d=k.useMemo(()=>pu(e,t,l,r,i,"assets"),[e,t,l,r,i]),h=k.useMemo(()=>{if(e===i.pathname+i.search+i.hash)return[];let v=new Set,x=!1;if(t.forEach(p=>{var m;let f=r.routes[p.route.id];!f||!f.hasLoader||(!c.some(w=>w.route.id===p.route.id)&&p.route.id in o&&((m=s[p.route.id])!=null&&m.shouldRevalidate)||f.hasClientLoader?x=!0:v.add(p.route.id))}),v.size===0)return[];let S=hg(e,a);return x&&v.size>0&&S.searchParams.set("_routes",t.filter(p=>v.has(p.route.id)).map(p=>p.route.id).join(",")),[S.pathname+S.search]},[a,o,i,r,c,t,e,s]),g=k.useMemo(()=>lg(d,r),[d,r]),y=vg(d);return k.createElement(k.Fragment,null,h.map(v=>k.createElement("link",{key:v,rel:"prefetch",as:"fetch",href:v,...n})),g.map(v=>k.createElement("link",{key:v,rel:"modulepreload",href:v,...n})),y.map(({key:v,link:x})=>k.createElement("link",{key:v,...x})))}function xg(...e){return t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t)})}}var Yd=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Yd&&(window.__reactRouterVersion="7.6.1")}catch{}function wg({basename:e,children:t,window:n}){let i=k.useRef();i.current==null&&(i.current=am({window:n,v5Compat:!0}));let r=i.current,[s,a]=k.useState({action:r.action,location:r.location}),o=k.useCallback(l=>{k.startTransition(()=>a(l))},[a]);return k.useLayoutEffect(()=>r.listen(o),[r,o]),k.createElement(qm,{basename:e,children:t,location:s.location,navigationType:s.action,navigator:r})}var Kd=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,me=k.forwardRef(function({onClick:t,discover:n="render",prefetch:i="none",relative:r,reloadDocument:s,replace:a,state:o,target:l,to:c,preventScrollReset:d,viewTransition:h,...g},y){let{basename:v}=k.useContext(tt),x=typeof c=="string"&&Kd.test(c),S,p=!1;if(typeof c=="string"&&x&&(S=c,Yd))try{let j=new URL(window.location.href),C=c.startsWith("//")?new URL(j.protocol+c):new URL(c),R=pt(C.pathname,v);C.origin===j.origin&&R!=null?c=R+C.search+C.hash:p=!0}catch{et(!1,`<Link to="${c}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let f=Dm(c,{relative:r}),[m,w,b]=mg(i,g),E=Eg(c,{replace:a,state:o,target:l,preventScrollReset:d,relative:r,viewTransition:h});function N(j){t&&t(j),j.defaultPrevented||E(j)}let T=k.createElement("a",{...g,...b,href:S||f,onClick:p||s?t:N,ref:xg(y,w),target:l,"data-discover":!x&&n==="render"?"true":void 0});return m&&!x?k.createElement(k.Fragment,null,T,k.createElement(gg,{page:f})):T});me.displayName="Link";var kg=k.forwardRef(function({"aria-current":t="page",caseSensitive:n=!1,className:i="",end:r=!1,style:s,to:a,viewTransition:o,children:l,...c},d){let h=Pi(a,{relative:c.relative}),g=zt(),y=k.useContext(cs),{navigator:v,basename:x}=k.useContext(tt),S=y!=null&&Fg(h)&&o===!0,p=v.encodeLocation?v.encodeLocation(h).pathname:h.pathname,f=g.pathname,m=y&&y.navigation&&y.navigation.location?y.navigation.location.pathname:null;n||(f=f.toLowerCase(),m=m?m.toLowerCase():null,p=p.toLowerCase()),m&&x&&(m=pt(m,x)||m);const w=p!=="/"&&p.endsWith("/")?p.length-1:p.length;let b=f===p||!r&&f.startsWith(p)&&f.charAt(w)==="/",E=m!=null&&(m===p||!r&&m.startsWith(p)&&m.charAt(p.length)==="/"),N={isActive:b,isPending:E,isTransitioning:S},T=b?t:void 0,j;typeof i=="function"?j=i(N):j=[i,b?"active":null,E?"pending":null,S?"transitioning":null].filter(Boolean).join(" ");let C=typeof s=="function"?s(N):s;return k.createElement(me,{...c,"aria-current":T,className:j,ref:d,style:C,to:a,viewTransition:o},typeof l=="function"?l(N):l)});kg.displayName="NavLink";var bg=k.forwardRef(({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:i,replace:r,state:s,method:a=pr,action:o,onSubmit:l,relative:c,preventScrollReset:d,viewTransition:h,...g},y)=>{let v=Tg(),x=jg(o,{relative:c}),S=a.toLowerCase()==="get"?"get":"post",p=typeof o=="string"&&Kd.test(o),f=m=>{if(l&&l(m),m.defaultPrevented)return;m.preventDefault();let w=m.nativeEvent.submitter,b=(w==null?void 0:w.getAttribute("formmethod"))||a;v(w||m.currentTarget,{fetcherKey:t,method:b,navigate:n,replace:r,state:s,relative:c,preventScrollReset:d,viewTransition:h})};return k.createElement("form",{ref:y,method:S,action:x,onSubmit:i?l:f,...g,"data-discover":!p&&e==="render"?"true":void 0})});bg.displayName="Form";function Sg(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function qd(e){let t=k.useContext(On);return Z(t,Sg(e)),t}function Eg(e,{target:t,replace:n,state:i,preventScrollReset:r,relative:s,viewTransition:a}={}){let o=$o(),l=zt(),c=Pi(e,{relative:s});return k.useCallback(d=>{if(tg(d,t)){d.preventDefault();let h=n!==void 0?n:Ei(l)===Ei(c);o(e,{replace:h,state:i,preventScrollReset:r,relative:s,viewTransition:a})}},[l,o,c,n,i,t,e,r,s,a])}var Cg=0,Ng=()=>`__${String(++Cg)}__`;function Tg(){let{router:e}=qd("useSubmit"),{basename:t}=k.useContext(tt),n=$m();return k.useCallback(async(i,r={})=>{let{action:s,method:a,encType:o,formData:l,body:c}=rg(i,t);if(r.navigate===!1){let d=r.fetcherKey||Ng();await e.fetch(d,n,r.action||s,{preventScrollReset:r.preventScrollReset,formData:l,body:c,formMethod:r.method||a,formEncType:r.encType||o,flushSync:r.flushSync})}else await e.navigate(r.action||s,{preventScrollReset:r.preventScrollReset,formData:l,body:c,formMethod:r.method||a,formEncType:r.encType||o,replace:r.replace,state:r.state,fromRouteId:n,flushSync:r.flushSync,viewTransition:r.viewTransition})},[e,t,n])}function jg(e,{relative:t}={}){let{basename:n}=k.useContext(tt),i=k.useContext(nt);Z(i,"useFormAction must be used inside a RouteContext");let[r]=i.matches.slice(-1),s={...Pi(e||".",{relative:t})},a=zt();if(e==null){s.search=a.search;let o=new URLSearchParams(s.search),l=o.getAll("index");if(l.some(d=>d==="")){o.delete("index"),l.filter(h=>h).forEach(h=>o.append("index",h));let d=o.toString();s.search=d?`?${d}`:""}}return(!e||e===".")&&r.route.index&&(s.search=s.search?s.search.replace(/^\?/,"?index&"):"?index"),n!=="/"&&(s.pathname=s.pathname==="/"?n:ut([n,s.pathname])),Ei(s)}function Fg(e,t={}){let n=k.useContext(Hd);Z(n!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:i}=qd("useViewTransitionState"),r=Pi(e,{relative:t.relative});if(!n.isTransitioning)return!1;let s=pt(n.currentLocation.pathname,i)||n.currentLocation.pathname,a=pt(n.nextLocation.pathname,i)||n.nextLocation.pathname;return Ur(r.pathname,a)!=null||Ur(r.pathname,s)!=null}[...fg];function Rg({title:e,titleId:t,...n},i){return k.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},n),e?k.createElement("title",{id:t},e):null,k.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}const Ig=k.forwardRef(Rg);function Pg({title:e,titleId:t,...n},i){return k.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},n),e?k.createElement("title",{id:t},e):null,k.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}const Dg=k.forwardRef(Pg),Lg=({language:e,toggleLanguage:t})=>{const[n,i]=k.useState(!1),[r,s]=k.useState(null),[a,o]=k.useState(null),l=$o(),c=k.useRef(null),d=k.useRef(null),h=()=>{i(!n)};k.useEffect(()=>{function y(v){window.innerWidth>=768&&c.current&&!c.current.contains(v.target)&&s(null)}return document.addEventListener("mousedown",y),()=>{document.removeEventListener("mousedown",y)}},[]),k.useEffect(()=>{const y=()=>{if(d.current){const v=d.current.offsetHeight;document.body.style.paddingTop=`${v}px`}};return y(),window.addEventListener("resize",y),()=>{window.removeEventListener("resize",y)}},[]);const g=[{en:"HOME",vi:"TRANG CHỦ",path:"/",dropdown:!1},{en:"ABOUT US",vi:"GIỚI THIỆU",path:"/about",dropdown:!0,submenu:[{en:"Our Passion",vi:"Đam Mê Của Chúng Tôi",path:"/about/passion"},{en:"Sustainability",vi:"Phát Triển Bền Vững",path:"/about/sustainability"}]},{en:"PRODUCTS",vi:"SẢN PHẨM",path:"/products",dropdown:!0,megaMenu:!0,categories:[{en:"FRESH FRUIT",vi:"TRÁI CÂY TƯƠI",products:[{en:"AVOCADO",vi:"BƠ",path:"/products/fresh-fruit/avocado"},{en:"BANANA",vi:"CHUỐI",path:"/products/fresh-fruit/banana"},{en:"PITAYA",vi:"THANH LONG",path:"/products/fresh-fruit/pitaya"},{en:"DURIAN",vi:"SẦU RIÊNG",path:"/products/fresh-fruit/durian"},{en:"JACKFRUIT",vi:"MÍT",path:"/products/fresh-fruit/jackfruit"},{en:"MANGO",vi:"XOÀI",path:"/products/fresh-fruit/mango"},{en:"COCONUT",vi:"DỪA",path:"/products/fresh-fruit/coconut"},{en:"PINEAPPLE",vi:"KHÓM",path:"/products/fresh-fruit/pineapple"}]},{en:"FROZEN FRUIT",vi:"TRÁI CÂY ĐÔNG LẠNH",products:[{en:"FROZEN AVOCADO",vi:"BƠ ĐÔNG LẠNH",path:"/products/frozen-fruit/frozen-avocado"},{en:"FROZEN BANANA",vi:"CHUỐI ĐÔNG LẠNH",path:"/products/frozen-fruit/frozen-banana"},{en:"FROZEN PITAYA",vi:"THANH LONG ĐÔNG LẠNH",path:"/products/frozen-fruit/frozen-pitaya"},{en:"FROZEN DURIAN",vi:"SẦU RIÊNG ĐÔNG LẠNH",path:"/products/frozen-fruit/frozen-durian"},{en:"FROZEN JACKFRUIT",vi:"MÍT ĐÔNG LẠNH",path:"/products/frozen-fruit/frozen-jackfruit"},{en:"FROZEN MANGO",vi:"XOÀI ĐÔNG LẠNH",path:"/products/frozen-fruit/frozen-mango"},{en:"FROZEN COCONUT MEAT",vi:"CƠM DỪA ĐÔNG LẠNH",path:"/products/frozen-fruit/frozen-coconut-meat"},{en:"FROZEN PINEAPPLE",vi:"KHÓM ĐÔNG LẠNH",path:"/products/frozen-fruit/frozen-pineapple"}]},{en:"DRIED FRUIT",vi:"TRÁI CÂY SẤY KHÔ",products:[{en:"DRIED BANANA",vi:"CHUỐI SẤY KHÔ",path:"/products/dried-fruit/dried-banana"},{en:"DRIED JACKFRUIT",vi:"MÍT SẤY KHÔ",path:"/products/dried-fruit/dried-jackfruit"},{en:"DRIED MANGO",vi:"XOÀI SẤY KHÔ",path:"/products/dried-fruit/dried-mango"},{en:"DRIED CASHEW NUT",vi:"HẠT ĐIỀU SẤY KHÔ",path:"/products/dried-fruit/dried-cashew-nut"},{en:"DRIED CARROTS",vi:"CÀ RỐT SẤY KHÔ",path:"/products/dried-fruit/dried-carrots"},{en:"DRIED LOTUS SEED",vi:"HẠT SEN SẤY KHÔ",path:"/products/dried-fruit/dried-lotus-seed"},{en:"DRIED COCONUT",vi:"DỪA SẤY KHÔ",path:"/products/dried-fruit/dried-coconut"},{en:"DRIED SWEET POTATO",vi:"KHOAI LANG SẤY KHÔ",path:"/products/dried-fruit/dried-sweet-potato"}]},{en:"FREEZE DRIED FRUIT",vi:"TRÁI CÂY ĐÔNG KHÔ",products:[{en:"FD AVOCADO",vi:"BƠ ĐÔNG KHÔ",path:"/products/freeze-dried-fruit/fd-avocado"},{en:"FD BANANA",vi:"CHUỐI ĐÔNG KHÔ",path:"/products/freeze-dried-fruit/fd-banana"},{en:"FD PITAYA",vi:"THANH LONG ĐÔNG KHÔ",path:"/products/freeze-dried-fruit/fd-pitaya"},{en:"FD DURIAN",vi:"SẦU RIÊNG ĐÔNG KHÔ",path:"/products/freeze-dried-fruit/fd-durian"},{en:"FD JACKFRUIT",vi:"MÍT ĐÔNG KHÔ",path:"/products/freeze-dried-fruit/fd-jackfruit"},{en:"FD MANGO",vi:"XOÀI ĐÔNG KHÔ",path:"/products/freeze-dried-fruit/fd-mango"},{en:"FD RAMBUTAN",vi:"CHÔM CHÔM ĐÔNG KHÔ",path:"/products/freeze-dried-fruit/fd-rambutan"},{en:"FD PINEAPPLE",vi:"KHÓM ĐÔNG KHÔ",path:"/products/freeze-dried-fruit/fd-pineapple"}]}]},{en:"CONTACT US",vi:"LIÊN HỆ",path:"/contact",dropdown:!1}];return u.jsxs("header",{ref:d,className:"fixed-header",children:[u.jsx("div",{className:"bg-[rgb(19,104,174)] text-white py-1 md:py-2 w-full",children:u.jsxs("div",{className:"w-full flex justify-between items-center px-1",children:[u.jsx("div",{className:"md:w-1/4 w-64 flex ml-[60%]"}),u.jsx("div",{className:"md:w-2/4 hidden md:flex justify-center",children:u.jsxs("div",{className:"flex space-x-2 md:space-x-4 items-center text-xs",children:[u.jsxs(me,{to:"/register",className:"text-white hover:text-gray-200 transition-colors duration-200 flex items-center",children:[u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 md:h-4 md:w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})}),e==="vi"?"Tạo tài khoản":"Create an Account"]}),u.jsx("span",{className:"text-white",children:"|"}),u.jsxs(me,{to:"/login",className:"text-white hover:text-gray-200 transition-colors duration-200 flex items-center",children:[u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 md:h-4 md:w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"})}),e==="vi"?"Đăng nhập":"Sign In"]})]})}),u.jsx("div",{className:"md:w-1/4 mr-16",children:u.jsxs("div",{className:"flex items-center justify-end mr-[5%]",children:[u.jsxs("button",{className:"hidden md:flex text-white hover:text-gray-200 transition-colors duration-200 items-center text-xs whitespace-nowrap",onClick:()=>{},"aria-label":"Search",children:[u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"w-3 h-3 md:w-4 md:h-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),u.jsx("span",{className:"hidden sm:inline",children:e==="vi"?"Tìm kiếm":"search"})]}),u.jsxs("div",{className:"md:hidden relative group mr-2",children:[u.jsxs("button",{className:"text-white hover:text-gray-200 flex items-center ml-[60%]",children:[u.jsx("img",{src:e==="vi"?"/images/assets/flag-vn.svg":"/images/assets/flag-en.svg",alt:e==="vi"?"Cờ Việt Nam":"UK Flag",className:"w-4 h-3 md:w-6 md:h-4 mr-1 md:mr-2 inline-block rounded-sm md:border md:border-gray-300 md:shadow-sm"}),u.jsx("span",{className:"text-sm md:text-sm",children:e==="vi"?"Vietnam":"English"}),u.jsx("svg",{className:"ml-1 md:ml-2 h-6 w-6 md:h-8 md:w-8 text-white group-hover:text-gray-200 transition-colors duration-200",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),u.jsxs("div",{className:"absolute right-0 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-20 hidden group-hover:block overflow-hidden",children:[u.jsxs("button",{onClick:e==="vi"?void 0:t,className:`block w-full text-left px-4 py-3 text-sm ${e==="vi"?"bg-gray-100 text-gray-600 font-medium cursor-default":"text-gray-800 hover:bg-gray-100"} flex items-center`,disabled:e==="vi",children:[u.jsx("img",{src:"/images/assets/flag-vn.svg",alt:"Cờ Việt Nam",className:"w-6 h-4 mr-2 inline-block rounded-sm md:border md:border-gray-300 md:shadow-sm"}),"Vietnam"]}),u.jsxs("button",{onClick:e==="en"?void 0:t,className:`block w-full text-left px-4 py-3 text-sm ${e==="en"?"bg-gray-100 text-gray-600 font-medium cursor-default":"text-gray-800 hover:bg-gray-100"} flex items-center`,disabled:e==="en",children:[u.jsx("img",{src:"/images/assets/flag-en.svg",alt:"UK Flag",className:"w-6 h-4 mr-2 inline-block rounded-sm md:border md:border-gray-300 md:shadow-sm"}),"English"]})]})]})]})})]})}),u.jsx("nav",{className:"bg-white shadow-md border-t-2 ",children:u.jsxs("div",{className:"w-full px-1",children:[u.jsxs("div",{className:"flex justify-between items-center py-1 md:py-2 md:flex-row",children:[u.jsx("div",{className:"md:w-1/4 flex items-center md:justify-start md:ml-24 ml-[10%]",children:u.jsx(me,{to:"/",className:"flex items-center",children:u.jsx("img",{src:"/images/assets/logo.png",alt:"AN BINH FOODS Logo",className:"h-8 md:h-10 w-auto",onError:y=>{const v=y.target;v.src="/images/assets/logo-placeholder.svg"}})})}),u.jsx("div",{className:"md:hidden flex items-center mr-[10%]",children:u.jsx("button",{onClick:h,className:"text-gray-800 hover:text-red-600 focus:outline-none p-1",children:n?u.jsx(Dg,{className:"h-6 w-6 md:h-8 md:w-8"}):u.jsx(Ig,{className:"h-6 w-6 md:h-8 md:w-8"})})}),u.jsx("div",{className:"md:w-2/4 hidden md:flex items-center justify-center ",ref:c,children:g.map((y,v)=>{var x,S;return u.jsxs("div",{className:"relative group mx-6",children:[y.dropdown?u.jsxs("div",{className:"text-gray-800 hover:text-green-600 font-bold cursor-pointer flex items-center font-serif text-sm ",onMouseEnter:()=>s(v),children:[e==="vi"?y.vi:y.en,u.jsx("svg",{className:"ml-1 h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}):u.jsx(me,{to:y.path,className:"text-gray-800 hover:text-green-600 font-bold  cursor-pointer flex items-center font-serif  text-sm ",children:e==="vi"?y.vi:y.en}),y.dropdown&&r===v&&!y.megaMenu&&u.jsx("div",{className:"absolute left-1/2 transform -translate-x-1/2 mt-2 w-48 bg-white border border-gray-200 rounded-xl shadow-xl py-3 z-10 transition-all duration-300 ease-in-out text-center",onMouseLeave:()=>s(null),children:(x=y.submenu)==null?void 0:x.map((p,f)=>u.jsx(me,{to:p.path,className:"block px-4 py-2 text-sm text-black hover:bg-green-50 hover:text-green-600 transition-colors duration-200 whitespace-nowrap overflow-hidden text-overflow-ellipsis",children:e==="vi"?p.vi:p.en},f))}),y.dropdown&&r===v&&y.megaMenu&&u.jsx("div",{className:"absolute left-1/2 transform -translate-x-1/2 mt-2 w-[1000px] bg-white border border-gray-200 rounded-xl shadow-xl py-6 px-2 z-10 grid grid-cols-4 gap-4 transition-all duration-300 ease-in-out",onMouseLeave:()=>s(null),children:(S=y.categories)==null?void 0:S.map((p,f)=>u.jsxs("div",{className:"px-4 min-w-[200px]",children:[u.jsx("h3",{className:`font-semibold  text-black mb-3 border-b border-gray-200 pb-2 text-left\r
                           whitespace-nowrap overflow-hidden text-overflow-ellipsis text-sm md:text-base uppercase`,children:e==="vi"?p.vi:p.en}),u.jsx("ul",{className:"space-y-1",children:p.products.map((m,w)=>u.jsx("li",{children:u.jsx(me,{to:m.path,className:"block text-xs font-semibold font-sans  text-gray-700 hover:text-green-600 py-1 transition-colors duration-200 whitespace-nowrap overflow-hidden text-overflow-ellipsis text-left uppercase tracking-wide",children:e==="vi"?m.vi:m.en})},w))})]},f))})]},v)})}),u.jsx("div",{className:"hidden md:flex md:w-1/4 mr-[2%] justify-end",children:u.jsx("div",{className:"flex items-center justify-end",children:u.jsxs("div",{className:"relative group mr-2",children:[u.jsxs("button",{className:"text-blue hover:text-gray-200 flex items-center",children:[u.jsx("img",{src:e==="vi"?"/images/assets/flag-vn.svg":"/images/assets/flag-en.svg",alt:e==="vi"?"Cờ Việt Nam":"UK Flag",className:"w-6 h-4 mr-2 inline-block rounded-sm border border-gray-300 shadow-sm"}),u.jsx("span",{children:e==="vi"?"Vietnam":"English"}),u.jsx("svg",{className:"ml-1 h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),u.jsxs("div",{className:"absolute right-0 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-20 hidden group-hover:block overflow-hidden",children:[u.jsxs("button",{onClick:e==="vi"?void 0:t,className:`block w-full text-left px-4 py-3 text-sm ${e==="vi"?"bg-gray-100 text-gray-600 font-medium cursor-default":"text-gray-800 hover:bg-gray-100"} flex items-center`,disabled:e==="vi",children:[u.jsx("img",{src:"/images/assets/flag-vn.svg",alt:"Cờ Việt Nam",className:"w-6 h-4 mr-2 inline-block rounded-sm border border-gray-300 shadow-sm"}),"Vietnam"]}),u.jsxs("button",{onClick:e==="en"?void 0:t,className:`block w-full text-left px-4 py-3 text-sm ${e==="en"?"bg-gray-100 text-gray-600 font-medium cursor-default":"text-gray-800 hover:bg-gray-100"} flex items-center`,disabled:e==="en",children:[u.jsx("img",{src:"/images/assets/flag-en.svg",alt:"UK Flag",className:"w-6 h-4 mr-2 inline-block rounded-sm border border-gray-300 shadow-sm"}),"English"]})]})]})})})]}),n&&u.jsx("div",{className:"md:hidden py-2 border-t",children:u.jsx("div",{className:"flex flex-col space-y-2",children:g.map((y,v)=>{var x,S;return u.jsx("div",{children:y.dropdown?u.jsxs(u.Fragment,{children:[u.jsxs("div",{className:"flex items-center",children:[u.jsx("button",{className:"text-gray-800 hover:text-red-600 font-medium px-4 py-1 flex-1 text-left text-sm",onClick:()=>{console.log("Dropdown toggle clicked, index:",v,"current activeDropdown:",r),s(r===v?null:v),o(null)},children:e==="vi"?y.vi:y.en}),u.jsx("button",{className:"text-gray-800 hover:text-red-600 px-4 py-1",onClick:()=>{console.log("Dropdown toggle clicked, index:",v,"current activeDropdown:",r),s(r===v?null:v),o(null)},children:u.jsx("svg",{className:`h-4 w-4 transition-transform ${r===v?"transform rotate-180":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]}),r===v&&!y.megaMenu&&u.jsx("div",{className:"pl-8 py-1 space-y-1 bg-white rounded-lg mx-4 mt-2 border border-gray-200 relative z-50",style:{pointerEvents:"auto"},onClick:p=>{p.stopPropagation()},children:(x=y.submenu)==null?void 0:x.map((p,f)=>u.jsx("div",{style:{pointerEvents:"auto"},onTouchEnd:m=>{m.stopPropagation(),i(!1),s(null),l(p.path)},onMouseUp:m=>{m.stopPropagation(),i(!1),s(null),l(p.path)},children:u.jsx("div",{className:"block w-full text-left text-gray-700 hover:text-green-600 py-1 px-2 transition-colors duration-200 whitespace-nowrap overflow-hidden text-overflow-ellipsis cursor-pointer relative z-50 text-xs",style:{pointerEvents:"auto"},children:e==="vi"?p.vi:p.en})},f))}),r===v&&y.megaMenu&&u.jsxs("div",{className:"bg-white rounded-lg mx-4 mt-2 border border-gray-200 max-h-[85vh] overflow-y-auto",onClick:p=>{p.stopPropagation()},children:[(S=y.categories)==null?void 0:S.map((p,f)=>u.jsxs("div",{className:"border-b border-gray-200 last:border-b-0",children:[u.jsxs("button",{className:"w-full text-left px-4 py-2 font-semibold text-gray-800 hover:bg-gray-100 flex justify-between items-center text-sm",onClick:m=>{m.preventDefault(),m.stopPropagation(),o(a===f?null:f)},type:"button",children:[u.jsx("span",{className:"text-xs uppercase",children:e==="vi"?p.vi:p.en}),u.jsx("svg",{className:`h-4 w-4 transition-transform ${a===f?"transform rotate-180":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),a===f&&u.jsx("div",{className:"px-4 pb-2 space-y-1 bg-white border border-gray-200 max-h-[40vh] overflow-y-auto",children:p.products&&p.products.length>0?p.products.map((m,w)=>u.jsx("button",{className:"block w-full text-left text-xs text-gray-600 hover:text-green-600 py-1 px-3 rounded hover:bg-green-50 transition-colors duration-200 uppercase tracking-wide",onClick:b=>{b.stopPropagation(),i(!1),s(null),o(null),l(m.path)},children:e==="vi"?m.vi:m.en},w)):u.jsx("div",{className:"text-red-500 text-xs",children:"Không có sản phẩm"})})]},f)),u.jsx("div",{className:"flex justify-end p-2",children:u.jsx("button",{onClick:()=>s(null),className:"text-red-500 hover:text-red-700 font-bold text-xs",children:"✕ Đóng"})})]})]}):u.jsx(me,{to:y.path,className:"text-gray-800 hover:text-red-600 font-medium px-4 py-1 block text-sm",onClick:()=>i(!1),children:e==="vi"?y.vi:y.en})},v)})})})]})})]})},Mg=({language:e})=>{const t={companyName:{vi:"CÔNG TY CỔ PHẦN AN BINH FOODS",en:"AN BINH FOODS CO, LTD"},address:{vi:"Địa chỉ: Số 58, đường 3, thôn 4, Duc Hanh, Duc Linh, Bình Thuận, Việt Nam",en:"Address: No.58, 3th Street, Hamlet 4, Duc Hanh,Duc Linh, Binh Thuan, Vietnam"},phone:{vi:"Điện thoại",en:"Phone"},email:{vi:"Email",en:"Email"},website:{vi:"Website",en:"Website"},copyright:{vi:"Copyright © 2025 All Rights Reserved.",en:"Copyright © 2025 All Rights Reserved."}};return u.jsxs("footer",{className:"bg-[rgb(19,104,174)] text-black",children:[u.jsx("div",{className:"container mx-auto px-4 py",children:u.jsx("div",{className:"bg-[rgb(19,104,174))]  rounded-lg p-2 mb-2",children:u.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[u.jsx("div",{className:"md:w-1/3 flex justify-center md:justify-end md:pr-4 md:mr-10",children:u.jsx(me,{to:"/",className:"flex items-center",children:u.jsx("div",{children:u.jsx("img",{src:"/images/assets/LOGO_OFFICIAL.png",alt:"AN BINH FOODS Logo",className:"h-32 w-auto rounded-full border-4 border-white filter drop-shadow-2xl",onError:n=>{const i=n.target;i.src="/images/assets/logo-white-placeholder.svg"}})})})}),u.jsxs("div",{className:"md:w-2/4 text-center md:text-left text-white",children:[u.jsx("h3",{className:"font-bold text-lg mb-2 ",children:t.companyName[e]}),u.jsx("p",{className:"mb-1",children:t.address[e]}),u.jsxs("p",{className:"mb-1",children:[t.phone[e],": +(84)(0252) 388-8468"]}),u.jsxs("p",{className:"mb-1",children:[t.email[e],": <EMAIL>"]}),u.jsxs("p",{className:"mb-1",children:[t.website[e],": anbinhfoods.com.vn"]})]}),u.jsxs("div",{className:"md:w-1/4 flex justify-center md:justify-start space-x-4 mt-4 md:mt-0",children:[u.jsxs("a",{href:"#",className:"bg-white rounded-full p-2 text-blue-500 hover:text-blue-700",children:[u.jsx("span",{className:"sr-only",children:"Facebook"}),u.jsx("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{d:"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"})})]}),u.jsxs("a",{href:"#",className:"bg-white rounded-full p-2 text-blue-500 hover:text-blue-700",children:[u.jsx("span",{className:"sr-only",children:"Twitter"}),u.jsx("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{d:"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"})})]}),u.jsxs("a",{href:"#",className:"bg-white rounded-full p-2 text-blue-500 hover:text-blue-700",children:[u.jsx("span",{className:"sr-only",children:"YouTube"}),u.jsx("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{d:"M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"})})]})]})]})})}),u.jsx("div",{className:"bg-[rgb(16,74,128)] p-1 text-center text-xs",children:u.jsx("p",{className:"text-white",children:t.copyright[e]})})]})},Og=({email:e,phone:t})=>{const[n,i]=k.useState(!1),r=()=>{i(!n)},s=()=>{window.location.href=`mailto:${e}`},a=()=>{window.location.href=`tel:${t}`};return k.useEffect(()=>{const o=l=>{const c=l.target;n&&!c.closest(".quick-contact-container")&&i(!1)};return document.addEventListener("mousedown",o),()=>{document.removeEventListener("mousedown",o)}},[n]),u.jsxs("div",{className:"fixed right-5 bottom-5 z-50 quick-contact-container",children:[u.jsxs("div",{className:`flex flex-col-reverse items-center gap-4 mb-4 transition-all duration-300 ${n?"opacity-100 translate-y-0":"opacity-0 translate-y-10 pointer-events-none"}`,children:[u.jsxs("div",{className:"relative group flex items-center",children:[u.jsx("div",{className:"absolute right-full mr-3 bg-white text-gray-800 px-3 py-1 rounded-lg shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap",children:e}),u.jsx("button",{onClick:s,className:"w-14 h-14 rounded-full bg-gray-200 flex items-center justify-center shadow-lg hover:bg-gray-300 transition-all duration-200 transform hover:scale-110",title:`Email: ${e}`,children:u.jsx("div",{className:"w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center",children:u.jsx("div",{className:"w-10 h-10 rounded-full bg-gray-500 flex items-center justify-center",children:u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})})})})]}),u.jsxs("div",{className:"relative group flex items-center",children:[u.jsx("div",{className:"absolute right-full mr-3 bg-white text-gray-800 px-3 py-1 rounded-lg shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap",children:"+(84)(0252) 388-8468"}),u.jsx("button",{onClick:a,className:"w-14 h-14 rounded-full bg-green-200 flex items-center justify-center shadow-lg hover:bg-green-300 transition-all duration-200 transform hover:scale-110",title:`Call: ${t}`,children:u.jsx("div",{className:"w-12 h-12 rounded-full bg-green-300 flex items-center justify-center",children:u.jsx("div",{className:"w-10 h-10 rounded-full bg-green-600 flex items-center justify-center",children:u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})})})})})]})]}),u.jsx("button",{onClick:r,className:`w-16 h-16 rounded-full bg-[rgb(44,159,253)] flex items-center justify-center shadow-lg hover:bg-[rgb(44,159,253)] transition-all duration-200 transform hover:scale-105 ${n?"":"pulse-animation"}`,"aria-label":"Toggle contact options",children:u.jsx("div",{className:"w-14 h-14 rounded-full bg-[rgb(44,159,253)] flex items-center justify-center border-2 border-white",children:n?u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}):u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})})})]})},Ag=()=>{const{pathname:e}=zt();return k.useEffect(()=>{window.scrollTo(0,0)},[e]),null},zg=({language:e})=>{const[t,n]=k.useState(0),[i,r]=k.useState(null),s={title:{vi:"AN BINH FOODS CHỨNG NHẬN",en:"AN BINH FOODS CERTIFICATES"}},a=[{id:1,image:"/images/certifications/FDA.jpg",alt:"FDA"},{id:2,image:"/images/certifications/ISO.jpg",alt:"ISO Certificate"},{id:3,image:"/images/certifications/HALAL-JAKIM-2025/HALAL JAKIM 2025_page-0001.jpg",alt:"Halal Certificate"},{id:4,image:"/images/certifications/GMP.jpg",alt:"GMP Certificate"},{id:5,image:"/images/certifications/HACCP (2).jpg",alt:"HACCP Certificate"},{id:6,image:"/images/certifications/KOSHER/KOSHER_page-0001.jpg",alt:"Kosher Certificate"}],o=()=>{const g=[];for(let y=0;y<4;y++){const v=(t+y)%a.length;g.push(a[v])}return g},l=k.useCallback(()=>{n(g=>(g+1)%a.length)},[a.length]),c=k.useCallback(()=>{n(g=>(g-1+a.length)%a.length)},[a.length]),d=g=>{r(g)},h=()=>{r(null)};return k.useEffect(()=>{const g=setInterval(()=>{l()},5e3);return()=>clearInterval(g)},[l]),u.jsxs("section",{className:"py-16 bg-gray-50",children:[u.jsxs("div",{className:"container mx-auto px-4",children:[u.jsx("h2",{className:"text-2xl font-bold text-center mb-12 text-red-600 uppercase",children:s.title[e]}),u.jsxs("div",{className:"relative max-w-6xl mx-auto",children:[u.jsx("div",{className:"flex flex-wrap justify-center items-center gap-4 md:gap-8",children:o().map(g=>u.jsx("div",{className:"bg-white rounded-[20px] shadow-md overflow-hidden border border-gray-200 hover:shadow-xl transition-all duration-300 p-4 w-full max-w-[250px] h-[350px] flex items-center justify-center cursor-pointer mb-4",onClick:()=>d(g.image),children:u.jsx("img",{src:g.image,alt:g.alt,className:"w-full h-auto object-contain max-h-[320px]"})},g.id))}),u.jsx("button",{onClick:c,className:"absolute left-0 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md hover:bg-gray-100 z-10","aria-label":"Previous certificate",children:u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-gray-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),u.jsx("button",{onClick:l,className:"absolute right-0 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md hover:bg-gray-100 z-10","aria-label":"Next certificate",children:u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-gray-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})]}),i&&u.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4",onClick:h,children:u.jsxs("div",{className:"relative max-w-4xl max-h-[90vh] overflow-auto bg-white rounded-lg p-2",onClick:g=>g.stopPropagation(),children:[u.jsx("img",{src:i,alt:"Certificate",className:"w-full h-auto object-contain"}),u.jsx("button",{className:"absolute top-2 right-2 bg-white rounded-full p-2 shadow-md hover:bg-gray-100",onClick:h,children:u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-gray-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})})]})},vn=[{id:"avocado",name:{vi:"BƠ",en:"AVOCADO"},category:{id:"fresh-fruit",name:{vi:"TRÁI CÂY TƯƠI",en:"FRESH FRUIT"}},description:{vi:"Bơ là một loại trái cây có thịt dày màu xanh hoặc đen với một hạt tròn lớn ở giữa và thịt màu xanh nhạt đến vàng thường được coi là một loại rau trong ẩm thực. Nhiều nghiên cứu đã phát hiện ra rằng một chế độ ăn chủ yếu từ thực vật bao gồm các loại thực phẩm như bơ có thể giúp giảm nguy cơ béo phì, tiểu đường, bệnh tim, và tử vong.",en:"Avocado is a fruit with thick green or black skin a large round seed at the center and olive-green to yellow flesh which is often as a vegetable in cuisine. Numerous studies have found that a predominantly plant-based diet that includes foods such as avocados can help to decrease the risk of obesity, diabetes, heart disease, and overall."},images:["/images/FRESH FRUIT/avocado/4.jpg","/images/DATA_WEB/FRESH FRUIT/FRESH AVOCADO/fresavo1.png","/images/FRESH FRUIT/avocado/3.jpg","/images/DATA_WEB/FRESH FRUIT/FRESH AVOCADO/fresavo4.png"],specifications:{composition:{vi:"100% bơ tự nhiên Việt Nam",en:"100% natural Vietnam Avocado"},color:{vi:"Xanh vàng đến xanh nhạt",en:"Yellowish green to light green"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"13.9%",en:"13.9%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"6 - 7 °C",en:"6 - 7 °C"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10-20kg)",en:"Bulk packing: PE bag in carton box (10-20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 1kg, 2kg, 5kg, hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 1kg, 2kg, 5kg, or as customer's request"}},shelfLife:{vi:"Khoảng 3 đến 5 ngày",en:"About 3 to 5 days"},harvestSeason:{vi:"Tháng 5 - Tháng 9",en:"May - Sep"},nutritionFacts:{calories:103,servingSize:"50g (1/3 medium)",totalFat:10,saturatedFat:1.4,transFat:0,cholesterol:0,sodium:5,totalCarbs:5.9,dietaryFiber:4.6,sugars:.2,protein:1.3,vitaminD:0,calcium:6,iron:.4,potassium:345}},{id:"banana",name:{vi:"CHUỐI",en:"BANANA"},category:{id:"fresh-fruit",name:{vi:"TRÁI CÂY TƯƠI",en:"FRESH FRUIT"}},description:{vi:"Chuối là một loại trái cây nhiệt đới phổ biến, giàu kali, vitamin C, vitamin B6 và chất xơ. Chuối của chúng tôi được trồng tại các vùng nhiệt đới của Việt Nam, nơi có điều kiện khí hậu lý tưởng cho sự phát triển của chuối. Chúng tôi chỉ thu hoạch chuối khi đã chín tự nhiên trên cây, đảm bảo hương vị ngọt ngào và giá trị dinh dưỡng cao nhất.",en:"Banana is a popular tropical fruit, rich in potassium, vitamin C, vitamin B6, and fiber. Our bananas are grown in the tropical regions of Vietnam, where the climate conditions are ideal for banana growth. We only harvest bananas when they are naturally ripened on the tree, ensuring the sweetest flavor and highest nutritional value."},images:["/images/FRESH FRUIT/banana/banana2.jpg","/images/DATA_WEB/FRESH FRUIT/FRESH BANANA/fresba1.png","/images/FRESH FRUIT/banana/banana5.jpg","/images/DATA_WEB/FRESH FRUIT/FRESH BANANA/fresba4.png"],specifications:{composition:{vi:"100% chuối tự nhiên Việt Nam",en:"100% natural Vietnam Banana"},color:{vi:"Vàng",en:"Yellow"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"18-22%",en:"18-22%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"13 - 14 °C",en:"13 - 14 °C"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (15-18kg)",en:"Bulk packing: PE bag in carton box (15-18kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 1kg, 2kg, 5kg, hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 1kg, 2kg, 5kg, or as customer's request"}},shelfLife:{vi:"Khoảng 7 đến 10 ngày",en:"About 7 to 10 days"},harvestSeason:{vi:"Quanh năm",en:"Year-round"},nutritionFacts:{calories:89,servingSize:"100g",totalFat:.3,saturatedFat:.1,transFat:0,cholesterol:0,sodium:1,totalCarbs:22.8,dietaryFiber:2.6,sugars:12.2,protein:1.1,vitaminD:0,calcium:5,iron:.3,potassium:358}},{id:"pitaya",name:{vi:"THANH LONG",en:"DRAGON FRUIT/PITAYA"},category:{id:"fresh-fruit",name:{vi:"TRÁI CÂY TƯƠI",en:"FRESH FRUIT"}},description:{vi:"Thanh long là một loại trái cây nhiệt đới đã trở nên ngày càng phổ biến trong những năm gần đây. Thanh long, còn được gọi là pitaya hoặc lê dâu tây, là một loại trái cây nhiệt đới đẹp mắt, ngọt và giòn. Thực tế, cây mà trái này mọc ra thực sự là một loại xương rồng thuộc chi Hylocereus, bao gồm khoảng 20 loài khác nhau.",en:"Dragon fruit is a tropical fruit that has become increasingly popular in recent years. Dragon fruit, also known as pitaya or the strawberry pear, is a beautiful tropical fruit that is sweet and crunchy. The plant the fruit comes from is actually a type of cactus of the genus Hylocereus, which includes only about 20 different species."},images:["/images/FRESH FRUIT/pitaya/4.jpg","/images/FRESH FRUIT/pitaya/5.jpg","/images/DATA_WEB/FRESH FRUIT/FRESH PITAYA/frespitaya2.png","/images/DATA_WEB/FRESH FRUIT/FRESH PITAYA/frespitaya4.png"],specifications:{composition:{vi:"100% thanh long tự nhiên Việt Nam",en:"100% natural Vietnam pitaya"},color:{vi:"Hồng đến hồng tím, trắng",en:"Pink to Pink purple, white"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"13.9%",en:"13.9%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"2-5 °C",en:"2-5 °C"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10-20kg)",en:"Bulk packing: PE bag in carton box (10-20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 1kg, 2kg, 5kg, hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 1kg, 2kg, 5kg, or as customer's request"}},shelfLife:{vi:"Khoảng 4-6 tuần",en:"About 4-6 weeks"},harvestSeason:{vi:"Tháng 5 - Tháng 9",en:"May - Sep"},nutritionFacts:{calories:60,servingSize:"100g",totalFat:0,saturatedFat:0,transFat:0,cholesterol:0,sodium:0,totalCarbs:12.9,dietaryFiber:2.9,sugars:7.7,protein:1.2,vitaminD:0,calcium:18,iron:.7,potassium:0}},{id:"durian",name:{vi:"SẦU RIÊNG",en:"DURIAN"},category:{id:"fresh-fruit",name:{vi:"TRÁI CÂY TƯƠI",en:"FRESH FRUIT"}},description:{vi:'Sầu riêng là một loại trái cây có nguồn gốc từ Đông Nam Á, đặc trưng bởi mùi hương rất mạnh mà không phải ai cũng có thể chịu được nhưng thường gây nghiện. Nó rất phổ biến ở Đông Nam Á, nơi nó được mệnh danh là "vua của các loại trái cây". Sầu riêng rất giàu chất dinh dưỡng, chứa nhiều dưỡng chất hơn hầu hết các loại trái cây khác.',en:`Durian is a fruit native to Southeast Asia, characterized by a very strong aroma that not everyone can tolerate but habitually addicted. It's popular in Southeast Asia, where it's nicknamed "the king of fruits." Durian is very high in nutrients, containing more than most other fruits.`},images:["/images/FRESH FRUIT/durian/5.jpg","/images/DATA_WEB/FRESH FRUIT/FRESH DURIAN/fresdu1.png","/images/DATA_WEB/FRESH FRUIT/FRESH DURIAN/fresdu2.png","/images/DATA_WEB/FRESH FRUIT/FRESH DURIAN/fresdu4.png"],specifications:{composition:{vi:"100% sầu riêng tự nhiên Việt Nam",en:"100% natural Vietnam durian"},color:{vi:"Vàng, màu tự nhiên",en:"Yellow, natural color"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"12%",en:"12%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Khoảng 15 °C",en:"Around 15 °C"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10-20kg)",en:"Bulk packing: PE bag in carton box (10-20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 1kg, 2kg, 5kg, hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 1kg, 2kg, 5kg, or as customer's request"}},shelfLife:{vi:"Khoảng 3 tuần",en:"About 3 weeks"},harvestSeason:{vi:"Tháng 5 - Tháng 9",en:"May - Sep"},nutritionFacts:{calories:147,servingSize:"100g",totalFat:5.3,saturatedFat:0,transFat:0,cholesterol:0,sodium:2,totalCarbs:27.1,dietaryFiber:3.8,sugars:7.7,protein:1.5,vitaminD:0,calcium:0,iron:.4,potassium:436}},{id:"jackfruit",name:{vi:"MÍT",en:"JACKFRUIT"},category:{id:"fresh-fruit",name:{vi:"TRÁI CÂY TƯƠI",en:"FRESH FRUIT"}},description:{vi:"Mít là một loại cây có vỏ ngoài xanh thô ráp, bên trong có phần thịt màu vàng. Mít chín có trái to ngọt, có nhiều gai nhọn và hạt bên trong, hương thơm rất đặc trưng và hấp dẫn. Đặc biệt, mít chín có vị ngọt, ăn vào giúp giải nhiệt, giải khát, giúp tiêu hóa, giảm nhiệt âm.",en:"Jackfruit is a type of tree with a rough green outer body inside the inside of yellow, ripe jackfruit has a big sweet fruit, has many sharp spines and seeds inside, the aroma is very specific and attractive. In particular, ripe jackfruit has a sweet taste, warmth helps to quench thirst, helps waste, minus negative heat."},images:["/images/FRESH FRUIT/jackfruit/2.jpg","/images/FRESH FRUIT/jackfruit/6.jpg","/images/FRESH FRUIT/jackfruit/1.jpg","/images/FRESH FRUIT/jackfruit/4.jpg"],specifications:{composition:{vi:"100% mít tự nhiên Việt Nam",en:"100% natural Vietnam jackfruit"},color:{vi:"Vàng nhạt đến vàng sáng",en:"pale yellow to bright gold"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"18.32%",en:"18.32%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"5 – 7 °C",en:"5 – 7 °C"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10-20kg)",en:"Bulk packing: PE bag in carton box (10-20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 1kg, 2kg, 5kg, hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 1kg, 2kg, 5kg, or as customer's request"}},shelfLife:{vi:"35 ngày",en:"35 days"},harvestSeason:{vi:"Tháng 5 - Tháng 9",en:"May - Sep"},nutritionFacts:{calories:95,servingSize:"100g",totalFat:.6,saturatedFat:.2,transFat:0,cholesterol:0,sodium:2,totalCarbs:23.3,dietaryFiber:1.5,sugars:19.1,protein:1.7,vitaminD:0,calcium:24,iron:.2,potassium:448}},{id:"mango",name:{vi:"XOÀI",en:"MANGO"},category:{id:"fresh-fruit",name:{vi:"TRÁI CÂY TƯƠI",en:"FRESH FRUIT"}},description:{vi:"Xoài là một loại trái cây thơm ngon được trồng ở vùng nhiệt đới, là một trong những loại trái cây phổ biến nhất, giàu dinh dưỡng với hương vị độc đáo, mùi thơm, vị ngon và có nhiều lợi ích cho sức khỏe.",en:"Mango is a delicious fruit grown in the tropics, is one of the most popular, nutritionally rich fruits with unique flavor, fragrance, taste, and health promoting qualities."},images:["/images/FRESH FRUIT/mango/mango1.jpg","/images/DATA_WEB/FRESH FRUIT/FRESH MANGO/fresman1.png","/images/DATA_WEB/FRESH FRUIT/FRESH MANGO/fresman3.png","/images/DATA_WEB/FRESH FRUIT/FRESH MANGO/fresman4.png"],specifications:{composition:{vi:"100% xoài tự nhiên Việt Nam",en:"100% natural Vietnam mango"},color:{vi:"Vàng nhạt đến vàng sáng",en:"pale yellow to bright gold"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"14%",en:"14%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"15-18 °C",en:"15-18 °C"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10-20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 1kg, 2kg, 5kg, hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"Khoảng 35 ngày",en:"about 35 days"},harvestSeason:{vi:"Tháng 1 - Tháng 5, Tháng 9 - Tháng 12",en:"Jan – May, Sep – Dec"},nutritionFacts:{calories:60,servingSize:"100g",totalFat:.4,saturatedFat:.1,transFat:0,cholesterol:0,sodium:1,totalCarbs:15,dietaryFiber:1.6,sugars:13.7,protein:.8,vitaminD:0,calcium:11,iron:.2,potassium:168}},{id:"coconut",name:{vi:"DỪA",en:"COCONUT"},category:{id:"fresh-fruit",name:{vi:"TRÁI CÂY TƯƠI",en:"FRESH FRUIT"}},description:{vi:"Dừa là phần thịt của quả dừa, có màu trắng và là phần ăn được của quả dừa. Cơm dừa có vị thơm ngon, hơi ngọt và thường hơi béo nhưng không quá béo, có thể ăn sống hoặc chế biến thành nhiều món hấp dẫn. Ngoài ra, cơm dừa còn được dùng để chế biến thành dầu dừa, bột sữa dừa, dừa đông lạnh, dừa sấy giòn...",en:"Coconut is the flesh of the coconut, is white and is the edible part of the coconut. Copra is delicious, slightly sweet and often greasy but not too greasy, eaten raw or processed into many attractive dishes. In addition, copra is also used to process into coconut oil, coconut milk powder, frozen coconut, coconut crispy dried..."},images:["/images/DATA_WEB/FRESH FRUIT/FRESH COCONUT/fresco2.png","/images/DATA_WEB/FRESH FRUIT/FRESH COCONUT/fresco1.png","/images/DATA_WEB/FRESH FRUIT/FRESH COCONUT/fresco3.png","/images/DATA_WEB/FRESH FRUIT/FRESH COCONUT/fresco4.png"],specifications:{composition:{vi:"100% dừa tự nhiên Việt Nam",en:"100% natural Vietnam coconut"},color:{vi:"Trắng sữa, màu tự nhiên",en:"milky white, natural color"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"1.34%",en:"1.34%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"0 - 1.5 °C",en:"0 - 1.5 °C"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10-20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 1kg, 2kg, 5kg, hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"Lên đến 60 ngày",en:"up to 60 days"},harvestSeason:{vi:"Tháng 7 - Tháng 9",en:"Jul – Sep"},nutritionFacts:{calories:19,servingSize:"100g",totalFat:.2,saturatedFat:.2,transFat:0,cholesterol:0,sodium:105,totalCarbs:3.7,dietaryFiber:1.1,sugars:2.6,protein:.7,vitaminD:0,calcium:24,iron:.3,potassium:250}},{id:"pineapple",name:{vi:"DỨA",en:"PINEAPPLE"},category:{id:"fresh-fruit",name:{vi:"TRÁI CÂY TƯƠI",en:"FRESH FRUIT"}},description:{vi:"Dứa là một loại trái cây nhiệt đới thơm ngon với vị ngọt thơm và hương thơm đặc trưng. Nó chứa vitamin A, vitamin C, beta-carotene, thiamin, vitamin B5 (axit pantothenic), vitamin B6, folate, khoáng chất như kali, đồng, mangan, canxi, natri và magiê.",en:"Pineapple is a delicious tropical fruit with sweet aromatic taste and aroma. It contains vitamin A, vitamin C, beta-carotene, thiamin, vitamin B5 (pantothenic acid), vitamin B6, folate, minerals like potassium, copper, manganese, calcium, sodium, and magnesium."},images:["/images/FRESH FRUIT/pineapple/pinapple1.jpg","/images/FRESH FRUIT/pineapple/pinapple3.jpg","/images/FRESH FRUIT/pineapple/pinapple4.jpg","/images/DATA_WEB/FRESH FRUIT/FRESH PINEAPPLE/frespine4.jpg"],specifications:{composition:{vi:"100% dứa tự nhiên Việt Nam",en:"100% natural Vietnam pineapple"},color:{vi:"Vàng nhạt đến vàng",en:"pale yellow to yellow"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"15%",en:"15%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"0 - 4 °C",en:"0 - 4 °C"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10-20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 1kg, 2kg, 5kg, hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"2 tuần",en:"2 weeks"},harvestSeason:{vi:"Tháng 8 - Tháng 12",en:"Aug - Dec"},nutritionFacts:{calories:50,servingSize:"100g",totalFat:.1,saturatedFat:.1,transFat:0,cholesterol:0,sodium:1,totalCarbs:13.1,dietaryFiber:1.4,sugars:9.9,protein:.5,vitaminD:0,calcium:13,iron:.3,potassium:109}},{id:"frozen-avocado",name:{vi:"BƠ ĐÔNG LẠNH",en:"FROZEN AVOCADO"},category:{id:"frozen-fruit",name:{vi:"TRÁI CÂY ĐÔNG LẠNH",en:"FROZEN FRUIT"}},description:{vi:"Bơ là một loại trái cây với thịch xanh hoặc đen vỏ dày một hạt tròn lớn ở giữa và thịt màu xanh ô liu hoặc vàng thường được sử dụng như một loại rau trong ẩm thực.",en:"Avocado is a fruit with thich green or black skin a large round seed at the center and oilygreen or yellow flesh which is often as a vegetable in salads."},images:["/images/FROZEN FRUIT/f_avocado/avofr1.jpg","/images/FROZEN FRUIT/f_avocado/avofr2.jpg","/images/FROZEN FRUIT/f_avocado/avofr3.jpg","/images/FROZEN FRUIT/f_avocado/avofr4.jpg"],specifications:{composition:{vi:"100% bơ tự nhiên Việt Nam",en:"100% natural Vietnam Avocado"},color:{vi:"Xanh đến vàng xanh",en:"green to yellow green"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"15%",en:"15%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Đông lạnh: (-18) Celsius hoặc thấp hơn",en:"Frozen: (-18) Celsius degree or below"},packing:{bulk:{vi:"Đóng gói số lượng lớn: túi PE trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"Đông lạnh: 24 tháng",en:"Frozen: 24 months"},harvestSeason:{vi:"Tháng 5 - Tháng 9",en:"May - Sep"},nutritionFacts:{calories:160,servingSize:"100g",totalFat:14,saturatedFat:2,transFat:0,cholesterol:0,sodium:0,totalCarbs:8,dietaryFiber:6,sugars:0,protein:2,vitaminD:0,calcium:20,iron:.6,potassium:480}},{id:"frozen-banana",name:{vi:"CHUỐI ĐÔNG LẠNH",en:"FROZEN BANANA"},category:{id:"frozen-fruit",name:{vi:"TRÁI CÂY ĐÔNG LẠNH",en:"FROZEN FRUIT"}},description:{vi:"Chuối chứa nhiều loại vitamin: axit ascorbic, vitamin nhóm B, vitamin PP và carotene, enzyme và các nguyên tố vi lượng khác nhau. Chuối là nguồn kali và chất xơ quý giá.",en:"Banana contains a variety of vitamins: ascorbic acid, B group vitamins, vitamin PP and carotene, enzymes and various trace elements. Banana is a valuable source of potassium and fiber."},images:["/images/FROZEN FRUIT/f_banana/bafr1.jpg","/images/FROZEN FRUIT/f_banana/bafr2.jpg","/images/FROZEN FRUIT/f_banana/bafr3.jpg","/images/FROZEN FRUIT/f_banana/bafr4.jpg"],specifications:{composition:{vi:"100% chuối tự nhiên Việt Nam",en:"100% natural Vietnam banana"},color:{vi:"Trắng sữa, màu tự nhiên",en:"milky white, natural color"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"28 - 32%",en:"28 - 32%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Đông lạnh: (-18) Celsius hoặc thấp hơn",en:"Frozen: (-18) Celsius degree or below"},packing:{bulk:{vi:"Đóng gói số lượng lớn: túi PE trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"Đông lạnh: 24 tháng",en:"Frozen: 24 months"},harvestSeason:{vi:"Quanh năm",en:"Year-round"},nutritionFacts:{calories:89,servingSize:"100g",totalFat:.3,saturatedFat:.1,transFat:0,cholesterol:0,sodium:1,totalCarbs:22.8,dietaryFiber:2.6,sugars:0,protein:1.1,vitaminD:0,calcium:5,iron:.3,potassium:358}},{id:"frozen-pitaya",name:{vi:"THANH LONG ĐÔNG LẠNH",en:"FROZEN PITAYA"},category:{id:"frozen-fruit",name:{vi:"TRÁI CÂY ĐÔNG LẠNH",en:"FROZEN FRUIT"}},description:{vi:"Thanh long, còn được gọi là pitaya hoặc lê dâu tây, là một loại trái cây nhiệt đới đẹp mắt, ngọt và giòn. Thực tế, cây mà trái cây này mọc ra là một loại xương rồng thuộc chi Hylocereus, chỉ bao gồm khoảng 20 loài khác nhau. Ban đầu chủ yếu phổ biến ở Đông Nam Á và Mỹ Latinh, thanh long hiện được trồng và thưởng thức trên khắp thế giới.",en:"Dragon fruit, also known as pitaya or the strawberry pear, is a beautiful tropical fruit that is sweet and crunchy. The plant the fruit comes from is actually a type of cactus of the genus Hylocereus, which includes only about 20 different species. Originally mainly popular in Southeast Asia and Latin America, dragon fruit is now grown and enjoyed all over the world."},images:["/images/FROZEN FRUIT/f_piyata/pifr1.jpg","/images/FROZEN FRUIT/f_piyata/pifr2.jpg","/images/FROZEN FRUIT/f_piyata/pifr3.jpg","/images/FROZEN FRUIT/f_piyata/pifr4.jpg"],specifications:{composition:{vi:"100% thanh long tự nhiên Việt Nam",en:"100% natural Vietnam pitaya"},color:{vi:"Hồng đến tím hồng, trắng",en:"Pink to pink purple, white"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"15%",en:"15%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Đông lạnh: (-18) Celsius hoặc thấp hơn",en:"Frozen: (-18) Celsius degree or below"},packing:{bulk:{vi:"Đóng gói số lượng lớn: túi PE trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"Đông lạnh: 24 tháng",en:"Frozen: 24 months"},size:{vi:"Miếng, khối, lát, nửa quả, dạng nhuyễn, nguyên quả",en:"Chunks, dices, slices, halves, puree, whole"},harvestSeason:{vi:"Quanh năm",en:"Year-round"},nutritionFacts:{calories:60,servingSize:"100g",totalFat:0,saturatedFat:0,transFat:0,cholesterol:0,sodium:0,totalCarbs:12.9,dietaryFiber:2.9,sugars:7.7,protein:1.2,vitaminD:0,calcium:18,iron:.7,potassium:0}},{id:"frozen-durian",name:{vi:"SẦU RIÊNG ĐÔNG LẠNH",en:"FROZEN DURIAN"},category:{id:"frozen-fruit",name:{vi:"TRÁI CÂY ĐÔNG LẠNH",en:"FROZEN FRUIT"}},description:{vi:"Sầu riêng là một loại trái cây có nguồn gốc từ Đông Nam Á, đặc trưng bởi mùi hương rất mạnh mà không phải ai cũng có thể chịu được nhưng thường gây nghiện.",en:"Durian is a fruit native to Southeast Asia, characterized by a very strong aroma that not everyone can tolerate but habitually addicted."},images:["/images/FROZEN FRUIT/f_durian/dufr1.jpg","/images/FROZEN FRUIT/f_durian/dufr2.jpg","/images/FROZEN FRUIT/f_durian/dufr3.jpg","/images/FROZEN FRUIT/f_durian/dufr4.jpg"],specifications:{composition:{vi:"100% sầu riêng tự nhiên Việt Nam",en:"100% natural Vietnam durian"},color:{vi:"Vàng, màu tự nhiên",en:"Yellow, natural color"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"30-36%",en:"30-36%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Đông lạnh: (-18) Celsius hoặc thấp hơn",en:"Frozen: (-18) Celsius degree or below"},packing:{bulk:{vi:"Đóng gói số lượng lớn: túi PE trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"Đông lạnh: 24 tháng",en:"Frozen: 24 months"},size:{vi:"Miếng, khối, dạng nhuyễn, lát, một phần tư, nửa quả, v.v.",en:"Chunks, dices, puree, slices, quarter, halves, etc"},harvestSeason:{vi:"Tháng 5 - Tháng 9",en:"May - Sep"},nutritionFacts:{calories:147,servingSize:"100g",totalFat:5.3,saturatedFat:0,transFat:0,cholesterol:0,sodium:2,totalCarbs:27.1,dietaryFiber:3.8,sugars:0,protein:1.5,vitaminD:0,calcium:6,iron:.4,potassium:436}},{id:"frozen-jackfruit",name:{vi:"MÍT ĐÔNG LẠNH",en:"FROZEN JACKFRUIT"},category:{id:"frozen-fruit",name:{vi:"TRÁI CÂY ĐÔNG LẠNH",en:"FROZEN FRUIT"}},description:{vi:"Mít là một loại cây có vỏ ngoài xanh thô ráp, bên trong có phần thịt màu vàng. Mít chín có trái ngọt lớn, bên trong có nhiều gai nhọn và hạt, mùi hương rất đặc trưng và hấp dẫn. Đặc biệt, mít chín có vị ngọt, ấm giúp giải khát, giúp tiêu hóa, giảm nhiệt âm.",en:"Jackfruit is a type of tree with a rough green outer body inside the inside of yellow, ripe jackfruit has a big sweet fruit, has many sharp spines and seeds inside, the aroma is very specific and attractive. In particular, ripe jackfruit has a sweet taste, warmth helps to quench thirst, helps waste, minus negative heat."},images:["/images/FROZEN FRUIT/f_jackfruit/jafr1.jpg","/images/FROZEN FRUIT/f_jackfruit/jafr2.jpg","/images/FROZEN FRUIT/f_jackfruit/jafr3.jpg","/images/FROZEN FRUIT/f_jackfruit/jafr4.jpg"],specifications:{composition:{vi:"100% mít tự nhiên Việt Nam",en:"100% natural Vietnam jackfruit"},color:{vi:"Vàng nhạt đến vàng sáng",en:"Pale yellow to bright gold"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"18.37%",en:"18.37%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Đông lạnh: (-18) Celsius hoặc thấp hơn",en:"Frozen: (-18) Celsius degree or below"},packing:{bulk:{vi:"Đóng gói số lượng lớn: túi PE trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"Đông lạnh: 24 tháng",en:"Frozen: 24 months"},size:{vi:"Nửa quả, nguyên quả",en:"Halves, whole"},harvestSeason:{vi:"Tháng 1, Tháng 2, Tháng 5 - Tháng 7, Tháng 12",en:"Jan, Feb, May - July, Dec"},nutritionFacts:{calories:95,servingSize:"100g",totalFat:.6,saturatedFat:.2,transFat:0,cholesterol:0,sodium:2,totalCarbs:23.3,dietaryFiber:1.5,sugars:19.1,protein:1.7,vitaminD:0,calcium:24,iron:.2,potassium:448}},{id:"frozen-mango",name:{vi:"XOÀI ĐÔNG LẠNH",en:"FROZEN MANGO"},category:{id:"frozen-fruit",name:{vi:"TRÁI CÂY ĐÔNG LẠNH",en:"FROZEN FRUIT"}},description:{vi:"Xoài là một loại trái cây thơm ngon được trồng ở vùng nhiệt đới, là một trong những loại trái cây phổ biến nhất, giàu dinh dưỡng với hương vị độc đáo, mùi thơm, vị ngon và có nhiều lợi ích cho sức khỏe.",en:"Mango is a delicious fruit grown in the tropics, is one of the most popular, nutritionally rich fruits with unique flavor, fragrance, taste, and heath promoting qualities."},images:["/images/FROZEN FRUIT/f_mango/mafr1.jpg","/images/FROZEN FRUIT/f_mango/mafr2.jpg","/images/FROZEN FRUIT/f_mango/mafr3.jpg","/images/FROZEN FRUIT/f_mango/mafr4.jpg"],specifications:{composition:{vi:"100% xoài tự nhiên Việt Nam",en:"100% natural Vietnam Mango"},color:{vi:"Vàng nhạt đến vàng sáng, tự nhiên",en:"pale yellow to bright gold, natural"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"12 - 17%",en:"12 - 17%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Đông lạnh: (-18) Celsius hoặc thấp hơn",en:"Frozen: (-18) Celsius degree or below"},packing:{bulk:{vi:"Đóng gói số lượng lớn: túi PE trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"Đông lạnh: 24 tháng",en:"Frozen: 24 months"},size:{vi:"Miếng, khối, lát, nửa quả, dạng nhuyễn, v.v.",en:"chunks, dices, slices, halves, whole, puree, etc"},harvestSeason:{vi:"Tháng 1 - Tháng 5, Tháng 9 - Tháng 12",en:"Jan – May, Sep – Dec"},nutritionFacts:{calories:68,servingSize:"100g",totalFat:.4,saturatedFat:0,transFat:0,cholesterol:0,sodium:0,totalCarbs:15.2,dietaryFiber:1.5,sugars:13.6,protein:.8,vitaminD:0,calcium:11,iron:.2,potassium:0}},{id:"frozen-coconut-meat",name:{vi:"CƠM DỪA ĐÔNG LẠNH",en:"FROZEN COCONUT MEAT"},category:{id:"frozen-fruit",name:{vi:"TRÁI CÂY ĐÔNG LẠNH",en:"FROZEN FRUIT"}},description:{vi:"Cơm dừa là phần thịt của quả dừa, có màu trắng và là phần ăn được của quả dừa. Cơm dừa thơm ngon, hơi ngọt và thường béo nhưng không quá béo, có thể ăn sống hoặc chế biến thành nhiều món hấp dẫn. Ngoài ra, cơm dừa còn được dùng để chế biến thành dầu dừa, bột sữa dừa, dừa đông lạnh, dừa sấy giòn...",en:"Coconut is the flesh of the coconut, is white and is the edible part of the coconut. Copra is delicious, slightly sweet and often greasy but not too greasy, eaten raw or processed into many attractive dishes. In addition, copra is also used to process into coconut oil, coconut milk powder, frozen coconut, coconut crispy dried..."},images:["/images/FROZEN FRUIT/f_coconut/cofr1.jpg","/images/FROZEN FRUIT/f_coconut/cofr2.jpg","/images/FROZEN FRUIT/f_coconut/cofr3.jpg","/images/FROZEN FRUIT/f_coconut/cofr4.jpg"],specifications:{composition:{vi:"100% dừa tự nhiên Việt Nam",en:"100% natural Vietnam coconut"},color:{vi:"Trắng sữa, màu tự nhiên",en:"milky white, natural color"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"10% min.",en:"10% min."},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Đông lạnh: (-18) Celsius hoặc thấp hơn",en:"Frozen: (-18) Celsius degree or below"},packing:{bulk:{vi:"Đóng gói số lượng lớn: túi PE trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"Đông lạnh: 24 tháng",en:"Frozen: 24 months"},size:{vi:"Lát, dạng nhuyễn, miếng, khối, lát, nửa quả, dạng nhuyễn",en:"slices, puree, chunks, dices, slices, halves, puree"},harvestSeason:{vi:"Quanh năm",en:"Year-round"},nutritionFacts:{calories:354,servingSize:"100g",totalFat:33.5,saturatedFat:29.7,transFat:0,cholesterol:0,sodium:20,totalCarbs:15.2,dietaryFiber:9,sugars:6.2,protein:3.3,vitaminD:0,calcium:14,iron:2.4,potassium:356}},{id:"frozen-pineapple",name:{vi:"KHÓM ĐÔNG LẠNH",en:"FROZEN PINEAPPLE"},category:{id:"frozen-fruit",name:{vi:"TRÁI CÂY ĐÔNG LẠNH",en:"FROZEN FRUIT"}},description:{vi:"Dứa là một loại trái cây nhiệt đới thơm ngon với vị ngọt thơm và hương thơm. Nó chứa vitamin A, vitamin C, beta-carotene, thiamin, vitamin B5 (axit pantothenic), vitamin B6, folate, khoáng chất như kali, đồng, mangan, canxi, natri và magiê.",en:"Pineapple is a delicious tropical fruit with sweet aromatic taste and aroma. It contains vitamin A, vitamin C, beta-carotene, thiamin, vitamin B5 (pantothenic acid), vitamin B6, folate, minerals like potassium, copper, manganese, calcium, sodium, and magnesium."},images:["/images/FROZEN FRUIT/f_pineapple/pifr1.jpg","/images/FROZEN FRUIT/f_pineapple/pifr2.jpg","/images/FROZEN FRUIT/f_pineapple/pifr3.jpg","/images/FROZEN FRUIT/f_pineapple/pifr4.jpg"],specifications:{composition:{vi:"100% dứa tự nhiên Việt Nam",en:"100% natural Vietnam pineapple"},color:{vi:"Vàng nhạt đến vàng",en:"pale yellow to yellow"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"60-65°",en:"60-65°"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Đông lạnh: (-18) Celsius hoặc thấp hơn",en:"Frozen: (-18) Celsius degree or below"},packing:{bulk:{vi:"Đóng gói số lượng lớn: túi PE trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"Đông lạnh: 24 tháng",en:"Frozen: 24 months"},size:{vi:"Miếng, khối, dạng nhuyễn, lát, một phần tư, nửa quả, v.v.",en:"chunks, dices, puree, slices, quarter, halves, etc"},harvestSeason:{vi:"Tháng 8 - Tháng 12",en:"Aug - Dec"},nutritionFacts:{calories:86,servingSize:"100g",totalFat:.1,saturatedFat:0,transFat:0,cholesterol:0,sodium:2,totalCarbs:22.2,dietaryFiber:1.1,sugars:21.1,protein:.4,vitaminD:0,calcium:9,iron:.4,potassium:100}},{id:"dried-banana",name:{vi:"CHUỐI SẤY KHÔ",en:"DRIED BANANA"},category:{id:"dried-fruit",name:{vi:"TRÁI CÂY SẤY KHÔ",en:"DRIED FRUIT"}},description:{vi:"Chuối sấy khô của chúng tôi được làm từ những quả chuối chín tự nhiên, được chọn lọc kỹ càng. Sau khi thu hoạch, chuối được rửa sạch, gọt vỏ, cắt thành lát mỏng và sấy khô ở nhiệt độ thấp để giữ nguyên hương vị tự nhiên và các chất dinh dưỡng. Sản phẩm chuối sấy khô của chúng tôi không chứa đường, không chất bảo quản, là món ăn vặt lành mạnh và bổ dưỡng.",en:"Our dried bananas are made from naturally ripened bananas, carefully selected. After harvesting, bananas are washed, peeled, sliced, and dried at low temperatures to preserve the natural flavor and nutrients. Our dried banana products contain no added sugar, no preservatives, and are a healthy and nutritious snack."},images:["/images/DRIED FRUIT/d_banana/badr1.jpg","/images/DRIED FRUIT/d_banana/badr2.jpg","/images/DRIED FRUIT/d_banana/badr3.jpg","/images/DRIED FRUIT/d_banana/badr4.jpg"],specifications:{composition:{vi:"100% chuối tự nhiên Việt Nam",en:"100% natural Vietnam Banana"},color:{vi:"Vàng đến nâu vàng",en:"Yellow to golden brown"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"65-70%",en:"65-70%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Nơi khô ráo, thoáng mát, tránh ánh nắng trực tiếp",en:"Dry, cool place, away from direct sunlight"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (5kg, 10kg)",en:"Bulk packing: PE bag in carton box (5kg, 10kg)"},retail:{vi:"Đóng gói bán lẻ: túi zip: 100g, 200g, 500g, hoặc theo yêu cầu của khách hàng",en:"Retail packing: zip bag: 100g, 200g, 500g, or as customer's request"}},shelfLife:{vi:"12 tháng ở điều kiện bảo quản thích hợp",en:"12 months under proper storage conditions"},harvestSeason:{vi:"Sản xuất quanh năm",en:"Produced year-round"},nutritionFacts:{calories:346,servingSize:"100g",totalFat:1.8,saturatedFat:.6,transFat:0,cholesterol:0,sodium:3,totalCarbs:88.3,dietaryFiber:9.9,sugars:47.3,protein:3.9,vitaminD:0,calcium:22,iron:1.2,potassium:1491}},{id:"dried-jackfruit",name:{vi:"MÍT SẤY KHÔ",en:"DRIED JACKFRUIT"},category:{id:"dried-fruit",name:{vi:"TRÁI CÂY SẤY KHÔ",en:"DRIED FRUIT"}},description:{vi:"Mít là một loại cây có vỏ ngoài xanh thô ráp, bên trong có phần thịt màu vàng. Mít chín có trái to ngọt, có nhiều gai nhọn và hạt bên trong, hương thơm rất đặc trưng và hấp dẫn. Đặc biệt, mít chín có vị ngọt, ăn vào giúp giải nhiệt, giải khát, giúp tiêu hóa, giảm nhiệt âm.",en:"Jackfruit is a type of tree with a rough green outer body inside the inside of yellow, ripe jackfruit has a big sweet fruit, has many sharp spines and seeds inside, the aroma is very specific and attractive. In particular, ripe jackfruit has a sweet taste, warmth helps to quench thirst, helps waste, minus negative heat."},images:["/images/DRIED FRUIT/d_jackfruit/jadr1.jpg","/images/DRIED FRUIT/d_jackfruit/jadr2.jpg","/images/DRIED FRUIT/d_jackfruit/jadr3.jpg","/images/DRIED FRUIT/d_jackfruit/jadr4.jpg"],specifications:{composition:{vi:"100% mít tự nhiên Việt Nam",en:"100% natural Vietnam jackfruit"},color:{vi:"Vàng nhạt đến vàng sáng, tự nhiên",en:"pale yellow to bright gold, natural"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},moisture:{vi:"21.3%",en:"21.3%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Nơi khô ráo, thoáng mát",en:"Store in cool and dried place"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"12 tháng",en:"12 months"},size:{vi:"Nửa quả, nguyên quả",en:"halves, whole"},harvestSeason:{vi:"Tháng 1, Tháng 2, Tháng 5 - Tháng 7, Tháng 12",en:"Jan, Feb, May - July, Dec"},nutritionFacts:{calories:325,servingSize:"100g",totalFat:0,saturatedFat:0,transFat:0,cholesterol:0,sodium:200,totalCarbs:80,dietaryFiber:5,sugars:55,protein:0,vitaminD:0,calcium:50,iron:1.8,potassium:525}},{id:"dried-mango",name:{vi:"XOÀI SẤY KHÔ",en:"DRIED MANGO"},category:{id:"dried-fruit",name:{vi:"TRÁI CÂY SẤY KHÔ",en:"DRIED FRUIT"}},description:{vi:"Xoài là một loại trái cây thơm ngon được trồng ở vùng nhiệt đới, là một trong những loại trái cây phổ biến nhất, giàu dinh dưỡng với hương vị độc đáo, mùi thơm, vị ngon và có nhiều lợi ích cho sức khỏe.",en:"Mango, being a delicious fruit grown in the tropics, is one of the most popular, nutritionally rich fruits with unique flavor, fragrance, taste, and heath promoting qualities."},images:["/images/DRIED FRUIT/d_mango/madr1.jpg","/images/DRIED FRUIT/d_mango/madr2.jpg","/images/DRIED FRUIT/d_mango/madr3.jpg","/images/DRIED FRUIT/d_mango/madr4.jpg"],specifications:{composition:{vi:"100% xoài tự nhiên Việt Nam",en:"100% natural Vietnam Mango"},color:{vi:"Vàng nhạt đến vàng sáng, tự nhiên",en:"pale yellow to bright gold, natural"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},moisture:{vi:"10 - 15%",en:"10 - 15%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Nơi khô ráo, thoáng mát",en:"Store in cool and dried place"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 200gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 200gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"12 tháng",en:"12 months"},size:{vi:"Miếng, khối, lát, nửa quả, bột",en:"chunks, dices, slices, halves, powder"},harvestSeason:{vi:"Tháng 1 - Tháng 5, Tháng 9 - Tháng 12",en:"Jan – May, Sep – Dec"},nutritionFacts:{calories:350,servingSize:"100g",totalFat:0,saturatedFat:0,transFat:0,cholesterol:0,sodium:138,totalCarbs:80,dietaryFiber:2.5,sugars:65,protein:5,vitaminD:0,calcium:50,iron:.9,potassium:0}},{id:"dried-cashew-nut",name:{vi:"HẠT ĐIỀU SẤY KHÔ",en:"DRIED CASHEW NUT"},category:{id:"dried-fruit",name:{vi:"TRÁI CÂY SẤY KHÔ",en:"DRIED FRUIT"}},description:{vi:"Hạt điều là một loại hạt có độ mềm vừa phải và hương vị ngọt. Chúng có nguồn gốc từ Nam Mỹ, cụ thể là Brazil, và được thực dân đưa đến châu Phi và Ấn Độ. Những khu vực này là những nhà sản xuất hạt điều lớn nhất hiện nay. Hạt điều được bán dưới dạng sống hoặc rang, có muối hoặc không muối.",en:"Cashews are a type of nut with a soft consistency and sweet flavor. They are native to South America, specifically Brazil, and were introduced by colonists to Africa and India. These regions are the largest producers of cashews today. Cashews are sold both raw or roasted, and salted or unsalted."},images:["/images/DRIED FRUIT/d_cashew/cadr1.jpg","/images/DRIED FRUIT/d_cashew/cadr2.jpg","/images/DRIED FRUIT/d_cashew/cadr3.jpg","/images/DRIED FRUIT/d_cashew/cadr4.jpg"],specifications:{composition:{vi:"100% hạt điều tự nhiên Việt Nam",en:"100% natural Vietnam cashew nut"},color:{vi:"Vàng nhạt đến vàng sáng, tự nhiên",en:"pale yellow to bright gold, natural"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},moisture:{vi:"10 % max",en:"10 % max"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Nơi khô ráo, thoáng mát",en:"Store in cool and dried place"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 200gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 200gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"12 tháng",en:"12 months"},size:{vi:"nguyên hạt, nửa hạt",en:"halves, whole"},harvestSeason:{vi:"Tháng 1 - Tháng 4",en:"Jan – Apr"},nutritionFacts:{calories:553,servingSize:"100g",totalFat:43.9,saturatedFat:7.8,transFat:0,cholesterol:0,sodium:12,totalCarbs:30.2,dietaryFiber:3.3,sugars:5.9,protein:18.2,vitaminD:0,calcium:37,iron:6.7,potassium:660}},{id:"dried-carrots",name:{vi:"CÀ RỐT SẤY KHÔ",en:"DRIED CARROTS"},category:{id:"dried-fruit",name:{vi:"TRÁI CÂY SẤY KHÔ",en:"DRIED FRUIT"}},description:{vi:"Cà rốt là một loại rễ dài, nhọn màu cam được ăn như một loại rau, một loại rau củ thường được coi là thực phẩm tốt nhất cho sức khỏe. Cà rốt chứa nhiều loại chất dinh dưỡng và chất chống oxy hóa, cùng với vitamin C, giúp tăng cường hệ miễn dịch của bạn. Ăn cà rốt thường xuyên tạo ra một lá chắn bảo vệ cơ thể bạn khỏi vi trùng.",en:"The carrot is a long pointed orange root eaten as a vegetable, a root vegetable often claimed to be the perfect health food. Carrots contain a variety of nutrients and antioxidants, along with vitamin C, that will boost your immune system. Eating carrots regularly creates a protective shield for your body from germs."},images:["/images/DRIED FRUIT/d_carrots/cardr1.jpg","/images/DRIED FRUIT/d_carrots/cardr2.jpg","/images/DRIED FRUIT/d_carrots/cardr3.jpg","/images/DRIED FRUIT/d_carrots/cardr4.jpg"],specifications:{composition:{vi:"100% cà rốt tự nhiên Việt Nam",en:"100% natural Vietnam carrots"},color:{vi:"Cam, màu tự nhiên",en:"Orange, natural"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},moisture:{vi:"5.21% max.",en:"5.21% max."},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Nơi khô ráo, thoáng mát",en:"Store in cool and dried place"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"12 tháng",en:"12 months"},size:{vi:"miếng, lát",en:"chunks, slices"},harvestSeason:{vi:"Quanh năm",en:"Year-round"},nutritionFacts:{calories:341,servingSize:"100g",totalFat:1.5,saturatedFat:.3,transFat:0,cholesterol:0,sodium:275,totalCarbs:80,dietaryFiber:24,sugars:39,protein:8.1,vitaminD:0,calcium:212,iron:3.93,potassium:2540}},{id:"dried-lotus-seed",name:{vi:"HẠT SEN SẤY KHÔ",en:"DRIED LOTUS SEED"},category:{id:"dried-fruit",name:{vi:"TRÁI CÂY SẤY KHÔ",en:"DRIED FRUIT"}},description:{vi:"Hạt sen hay còn gọi là hạt sen là hạt của cây thuộc chi Nelumbo, đặc biệt là loài Nelumbo nucifera. Hạt được sử dụng trong ẩm thực châu Á và y học truyền thống. Chủ yếu được bán dưới dạng sấy khô, đã bóc vỏ. Hạt sen chứa hàm lượng phong phú protein, vitamin B và khoáng chất thiết yếu.",en:"A lotus seed or lotus nut is the seed of a plant in the genus Nelumbo, particularly the species Nelumbo nucifera. The seeds are used in Asian cuisine and traditional medicine. Mostly sold in dried, shelled form. The seeds contain rich contents of protein, B vitamins and dietary minerals."},images:["/images/DRIED FRUIT/d_lotus/lodr1.jpg","/images/DRIED FRUIT/d_lotus/lodr2.jpg","/images/DRIED FRUIT/d_lotus/lodr3.jpg","/images/DRIED FRUIT/d_lotus/lodr4.jpg"],specifications:{composition:{vi:"100% hạt sen tự nhiên Việt Nam",en:"100% natural Vietnam lotus seeds"},color:{vi:"Vàng nhạt",en:"Light yellow"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},moisture:{vi:"7 - 8% max",en:"7 - 8% max"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Nơi khô ráo, thoáng mát",en:"Store in cool and dried place"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 200gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 200gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"12 tháng",en:"12 months"},size:{vi:"nguyên hạt, nửa hạt",en:"halves, whole"},harvestSeason:{vi:"Tháng 5 - Tháng 7",en:"May - Jul"},nutritionFacts:{calories:332,servingSize:"100g",totalFat:2,saturatedFat:.3,transFat:0,cholesterol:0,sodium:5,totalCarbs:64.5,dietaryFiber:0,sugars:0,protein:15.4,vitaminD:0,calcium:163,iron:3.5,potassium:1368}},{id:"dried-coconut",name:{vi:"DỪA SẤY KHÔ",en:"DRIED COCONUT"},category:{id:"dried-fruit",name:{vi:"TRÁI CÂY SẤY KHÔ",en:"DRIED FRUIT"}},description:{vi:"Dừa là phần thịt của quả dừa, có màu trắng và là phần ăn được của quả dừa. Cơm dừa có vị thơm ngon, hơi ngọt và thường béo nhưng không quá béo, có thể ăn sống hoặc chế biến thành nhiều món hấp dẫn. Ngoài ra, cơm dừa còn được sử dụng để chế biến thành dầu dừa, bột sữa dừa, dừa đông lạnh, dừa sấy giòn...",en:"Coconut is the flesh of the coconut, is white and is the edible part of the coconut. Copra is delicious, slightly sweet and often greasy but not too greasy, eaten raw or processed into many attractive dishes. In addition, copra is also used to process into coconut oil, coconut milk powder, frozen coconut, coconut crispy dried..."},images:["/images/DRIED FRUIT/d_coconut/codr1.jpg","/images/DRIED FRUIT/d_coconut/codr2.jpg","/images/DRIED FRUIT/d_coconut/codr3.jpg","/images/DRIED FRUIT/d_coconut/codr4.jpg"],specifications:{composition:{vi:"100% dừa tự nhiên Việt Nam",en:"100% natural Vietnam coconut"},color:{vi:"Cam, màu tự nhiên",en:"Orange, natural"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},moisture:{vi:"5.21% max.",en:"5.21% max."},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Nơi khô ráo, thoáng mát",en:"Store in cool and dried place"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"12 tháng",en:"12 months"},size:{vi:"miếng, lát",en:"chunks, slices"},harvestSeason:{vi:"Quanh năm",en:"Year-round"},nutritionFacts:{calories:592,servingSize:"100g",totalFat:47,saturatedFat:41.7,transFat:0,cholesterol:0,sodium:37,totalCarbs:44.4,dietaryFiber:0,sugars:0,protein:5.3,vitaminD:0,calcium:27,iron:3.4,potassium:534,phosphorus:211}},{id:"dried-sweet-potato",name:{vi:"KHOAI LANG SẤY KHÔ",en:"DRIED SWEET POTATO"},category:{id:"dried-fruit",name:{vi:"TRÁI CÂY SẤY KHÔ",en:"DRIED FRUIT"}},description:{vi:'Khoai lang là một loại cây hai lá mầm thuộc họ bìm bìm hoặc họ rau muống. Thân, lá, củ to, nhiều tinh bột, vị ngọt là một loại rau củ. Lá non và ngọn đôi khi được ăn như rau xanh. Khoai lang thường được coi là một loại khoai tây nhưng không thuộc họ cà, Solanaceae, mặc dù cả hai họ đều thuộc cùng một bậc phân loại, Solanales. Khoai lang, đặc biệt là giống màu cam, thường được gọi là "khoai mỡ" ở một số vùng của Bắc Mỹ, nhưng về mặt thực vật học rất khác biệt so với khoai mỡ thực sự.',en:'The sweet potato is a dicotyledonous plant that belongs to the bindweed or morning glory family. Its large, starchy, sweet-tasting, tuberous roots are a root vegetable.The young leaves and shoots are sometimes eaten as greens. The sweet potato is commonly thought to be a type of potato but does not belong to the nightshade family, Solanaceae, but both families belong to the same taxonomic order, the Solanales. The sweet potato, especially the orange variety, is often called a "yam" in parts of North America, but is botanically very distinct from true yams.'},images:["/images/DRIED FRUIT/d_sweet/swdr1.jpg","/images/DRIED FRUIT/d_sweet/swdr2.jpg","/images/DRIED FRUIT/d_sweet/swdr3.jpg","/images/DRIED FRUIT/d_sweet/swdr4.jpg"],specifications:{composition:{vi:"100% khoai lang tự nhiên Việt Nam",en:"100% natural Vietnam sweet potato"},color:{vi:"Màu tím hoặc vàng, màu tự nhiên",en:"Purple or yellow color, natural color"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},moisture:{vi:"15%",en:"15%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Nơi khô ráo, thoáng mát",en:"Store in cool and dried place"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"12 tháng",en:"12 months"},size:{vi:"miếng, hạt lựu, lát, nửa, bột, nguyên củ",en:"chunks, dices, slices, halves, powder, whole"},harvestSeason:{vi:"Quanh năm",en:"Year-round"},nutritionFacts:{calories:86,servingSize:"100g",totalFat:.1,saturatedFat:0,transFat:0,cholesterol:0,sodium:55,totalCarbs:20.1,dietaryFiber:3,sugars:4.2,protein:1.6,vitaminD:0,calcium:30,iron:.7,potassium:337,phosphorus:47,vitaminC:2.4}},{id:"fd-avocado",name:{vi:"BƠ ĐÔNG KHÔ",en:"FD AVOCADO"},category:{id:"freeze-dried-fruit",name:{vi:"TRÁI CÂY ĐÔNG KHÔ",en:"FREEZE DRIED FRUIT"}},description:{vi:"Bơ đông khô của chúng tôi được sản xuất bằng công nghệ đông khô tiên tiến, giúp giữ nguyên hương vị, màu sắc và giá trị dinh dưỡng của bơ tươi. Quá trình đông khô bao gồm việc đông lạnh nhanh bơ tươi ở nhiệt độ cực thấp, sau đó loại bỏ nước bằng cách thăng hoa trong môi trường chân không. Sản phẩm bơ đông khô của chúng tôi có thể được bảo quản lâu dài mà không cần chất bảo quản, và có thể dễ dàng hoàn nguyên bằng cách thêm nước.",en:"Our freeze-dried avocados are produced using advanced freeze-drying technology, which helps preserve the flavor, color, and nutritional value of fresh avocados. The freeze-drying process includes quick freezing of fresh avocados at extremely low temperatures, then removing water by sublimation in a vacuum environment. Our freeze-dried avocado products can be stored for a long time without preservatives and can be easily rehydrated by adding water."},images:["/images/FREEZE DRIED FRUIT/fd_avocado/avofd1.jpg","/images/FREEZE DRIED FRUIT/fd_avocado/avofd2.jpg","/images/FREEZE DRIED FRUIT/fd_avocado/avofd3.jpg","/images/FREEZE DRIED FRUIT/fd_avocado/avofd4.jpg"],specifications:{composition:{vi:"100% bơ tự nhiên Việt Nam",en:"100% natural Vietnam Avocado"},color:{vi:"Xanh vàng đến xanh nhạt",en:"Yellowish green to light green"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"N/A",en:"N/A"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Nơi khô ráo, thoáng mát, tránh ánh nắng trực tiếp",en:"Dry, cool place, away from direct sunlight"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (2kg, 5kg)",en:"Bulk packing: PE bag in carton box (2kg, 5kg)"},retail:{vi:"Đóng gói bán lẻ: túi zip hoặc hộp: 50g, 100g, 200g, hoặc theo yêu cầu của khách hàng",en:"Retail packing: zip bag or box: 50g, 100g, 200g, or as customer's request"}},shelfLife:{vi:"24 tháng ở điều kiện bảo quản thích hợp",en:"24 months under proper storage conditions"},harvestSeason:{vi:"Sản xuất quanh năm",en:"Produced year-round"},nutritionFacts:{calories:515,servingSize:"100g",totalFat:50,saturatedFat:7,transFat:0,cholesterol:0,sodium:25,totalCarbs:29.5,dietaryFiber:23,sugars:1,protein:6.5,vitaminD:0,calcium:30,iron:2,potassium:1725}},{id:"fd-banana",name:{vi:"CHUỐI ĐÔNG KHÔ",en:"FD BANANA"},category:{id:"freeze-dried-fruit",name:{vi:"TRÁI CÂY ĐÔNG KHÔ",en:"FREEZE DRIED FRUIT"}},description:{vi:"Chuối chứa nhiều loại vitamin: axit ascorbic, vitamin nhóm B, vitamin PP và carotene, enzyme và các nguyên tố vi lượng khác nhau. Chuối là nguồn kali và chất xơ quý giá.",en:"Banana contains a variety of vitamins: ascorbic acid, B group vitamins, vitamin PP and carotene, enzymes and various trace elements. Banana is a valuable source of potassium and fiber."},images:["/images/FREEZE DRIED FRUIT/fd_banana/bafd1.jpg","/images/FREEZE DRIED FRUIT/fd_banana/bafd2.jpg","/images/FREEZE DRIED FRUIT/fd_banana/bafd3.jpg","/images/FREEZE DRIED FRUIT/fd_banana/bafd4.jpg"],specifications:{composition:{vi:"100% chuối tự nhiên Việt Nam",en:"100% natural Vietnam banana"},color:{vi:"Trắng sữa, màu tự nhiên",en:"milky white, natural color"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"16%",en:"16%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"}},storageConditions:{vi:"Nơi khô ráo, thoáng mát",en:"In normal temperature"},packing:{bulk:{vi:"Đóng gói số lượng lớn: túi PE trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"18 tháng kể từ ngày sản xuất",en:"18 month from MF date"},size:{vi:"miếng, hạt lựu, lát, bột",en:"chunks, dices, slices, powder"},harvestSeason:{vi:"Tháng 5 - Tháng 9",en:"May - Sep"},nutritionFacts:{calories:400,servingSize:"100g",totalFat:0,saturatedFat:0,transFat:0,cholesterol:0,sodium:0,totalCarbs:90,dietaryFiber:10,sugars:50,protein:5,vitaminD:0,iron:0,calcium:0,potassium:1450}},{id:"fd-pitaya",name:{vi:"THANH LONG ĐÔNG KHÔ",en:"FD PITAYA"},category:{id:"freeze-dried-fruit",name:{vi:"TRÁI CÂY ĐÔNG KHÔ",en:"FREEZE DRIED FRUIT"}},description:{vi:"Thanh long, còn được gọi là pitaya hoặc lê dâu tây, là một loại trái cây nhiệt đới đẹp mắt, ngọt và giòn. Thực tế, cây mà trái này mọc ra thực sự là một loại xương rồng thuộc chi Hylocereus, bao gồm khoảng 20 loài khác nhau.",en:"Dragon fruit, also known as pitaya or the strawberry pear, is a beautiful tropical fruit that is sweet and crunchy. The plant the fruit comes from is actually a type of cactus of the genus Hylocereus, which includes only about 20 different species."},images:["/images/FREEZE DRIED FRUIT/fd_pitaya/pifd1.jpg","/images/FREEZE DRIED FRUIT/fd_pitaya/pifd2.jpg","/images/FREEZE DRIED FRUIT/fd_pitaya/pifd3.jpg","/images/FREEZE DRIED FRUIT/fd_pitaya/pifd4.jpg"],specifications:{composition:{vi:"100% thanh long tự nhiên Việt Nam",en:"100% natural Vietnam pitaya"},color:{vi:"Hồng đến hồng tím",en:"Pink to pink purple"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"13.9%",en:"13.9%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"},moisture:{vi:"5%",en:"5%"}},storageConditions:{vi:"Nơi khô ráo, thoáng mát",en:"In normal temperature"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"18 tháng kể từ ngày sản xuất",en:"18 months from MF date"},size:{vi:"hạt lựu, lát, miếng, bột",en:"dices, slices, chunks, powder"},harvestSeason:{vi:"Tháng 5 - Tháng 9",en:"May - Sep"},nutritionFacts:{calories:45,servingSize:"12g",totalFat:.5,saturatedFat:0,transFat:0,cholesterol:0,sodium:0,totalCarbs:10,dietaryFiber:3,sugars:6,protein:1,vitaminD:0,calcium:3,iron:.5,potassium:170}},{id:"fd-durian",name:{vi:"SẦU RIÊNG ĐÔNG KHÔ",en:"FD DURIAN"},category:{id:"freeze-dried-fruit",name:{vi:"TRÁI CÂY ĐÔNG KHÔ",en:"FREEZE DRIED FRUIT"}},description:{vi:'Sầu riêng là một loại trái cây có nguồn gốc từ Đông Nam Á, đặc trưng bởi mùi hương rất mạnh mà không phải ai cũng có thể chịu được nhưng thường gây nghiện. Nó phổ biến ở Đông Nam Á, nơi nó được mệnh danh là "vua của các loại trái cây". Sầu riêng rất giàu chất dinh dưỡng, chứa nhiều dưỡng chất hơn hầu hết các loại trái cây khác.',en:`Durian is a fruit native to Southeat Asia, characterized by a very strong aroma that not everyone can tolerate but habitually addicted. It's popular in Southeast Asia, where it's nicknamed "the king of fruits." Durian is very high in nutrients, containing more than most other fruits.`},images:["/images/FREEZE DRIED FRUIT/fd_durian/dufd1.jpg","/images/FREEZE DRIED FRUIT/fd_durian/dufd2.jpg","/images/FREEZE DRIED FRUIT/fd_durian/dufd3.jpg","/images/FREEZE DRIED FRUIT/fd_durian/dufd4.jpg"],specifications:{composition:{vi:"100% sầu riêng tự nhiên Việt Nam",en:"100% natural Vietnam durian"},color:{vi:"Vàng, màu tự nhiên",en:"Yellow, natural color"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"12%",en:"12%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"},moisture:{vi:"5%",en:"5%"}},storageConditions:{vi:"Nơi khô ráo, thoáng mát",en:"In normal temperature"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"18 tháng kể từ ngày sản xuất",en:"18 months from MF date"},size:{vi:"hạt lựu, lát, bột",en:"dices, slices, powder"},harvestSeason:{vi:"Tháng 5 - Tháng 9",en:"May - Sep"},nutritionFacts:{calories:130,servingSize:"80g",totalFat:2.5,saturatedFat:1,transFat:0,cholesterol:0,sodium:0,totalCarbs:24,dietaryFiber:3.8,sugars:20,protein:2,vitaminD:0,calcium:0,iron:.36,potassium:436}},{id:"fd-jackfruit",name:{vi:"MÍT ĐÔNG KHÔ",en:"FD JACKFRUIT"},category:{id:"freeze-dried-fruit",name:{vi:"TRÁI CÂY ĐÔNG KHÔ",en:"FREEZE DRIED FRUIT"}},description:{vi:"Mít là một loại cây có vỏ ngoài xanh thô ráp, bên trong có phần thịt màu vàng. Mít chín có trái ngọt lớn, bên trong có nhiều gai nhọn và hạt, mùi hương rất đặc trưng và hấp dẫn. Đặc biệt, mít chín có vị ngọt, hương thơm giúp giải khát, giúp giảm nhiệt âm.",en:"Jackfruit is a type of tree with a rough green outer body inside the inside of yellow, ripe jackfruit has a big sweet fruit, has many sharp spines and seeds inside, the aroma is very specific and attractive. In particular, ripe jackfruit has a sweet taste, warmth helps to quench thirst, helps waste, minus negative heat."},images:["/images/FREEZE DRIED FRUIT/fd_jackfruit/jafd1.jpg","/images/FREEZE DRIED FRUIT/fd_jackfruit/jafd2.jpg","/images/FREEZE DRIED FRUIT/fd_jackfruit/jafd3.jpg","/images/FREEZE DRIED FRUIT/fd_jackfruit/jafd4.jpg"],specifications:{composition:{vi:"100% mít tự nhiên Việt Nam",en:"100% natural Vietnam jackfruit"},color:{vi:"Vàng nhạt đến vàng sáng, tự nhiên",en:"Pale yellow to bright gold, natural"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"",en:""},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"},moisture:{vi:"21.3%",en:"21.3%"}},storageConditions:{vi:"Nơi khô ráo, thoáng mát",en:"Store in cool and dried place"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 500gr, 1kg, 2kg, 5kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 500gr, 1kg, 2kg, 5kg or as customer's request"}},shelfLife:{vi:"12 tháng",en:"12 months"},size:{vi:"nửa, nguyên",en:"halves, whole"},harvestSeason:{vi:"Tháng 1, Tháng 2, Tháng 5 - Tháng 7, Tháng 12",en:"Jan, Feb, May - July, Dec"},nutritionFacts:{calories:400,servingSize:"100g",totalFat:0,saturatedFat:0,transFat:0,cholesterol:0,sodium:0,totalCarbs:90,dietaryFiber:10,sugars:50,protein:5,vitaminC:30,vitaminD:0,calcium:0,iron:0,potassium:1450}},{id:"fd-mango",name:{vi:"XOÀI ĐÔNG KHÔ",en:"FD MANGO"},category:{id:"freeze-dried-fruit",name:{vi:"TRÁI CÂY ĐÔNG KHÔ",en:"FREEZE DRIED FRUIT"}},description:{vi:"Xoài là một loại trái cây thơm ngon được trồng ở vùng nhiệt đới, là một trong những loại trái cây phổ biến nhất, giàu dinh dưỡng với hương vị, mùi thơm, vị độc đáo và có tính chất tốt cho sức khỏe.",en:"Mango is a delicious fruit grown in the tropics, is one of the most popular, nutritionally rich fruits with unique flavor, fragrance, taste, and health promoting qualities."},images:["/images/FREEZE DRIED FRUIT/fd_mango/mafd1.jpg","/images/FREEZE DRIED FRUIT/fd_mango/mafd2.jpg","/images/FREEZE DRIED FRUIT/fd_mango/mafd3.jpg","/images/FREEZE DRIED FRUIT/fd_mango/mafd4.jpg"],specifications:{composition:{vi:"100% xoài tự nhiên Việt Nam",en:"100% natural Vietnam Mango"},color:{vi:"Vàng nhạt đến vàng sáng, tự nhiên",en:"Pale yellow to bright gold, natural"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"14%",en:"14%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"},moisture:{vi:"5%",en:"5%"}},storageConditions:{vi:"Nhiệt độ bình thường",en:"In normal temperature"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 200gr, 1kg, 2kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100gr, 200gr, 1kg, 2kg or as customer's request"}},shelfLife:{vi:"18 tháng kể từ ngày sản xuất",en:"18 month from MF Date"},size:{vi:"miếng, hạt lựu, lát, nửa, bột",en:"chunks, dices, slices, halves, powder"},harvestSeason:{vi:"Tháng 1 - Tháng 5, Tháng 9 - Tháng 12",en:"Jan - May, Sep - Dec"},nutritionFacts:{calories:375,servingSize:"100g",totalFat:0,saturatedFat:0,transFat:0,cholesterol:0,sodium:10,totalCarbs:89.6,dietaryFiber:6.2,sugars:77.1,protein:4.2,vitaminC:56.2,vitaminD:0,calcium:42,iron:.8,potassium:0}},{id:"fd-rambutan",name:{vi:"CHÔM CHÔM ĐÔNG KHÔ",en:"FD RAMBUTAN"},category:{id:"freeze-dried-fruit",name:{vi:"TRÁI CÂY ĐÔNG KHÔ",en:"FREEZE DRIED FRUIT"}},description:{vi:"Chôm chôm là một loại cây nhiệt đới cỡ trung bình thuộc họ Sapindaceae. Tên gọi cũng đề cập đến loại trái cây ăn được do cây này tạo ra. Chôm chôm có nguồn gốc từ Đông Nam Á. Nó có họ hàng gần với một số loại trái cây nhiệt đới ăn được khác bao gồm vải, nhãn, pulasan và mamoncillo. Quả chôm chôm giàu nhiều vitamin, khoáng chất và các hợp chất thực vật có lợi. Chôm chôm cũng chứa một lượng đồng tốt, đóng vai trò trong sự phát triển và duy trì đúng đắn của các tế bào khác nhau, bao gồm cả các tế bào của xương, não và tim.",en:"The rambutan is a medium-sized tropical tree in the family Sapindaceae. The name also refers to the edible fruit produced by this tree. The rambutan is native to Southeast Asia. It is closely related to several other edible tropical fruits including the lychee, longan, pulasan and mamoncillo. The rambutan fruit is rich in many vitamins, minerals and beneficial plant compounds. Rambutan also contains a good amount of copper, which plays a role in the proper growth and maintenance of various cells, including those of your bones, brain and heart."},images:["/images/FREEZE DRIED FRUIT/fd_rambutan/ramfd1.jpg","/images/FREEZE DRIED FRUIT/fd_rambutan/ramfd2.jpg","/images/FREEZE DRIED FRUIT/fd_rambutan/ramfd3.jpg","/images/FREEZE DRIED FRUIT/fd_rambutan/ramfd4.jpg"],specifications:{composition:{vi:"100% chôm chôm tự nhiên Việt Nam",en:"100% natural Vietnam rambutan"},color:{vi:"Trắng sữa",en:"Milky white"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"13.9%",en:"13.9%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"},moisture:{vi:"5%",en:"5%"}},storageConditions:{vi:"Chưa mở ở nhiệt độ dưới 20°C và độ ẩm thấp",en:"Unopened at temperatures below 20°C and low humidity"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 200gr, 1kg, 2kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100grs, 200grs, 1kg, 2kg or as customer's request"}},shelfLife:{vi:"18 tháng",en:"18 months"},size:{vi:"Nguyên quả",en:"Whole fruit"},harvestSeason:{vi:"Tháng 5 - Tháng 7",en:"May - July"},nutritionFacts:{calories:90,servingSize:"25g",totalFat:0,saturatedFat:0,transFat:0,cholesterol:0,sodium:5,totalCarbs:22,dietaryFiber:3,sugars:18,protein:1,vitaminC:25,iron:2,calcium:10,vitaminB2:6,vitaminB1:2,potassium:0}},{id:"fd-pineapple",name:{vi:"DỨA ĐÔNG KHÔ",en:"FD PINEAPPLE"},category:{id:"freeze-dried-fruit",name:{vi:"TRÁI CÂY ĐÔNG KHÔ",en:"FREEZE DRIED FRUIT"}},description:{vi:"Dứa là loại trái cây nhiệt đới giàu vitamin, enzyme và chất chống oxy hóa. Chúng có thể giúp tăng cường hệ miễn dịch, xây dựng xương chắc khỏe và hỗ trợ tiêu hóa. Và, mặc dù có vị ngọt, dứa lại ít calo. Quả dứa được tạo thành từ nhiều quả mọng riêng lẻ phát triển cùng nhau xung quanh một lõi trung tâm. Mỗi vảy dứa là một bông hoa riêng lẻ, hay quả mọng.",en:"Pineapples are tropical fruits that are rich in vitamins, enzymes and antioxidants. They may help boost the immune system, build strong bones and aid digestion. And, despite their sweetness, pineapples are low in calories. The fruit is made of many individual berries that grow together around a central core. Each pineapple scale is an individual flower, or berry."},images:["/images/FREEZE DRIED FRUIT/fd_pineapple/pifd1.jpg","/images/FREEZE DRIED FRUIT/fd_pineapple/pifd2.jpg","/images/FREEZE DRIED FRUIT/fd_pineapple/pifd3.jpg","/images/FREEZE DRIED FRUIT/fd_pineapple/pifd4.jpg"],specifications:{composition:{vi:"100% dứa tự nhiên Việt Nam",en:"100% natural Vietnam pineapple"},color:{vi:"Vàng xanh đến xanh nhạt",en:"Yellowish green to light green"},califore:{vi:"<10 CFU/g",en:"<10 CFU/g"},brix:{vi:"13.9%",en:"13.9%"},ecoli:{vi:"0 CFU/g",en:"0 CFU/g"},moisture:{vi:"5%",en:"5%"}},storageConditions:{vi:"Chưa mở ở nhiệt độ dưới 20°C và độ ẩm thấp",en:"Unopened at temperatures below 20°C and low humidity"},packing:{bulk:{vi:"Đóng gói PE: túi trong thùng carton (10/20kg)",en:"Bulk packing: PE bag in carton box (10/20kg)"},retail:{vi:"Đóng gói bán lẻ: túi trong hộp: 100gr, 200gr, 1kg, 2kg hoặc theo yêu cầu của khách hàng",en:"Retail packing: bag-in-box: 100grs, 200grs, 1kg, 2kg or as customer's request"}},shelfLife:{vi:"18 tháng",en:"18 months"},size:{vi:"Hạt lựu, lát, miếng, bột",en:"Dices, slices, chunks, powder"},harvestSeason:{vi:"Quanh năm",en:"Year-round"},nutritionFacts:{calories:48,servingSize:"25g",totalFat:0,saturatedFat:0,transFat:0,cholesterol:0,sodium:0,totalCarbs:12,dietaryFiber:1,sugars:9,protein:0,vitaminC:15,iron:2,calcium:0,potassium:0}}],_g=({language:e})=>{const[t,n]=k.useState(0),[i,r]=k.useState([0,0,0,0]),s=k.useMemo(()=>[{image:"/images/slides/assortment-fresh-fruits-table_434193-4560.avif",alt:"Fresh fruits display"},{image:"/images/slides/tropical_fruit_1917837551.jpg",alt:"Fruit processing"},{image:"/images/slides/tropical_fruits.webp",alt:"Fruit export"},{image:"/images/slides/fruits-in-vietnam-1.jpg",alt:"Fruit export"}],[]),a=k.useMemo(()=>[{name:{vi:"Trái cây tươi",en:"Fresh Fruits"},image:"/images/FRESH FRUIT/avocado/1.jpg",path:"/products"},{name:{vi:"Trái cây đông lạnh",en:"Frozen Fruits"},image:"/images/FROZEN FRUIT/f_avocado/avofr1.jpg",path:"/products"},{name:{vi:"Trái cây sấy khô",en:"Dried Fruits"},image:"/images/DRIED FRUIT/d_banana/badr1.jpg",path:"/products"},{name:{vi:"Trái cây Đông khô",en:"Freeze Dried Fruits"},image:"/images/FREEZE DRIED FRUIT/fd_avocado/avofd1.jpg",path:"/products"}],[]),o=k.useMemo(()=>[vn.filter(d=>d.category.id==="fresh-fruit"),vn.filter(d=>d.category.id==="frozen-fruit"),vn.filter(d=>d.category.id==="dried-fruit"),vn.filter(d=>d.category.id==="freeze-dried-fruit")],[]),l=k.useMemo(()=>a.map((d,h)=>{const g=o[h];return!g||g.length<2?[{image:d.image,name:d.name[e]},{image:d.image,name:d.name[e]},{image:d.image,name:d.name[e]},{image:d.image,name:d.name[e]}]:g.map(y=>({image:y.images[0],name:y.name[e]}))}),[a,o,e]),c={hero:{title:{vi:"NHÀ SẢN XUẤT VÀ XUẤT KHẨU TRÁI CÂY ĐÔNG LẠNH VÀ SẤY KHÔ",en:"MANUFACTURER AND EXPORTER OF PRODUCTS FROM FRUITS AND NUTS"},subtitle:{vi:"Chúng tôi cung cấp trái cây tươi chất lượng cao, uy tín và giá cạnh tranh.",en:"We provide high-quality fresh fruits from around the world"},button:{vi:"Khám phá ngay",en:"Explore now"}},productSection:{title:{vi:"DANH MỤC SẢN PHẨM",en:"PRODUCT CATEGORIES"},viewMore:{vi:"Xem chi tiết",en:"View more"}},aboutSection:{title:{vi:"VỀ CHÚNG TÔI",en:"ABOUT US"},content:{vi:"Việt Nam nổi tiếng trên thế giới về trái cây nhiệt đới, với hàng ngàn chủng loại trái cây và rau củ bổ dưỡng như : Mít, thanh long, khóm, xoài, chuối, khoai môn,..Với mục tiêu được xác định ngay từ những ngày đầu thành lập, ANBINHFOODS luôn hướng tới mục tiêu góp phần đưa nông sản Việt Nam ra thế giới, để mọi người trên thế giới có thể dễ dàng thưởng thức được hương vị và chất lượng cao nhất của những trái cây vùng nhiệt đới.",en:"Vietnam is renowned worldwide for its tropical fruits, with thousands of varieties of nutritious fruits and vegetables such as jackfruit, dragon fruit, pineapple, mango, banana, taro, and more. With a mission established from its founding days, ANBINHFOODS has always aimed to contribute to bringing Vietnamese agricultural products to the world, so that people globally can easily enjoy the finest flavors and highest quality of these tropical fruits."},button:{vi:"Tìm hiểu thêm",en:"Learn more"}}};return k.useEffect(()=>{const d=setInterval(()=>{n(g=>g===s.length-1?0:g+1)},5e3),h=[];for(let g=0;g<l.length;g++){const y=setInterval(()=>{r(v=>{const x=[...v];return x[g]=(x[g]+1)%l[g].length,x})},3e3);h.push(y)}return()=>{clearInterval(d),h.forEach(g=>clearInterval(g))}},[s,l]),u.jsxs("div",{children:[u.jsxs("section",{className:"relative h-[600px] overflow-hidden",children:[s.map((d,h)=>u.jsxs("div",{className:`absolute inset-0 transition-opacity duration-1000 ${h===t?"opacity-100":"opacity-0"}`,children:[u.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-20"}),u.jsx("img",{src:d.image,alt:d.alt,className:"w-full h-full object-cover"}),u.jsxs("div",{className:"absolute inset-0 flex flex-col items-center justify-center text-white text-center px-4",children:[u.jsx("h1",{className:"text-3xl md:text-5xl font-bold mb-4 max-w-4xl",children:c.hero.title[e]}),u.jsx("p",{className:"text-xl md:text-2xl mb-8 max-w-2xl",children:c.hero.subtitle[e]}),u.jsx(me,{to:"/products",className:"bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-md transition duration-300",children:c.hero.button[e]})]})]},h)),u.jsx("div",{className:"absolute bottom-5 left-0 right-0 flex justify-center space-x-2",children:s.map((d,h)=>u.jsx("button",{onClick:()=>n(h),className:`w-3 h-3 rounded-full ${h===t?"bg-red-600":"bg-white bg-opacity-50"}`,"aria-label":`Go to slide ${h+1}`},h))})]}),u.jsx("section",{className:"py-16 bg-gray-50",children:u.jsxs("div",{className:"container mx-auto px-4",children:[u.jsx("h2",{className:"text-3xl font-bold text-center mb-12 text-red-600",children:c.productSection.title[e]}),u.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:a.map((d,h)=>u.jsxs("div",{className:"bg-white rounded-[20px] shadow-md overflow-hidden group border border-gray-200 hover:shadow-xl hover:-translate-y-2 transition-all duration-300",children:[u.jsx("div",{className:"h-80 overflow-hidden relative rounded-t-[20px]",children:u.jsxs(u.Fragment,{children:[l[h].map((g,y)=>u.jsx("div",{className:`absolute inset-0 transition-all duration-1000 ${y===i[h]?"opacity-100 transform scale-100":"opacity-0 transform scale-105"}`,children:u.jsxs("div",{className:"relative w-full h-full",children:[u.jsx("img",{src:g.image,alt:g.name,className:"w-full h-full object-cover transition-transform duration-1000"}),u.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-20 flex items-end justify-center",children:u.jsx("div",{className:"bg-black bg-opacity-50 text-white px-4 py-2 mb-8 rounded-md text-center",children:u.jsx("span",{className:"font-semibold",children:g.name})})})]})},y)),u.jsx("div",{className:"absolute bottom-3 left-0 right-0 flex justify-center space-x-1.5 z-10",children:l[h].map((g,y)=>u.jsx("button",{onClick:()=>{const v=[...i];v[h]=y,r(v)},className:`w-2 h-2 rounded-full transition-all duration-300 ${y===i[h]?"bg-red-600 w-3":"bg-white bg-opacity-70"}`,"aria-label":`Go to product slide ${y+1}`},y))})]})}),u.jsxs("div",{className:"p-8 relative z-10",children:[u.jsx("h3",{className:"text-xl font-semibold mb-4",children:d.name[e]}),u.jsxs(me,{to:d.path,className:"text-red-600 hover:text-red-700 font-medium inline-flex items-center",children:[c.productSection.viewMore[e],u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 ml-1",viewBox:"0 0 20 20",fill:"currentColor",children:u.jsx("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]})]},h))})]})}),u.jsx("section",{className:"py-4 bg-white",children:u.jsx("div",{className:"container mx-auto px-4",children:u.jsxs("div",{className:"flex flex-col md:flex-row items-center",children:[u.jsx("div",{className:"md:w-3/5 mb-8 md:mb-0 md:pr-8",children:u.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[u.jsx("div",{className:"overflow-hidden rounded-lg shadow-lg",children:u.jsx("img",{src:"/images/about/anh4.jpg",alt:"About FreshFruits 1",className:"w-full h-[250px] object-cover hover:scale-110 transition-transform duration-500"})}),u.jsx("div",{className:"overflow-hidden rounded-lg shadow-lg",children:u.jsx("img",{src:"/images/about/anh3.jpg",alt:"About FreshFruits 2",className:"w-full h-[250px] object-cover hover:scale-110 transition-transform duration-500"})}),u.jsx("div",{className:"overflow-hidden rounded-lg shadow-lg",children:u.jsx("img",{src:"/images/about/anh6.jpg",alt:"About FreshFruits 3",className:"w-full h-[250px] object-cover hover:scale-110 transition-transform duration-500"})}),u.jsx("div",{className:"overflow-hidden rounded-lg shadow-lg",children:u.jsx("img",{src:"/images/about/anh1.jpg",alt:"About FreshFruits 4",className:"w-full h-[250px] object-cover hover:scale-110 transition-transform duration-500"})})]})}),u.jsxs("div",{className:"md:w-2/5",children:[u.jsx("h2",{className:"text-3xl font-bold mb-6 text-red-600 text-center",children:c.aboutSection.title[e]}),u.jsx("p",{className:"text-gray-700 mb-6 text-lg leading-relaxed text-justify",children:c.aboutSection.content[e]}),u.jsx("div",{className:"flex justify-center",children:u.jsx(me,{to:"/about/passion",className:"bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-md transition duration-300 inline-block",children:c.aboutSection.button[e]})})]})]})})}),u.jsx(zg,{language:e})]})},Hg=({language:e})=>{const t={pageTitle:{vi:"GIỚI THIỆU",en:"ABOUT US"},heroTitle:{vi:"CHÚNG TÔI LÀ AI",en:"WHO WE ARE"},heroContent:{vi:"FreshFruits Group - Nhà cung cấp trái cây tươi quốc tế hàng đầu",en:"FreshFruits Group - Leading international fresh fruit supplier"},ourStory:{title:{vi:"CÂU CHUYỆN CỦA CHÚNG TÔI",en:"OUR STORY"},content:{vi:`FreshFruits Group được thành lập vào năm 1995, bắt đầu từ một cửa hàng nhỏ chuyên cung cấp trái cây nhập khẩu tại Thành phố Hồ Chí Minh. Trải qua hơn 25 năm phát triển, chúng tôi đã trở thành một trong những nhà nhập khẩu và phân phối trái cây tươi lớn nhất Việt Nam.

Với mạng lưới đối tác rộng khắp các quốc gia như Mỹ, Úc, New Zealand, Chile, Nam Phi và các nước Châu Âu, chúng tôi tự hào mang đến cho người tiêu dùng Việt Nam những loại trái cây tươi ngon, chất lượng cao và an toàn nhất.`,en:`FreshFruits Group was established in 1995, starting as a small store specializing in imported fruits in Ho Chi Minh City. Over more than 25 years of development, we have become one of the largest importers and distributors of fresh fruits in Vietnam.

With a network of partners across countries such as the USA, Australia, New Zealand, Chile, South Africa, and European countries, we are proud to bring Vietnamese consumers the freshest, highest quality, and safest fruits.`}},mission:{title:{vi:"SỨ MỆNH CỦA CHÚNG TÔI",en:"OUR MISSION"},content:{vi:`Sứ mệnh của FreshFruits Group là mang đến những sản phẩm trái cây tươi ngon, chất lượng cao và an toàn cho sức khỏe người tiêu dùng. Chúng tôi cam kết:

- Cung cấp trái cây tươi ngon nhất từ các vùng trồng nổi tiếng trên thế giới
- Đảm bảo quy trình vận chuyển, bảo quản đạt tiêu chuẩn quốc tế
- Kiểm soát chất lượng nghiêm ngặt từ nông trại đến tay người tiêu dùng
- Phát triển bền vững và bảo vệ môi trường`,en:`The mission of FreshFruits Group is to bring fresh, high-quality, and health-safe fruit products to consumers. We are committed to:

- Providing the freshest fruits from famous growing regions around the world
- Ensuring transportation and preservation processes meet international standards
- Strict quality control from farm to consumer
- Sustainable development and environmental protection`}},values:{title:{vi:"GIÁ TRỊ CỐT LÕI",en:"CORE VALUES"},values:[{title:{vi:"Chất lượng",en:"Quality"},description:{vi:"Chúng tôi luôn đặt chất lượng sản phẩm lên hàng đầu, đảm bảo mỗi trái cây đều đạt tiêu chuẩn cao nhất.",en:"We always put product quality first, ensuring each fruit meets the highest standards."},icon:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},{title:{vi:"Đổi mới",en:"Innovation"},description:{vi:"Chúng tôi không ngừng tìm kiếm những giải pháp mới để cải thiện sản phẩm và dịch vụ.",en:"We constantly seek new solutions to improve our products and services."},icon:"M13 10V3L4 14h7v7l9-11h-7z"},{title:{vi:"Bền vững",en:"Sustainability"},description:{vi:"Chúng tôi cam kết phát triển bền vững và bảo vệ môi trường trong mọi hoạt động kinh doanh.",en:"We are committed to sustainable development and environmental protection in all business activities."},icon:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},{title:{vi:"Hợp tác",en:"Collaboration"},description:{vi:"Chúng tôi xây dựng mối quan hệ đối tác bền vững với nông dân, nhà cung cấp và khách hàng.",en:"We build sustainable partnerships with farmers, suppliers, and customers."},icon:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"}]},team:{title:{vi:"ĐỘI NGŨ LÃNH ĐẠO",en:"LEADERSHIP TEAM"},members:[{name:"Nguyễn Văn A",position:{vi:"Tổng Giám đốc",en:"Chief Executive Officer"},image:"https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80"},{name:"Trần Thị B",position:{vi:"Giám đốc Vận hành",en:"Chief Operations Officer"},image:"https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=688&q=80"},{name:"Lê Văn C",position:{vi:"Giám đốc Tài chính",en:"Chief Financial Officer"},image:"https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80"},{name:"Phạm Thị D",position:{vi:"Giám đốc Marketing",en:"Chief Marketing Officer"},image:"https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80"}]}};return u.jsxs("div",{children:[u.jsxs("section",{className:"relative h-[400px] bg-cover bg-center",style:{backgroundImage:"url(https://images.unsplash.com/photo-1577234286642-fc512a5f8f11?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80)"},children:[u.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-60"}),u.jsxs("div",{className:"absolute inset-0 flex flex-col items-center justify-center text-white text-center px-4",children:[u.jsx("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:t.pageTitle[e]}),u.jsx("div",{className:"w-20 h-1 bg-red-600 mb-6"}),u.jsx("h2",{className:"text-2xl md:text-3xl font-semibold mb-2",children:t.heroTitle[e]}),u.jsx("p",{className:"text-xl max-w-2xl",children:t.heroContent[e]})]})]}),u.jsx("section",{className:"py-16 bg-white",children:u.jsx("div",{className:"container mx-auto px-4",children:u.jsxs("div",{className:"flex flex-col md:flex-row items-center",children:[u.jsx("div",{className:"md:w-1/2 mb-8 md:mb-0 md:pr-8",children:u.jsx("img",{src:"https://images.unsplash.com/photo-1542838132-92c53300491e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",alt:"Our Story",className:"rounded-lg shadow-lg w-full h-auto"})}),u.jsxs("div",{className:"md:w-1/2",children:[u.jsx("h2",{className:"text-3xl font-bold mb-6 text-red-600",children:t.ourStory.title[e]}),u.jsx("div",{className:"text-gray-700 space-y-4",children:t.ourStory.content[e].split(`

`).map((n,i)=>u.jsx("p",{className:"text-lg leading-relaxed",children:n},i))})]})]})})}),u.jsx("section",{className:"py-16 bg-gray-50",children:u.jsxs("div",{className:"container mx-auto px-4",children:[u.jsx("h2",{className:"text-3xl font-bold text-center mb-12 text-red-600",children:t.mission.title[e]}),u.jsx("div",{className:"max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-8",children:u.jsx("div",{className:"text-gray-700 space-y-4",children:t.mission.content[e].split(`

`).map((n,i)=>u.jsx("div",{children:n.includes("-")?u.jsx("ul",{className:"list-disc pl-5 space-y-2",children:n.split(`
`).map((r,s)=>u.jsx("li",{className:"text-lg leading-relaxed",children:r.replace("- ","")},s))}):u.jsx("p",{className:"text-lg leading-relaxed",children:n})},i))})})]})}),u.jsx("section",{className:"py-16 bg-white",children:u.jsxs("div",{className:"container mx-auto px-4",children:[u.jsx("h2",{className:"text-3xl font-bold text-center mb-12 text-red-600",children:t.values.title[e]}),u.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:t.values.values.map((n,i)=>u.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 text-center hover:shadow-lg transition-shadow duration-300",children:[u.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-red-100 text-red-600 rounded-full mb-6",children:u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:n.icon})})}),u.jsx("h3",{className:"text-xl font-semibold mb-4",children:n.title[e]}),u.jsx("p",{className:"text-gray-600",children:n.description[e]})]},i))})]})}),u.jsx("section",{className:"py-16 bg-gray-50",children:u.jsxs("div",{className:"container mx-auto px-4",children:[u.jsx("h2",{className:"text-3xl font-bold text-center mb-12 text-red-600",children:t.team.title[e]}),u.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:t.team.members.map((n,i)=>u.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden group",children:[u.jsx("div",{className:"h-64 overflow-hidden",children:u.jsx("img",{src:n.image,alt:n.name,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"})}),u.jsxs("div",{className:"p-6 text-center",children:[u.jsx("h3",{className:"text-xl font-semibold mb-2",children:n.name}),u.jsx("p",{className:"text-gray-600",children:n.position[e]})]})]},i))})]})})]})},Ug=({language:e})=>{const t={pageTitle:{vi:"ĐAM MÊ CỦA CHÚNG TÔI",en:"OUR PASSION"},heroTitle:{vi:"NIỀM ĐAM MÊ VỚI TRÁI CÂY TƯƠI NGON",en:"OUR PASSION FOR FRESH FRUITS"},heroContent:{vi:"Chúng tôi đam mê mang đến những trái cây tươi ngon nhất từ khắp nơi trên thế giới",en:"We are passionate about bringing the freshest fruits from around the world"},ourPassion:{title:{vi:"NIỀM ĐAM MÊ CỦA CHÚNG TÔI",en:"OUR PASSION"},content:{vi:`Từ những vườn cây trĩu quả trên khắp Việt Nam, AB FOODS ra đời với sứ mệnh mang hương vị thuần khiết của trái cây nhiệt đới đến bàn ăn mọi gia đình trên toàn cầu. Mỗi trái cây là tinh túy của đất trời, kết tinh từ nắng, gió và bàn tay chăm sóc tận tâm của người nông dân.

Chúng tôi chọn lọc chỉ những "siêu trái cây" tươi ngon nhất, thu hoạch đúng độ chín và bảo quản bằng công nghệ đông lạnh nhanh để giữ trọn hương vị, dinh dưỡng. Từ sầu riêng thơm lừng, mít vàng óng đến thanh long rực rỡ, mỗi sản phẩm AB FOODS là niềm tự hào về nông sản Việt.

Không chỉ kinh doanh, chúng tôi mong muốn lan tỏa lối sống lành mạnh, kết nối con người với thiên nhiên. Mỗi sản phẩm là sự tận tâm từ khâu nguyên liệu đến chế biến, vì khách hàng xứng đáng được thưởng thức những gì tốt nhất.

Cùng AB FOODS khám phá hương vị thuần khiết – Vì cuộc sống tươi ngon, khỏe mạnh!`,en:`At FreshFruits Group, our passion is to bring the freshest fruits from around the world to Vietnamese consumers. We believe that each fruit is not just a product, but a crystallization of nature, meticulous care, and the love of farmers.

This passion has driven us to constantly search for the best growing regions, the varieties that produce the most delicious fruits, and the most sustainable farming methods. From apple orchards in Washington, orange groves in California, to cherry orchards in Chile and kiwi farms in New Zealand, we have traveled the world in search of perfect fruits.`}},qualityCommitment:{title:{vi:"CAM KẾT CHẤT LƯỢNG CỦA AN BÌNH FOODS",en:"QUALITY COMMITMENT OF AN BINH FOODS"},intro:{vi:'Tại An Bình Foods, chúng tôi không chỉ cung cấp trái cây – chúng tôi trao gửi niềm tin, sự tận tâm và những giá trị bền vững trong từng sản phẩm. Với triết lý "Chất lượng từ nguồn cội, an toàn từ tâm huyết", chúng tôi cam kết:',en:`At An Binh Foods, we don't just provide fruits – we deliver trust, dedication, and sustainable values in every product. With the philosophy "Quality from the source, safety from dedication", we commit to:`},commitments:[{title:{vi:"NGUỒN NGUYÊN LIỆU TUYỂN CHỌN KHẮT KHE",en:"STRICTLY SELECTED INGREDIENTS"},points:{vi:["Chỉ hợp tác với những vườn trái cây đạt chuẩn về chất lượng, canh tác bền vững.","Thu hoạch đúng độ chín tự nhiên, không ép chín, không chất bảo quản độc hại."],en:["Only partnering with orchards that meet quality standards and practice sustainable farming.","Harvesting at the right natural ripeness, no forced ripening, no harmful preservatives."]},icon:"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z",image:"/images/about/quality1.png"},{title:{vi:"CÔNG NGHỆ ĐÔNG LẠNH NHANH HIỆN ĐẠI",en:"MODERN QUICK FREEZING TECHNOLOGY"},points:{vi:["Ứng dụng phương pháp BQF để giữ trọn hương vị, màu sắc và dinh dưỡng như vừa mới hái.","Quy trình khép kín, đạt chuẩn HACCP, ISO 22000, đảm bảo an toàn vệ sinh tuyệt đối."],en:["Applying BQF method to preserve flavor, color and nutrition as if freshly picked.","Closed process, meeting HACCP, ISO 22000 standards, ensuring absolute hygiene safety."]},icon:"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z",image:"/images/about/anh2.jpg"},{title:{vi:"MINH BẠCH VÀ TRÁCH NHIỆM",en:"TRANSPARENCY AND RESPONSIBILITY"},points:{vi:["Truy xuất nguồn gốc rõ ràng từ vườn đến bàn ăn.","Cam kết không biến đổi gen (Non-GMO), không phẩm màu, không hương liệu nhân tạo."],en:["Clear traceability from farm to table.","Commitment to non-GMO, no artificial colors, no artificial flavors."]},icon:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z",image:"/images/about/anh6.jpg"},{title:{vi:"VÌ SỨC KHỎE VÀ MÔI TRƯỜNG",en:"FOR HEALTH AND ENVIRONMENT"},points:{vi:["Bao bì thân thiện, tái chế được, giảm thiểu rác thải nhựa.","Hướng đến nền nông nghiệp xanh, hỗ trợ cộng đồng nông dân Việt phát triển bền vững."],en:["Eco-friendly, recyclable packaging, minimizing plastic waste.","Aiming for green agriculture, supporting Vietnamese farming communities for sustainable development."]},icon:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z",image:"/images/about/anh5.jpg"}],conclusion:{vi:`An Bình Foods không ngừng nỗ lực để mỗi sản phẩm không chỉ là thức quà thiên nhiên ban tặng, mà còn là lời tri ân đến khách hàng – những người xứng đáng được thưởng thức điều tốt nhất!

🌿 Chọn An Bình Foods – Chọn sự an tâm từ những điều giản dị nhất! 🌿`,en:`An Binh Foods continuously strives to ensure each product is not just a gift from nature, but also a token of appreciation to our customers – who deserve to enjoy the best!

🌿 Choose An Binh Foods – Choose peace of mind from the simplest things! 🌿`}}};return u.jsxs("div",{children:[u.jsxs("section",{className:"relative h-[400px] bg-cover bg-center",style:{backgroundImage:"url(/images/about/anh3.jpg)"},children:[u.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-60"}),u.jsxs("div",{className:"absolute inset-0 flex flex-col items-center justify-center text-white text-center px-4",children:[u.jsx("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:t.pageTitle[e]}),u.jsx("div",{className:"w-20 h-1 bg-red-600 mb-6"}),u.jsx("h2",{className:"text-2xl md:text-3xl font-semibold mb-2",children:t.heroTitle[e]}),u.jsx("p",{className:"text-xl max-w-2xl",children:t.heroContent[e]})]})]}),u.jsx("section",{className:"py-16 bg-white",children:u.jsx("div",{className:"container mx-auto px-4",children:u.jsxs("div",{className:"flex flex-col md:flex-row items-center",children:[u.jsx("div",{className:"md:w-1/3 mb-8 md:mb-0 md:pr-8",children:u.jsx("img",{src:"/public/images/about/anh7.jpg",alt:"Our Passion",className:"rounded-lg shadow-lg w-full h-auto mt-16"})}),u.jsxs("div",{className:"md:w-2/3",children:[u.jsx("h2",{className:"text-3xl font-bold mb-6 text-red-600  ",children:t.ourPassion.title[e]}),u.jsx("div",{className:"text-gray-900 space-y-4",children:t.ourPassion.content[e].split(`

`).map((n,i)=>u.jsx("p",{className:"text-lg leading-relaxed",children:n},i))})]})]})})}),u.jsx("section",{className:"py-16 bg-gray-50",children:u.jsxs("div",{className:"container mx-auto px-4",children:[u.jsx("h2",{className:"text-3xl font-bold text-center mb-8 text-red-600",children:t.qualityCommitment.title[e]}),u.jsx("p",{className:"text-xl text-center max-w-4xl mx-auto mb-12 text-gray-700",children:t.qualityCommitment.intro[e]}),u.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-12",children:t.qualityCommitment.commitments.map((n,i)=>u.jsxs("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden transform transition-transform duration-300 hover:scale-105",children:[u.jsxs("div",{className:"relative h-64 overflow-hidden",children:[u.jsx("img",{src:n.image,alt:n.title[e],className:"w-full h-full object-cover"}),u.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center",children:u.jsx("div",{className:"bg-red-600 bg-opacity-90 text-white py-3 px-6 rounded-lg",children:u.jsxs("h3",{className:"text-xl font-bold text-center",children:[i+1,". ",n.title[e]]})})})]}),u.jsx("div",{className:"p-6",children:u.jsxs("div",{className:"flex items-start mb-4",children:[u.jsx("div",{className:"flex-shrink-0 bg-red-100 p-3 rounded-full mr-4",children:u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:n.icon})})}),u.jsx("div",{className:"flex-1",children:u.jsx("ul",{className:"space-y-3",children:n.points[e].map((r,s)=>u.jsxs("li",{className:"flex items-start",children:[u.jsx("svg",{className:"h-5 w-5 text-red-500 mr-2 mt-1 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20",children:u.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),u.jsx("span",{className:"text-gray-700",children:r})]},s))})})]})})]},i))}),u.jsx("div",{className:"max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-8 text-center",children:u.jsx("div",{className:"text-gray-700 space-y-4",children:t.qualityCommitment.conclusion[e].split(`

`).map((n,i)=>u.jsx("p",{className:"text-lg leading-relaxed font-medium",children:n},i))})})]})}),u.jsx("section",{className:"py-16 text-red-600",children:u.jsxs("div",{className:"container mx-auto px-4 text-center",children:[u.jsx("h2",{className:"text-3xl font-bold mb-6",children:e==="vi"?"KHÁM PHÁ CÁC SẢN PHẨM CỦA CHÚNG TÔI":"EXPLORE OUR PRODUCTS"}),u.jsx(me,{to:"/products",className:"inline-block bg-white text-red-600 font-bold py-3 px-8 rounded-full hover:bg-gray-100 transition duration-300",children:e==="vi"?"XEM SẢN PHẨM":"VIEW PRODUCTS"})]})})]})},Bg=({language:e})=>{const t={pageTitle:{vi:"PHÁT TRIỂN BỀN VỮNG",en:"SUSTAINABILITY"},heroTitle:{vi:"CAM KẾT VỚI MÔI TRƯỜNG VÀ XÃ HỘI",en:"OUR COMMITMENT TO ENVIRONMENT AND SOCIETY"},heroContent:{vi:"Chúng tôi tin rằng kinh doanh bền vững là nền tảng cho sự phát triển lâu dài",en:"We believe that sustainable business is the foundation for long-term development"},ourApproach:{title:{vi:"CÁCH TIẾP CẬN CỦA CHÚNG TÔI",en:"OUR APPROACH"},content:{vi:`Tại AN BÌNH FOODS, phát triển bền vững không chỉ là một khẩu hiệu mà là một phần không thể thiếu trong mọi hoạt động kinh doanh của chúng tôi. Chúng tôi hiểu rằng sự thành công lâu dài phụ thuộc vào khả năng cân bằng giữa lợi nhuận kinh tế, trách nhiệm môi trường và phúc lợi xã hội.

Chúng tôi áp dụng cách tiếp cận toàn diện đối với tính bền vững, từ cách chúng tôi trồng và thu hoạch trái cây, đến cách chúng tôi đóng gói, vận chuyển và phân phối sản phẩm đến tay người tiêu dùng.`,en:`At AN BINH FOODS, sustainability is not just a slogan but an integral part of all our business operations. We understand that long-term success depends on the ability to balance economic profit, environmental responsibility, and social welfare.

We apply a comprehensive approach to sustainability, from how we grow and harvest fruits, to how we package, transport, and distribute products to consumers.`}},environmentalInitiatives:{title:{vi:"SÁNG KIẾN MÔI TRƯỜNG",en:"ENVIRONMENTAL INITIATIVES"},content:{vi:`Chúng tôi cam kết giảm thiểu tác động môi trường trong toàn bộ chuỗi cung ứng của mình. Một số sáng kiến chính của chúng tôi bao gồm:

- Thực hành nông nghiệp bền vững: Hợp tác với nông dân áp dụng các phương pháp canh tác thân thiện với môi trường, giảm thiểu sử dụng hóa chất và bảo tồn đa dạng sinh học.

- Quản lý nước: Triển khai các hệ thống tưới tiêu hiệu quả và xử lý nước thải để bảo vệ nguồn nước quý giá.

- Giảm thiểu chất thải: Áp dụng nguyên tắc giảm thiểu, tái sử dụng và tái chế trong toàn bộ hoạt động, đặc biệt là trong đóng gói sản phẩm.

- Năng lượng tái tạo: Đầu tư vào năng lượng mặt trời và các nguồn năng lượng tái tạo khác tại các cơ sở sản xuất và kho bãi.`,en:`We are committed to minimizing environmental impact throughout our supply chain. Some of our key initiatives include:

- Sustainable agricultural practices: Partnering with farmers to implement environmentally friendly farming methods, minimizing chemical use, and preserving biodiversity.

- Water management: Implementing efficient irrigation systems and wastewater treatment to protect valuable water resources.

- Waste reduction: Applying the principles of reduce, reuse, and recycle throughout our operations, especially in product packaging.

- Renewable energy: Investing in solar energy and other renewable energy sources at our production facilities and warehouses.`}},socialResponsibility:{title:{vi:"TRÁCH NHIỆM XÃ HỘI",en:"SOCIAL RESPONSIBILITY"},content:{vi:`Chúng tôi tin rằng thành công trong kinh doanh đi đôi với trách nhiệm đối với cộng đồng. Các chương trình trách nhiệm xã hội của chúng tôi tập trung vào:

- Hỗ trợ nông dân: Cung cấp đào tạo, hỗ trợ kỹ thuật và đảm bảo thu nhập ổn định cho nông dân đối tác.

- Phát triển cộng đồng: Đầu tư vào giáo dục, y tế và cơ sở hạ tầng tại các cộng đồng nơi chúng tôi hoạt động.

- Điều kiện làm việc công bằng: Đảm bảo môi trường làm việc an toàn, lương thưởng công bằng và cơ hội phát triển cho tất cả nhân viên.

- An toàn thực phẩm: Cam kết cung cấp sản phẩm an toàn, chất lượng cao cho người tiêu dùng.`,en:`We believe that business success goes hand in hand with responsibility to the community. Our social responsibility programs focus on:

- Supporting farmers: Providing training, technical support, and ensuring stable income for partner farmers.

- Community development: Investing in education, healthcare, and infrastructure in the communities where we operate.

- Fair working conditions: Ensuring safe working environments, fair compensation, and development opportunities for all employees.

- Food safety: Commitment to providing safe, high-quality products to consumers.`}}};return u.jsxs("div",{children:[u.jsxs("section",{className:"relative h-[400px] overflow-hidden",children:[u.jsxs("video",{className:"absolute w-full h-full object-cover",autoPlay:!0,muted:!0,loop:!0,playsInline:!0,children:[u.jsx("source",{src:"/video/video1.mp4",type:"video/mp4"}),"Your browser does not support the video tag."]}),u.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-60"}),u.jsxs("div",{className:"absolute inset-0 flex flex-col items-center justify-center text-white text-center px-4",children:[u.jsx("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:t.pageTitle[e]}),u.jsx("div",{className:"w-20 h-1 bg-green-600 mb-6"}),u.jsx("h2",{className:"text-2xl md:text-3xl font-semibold mb-2",children:t.heroTitle[e]}),u.jsx("p",{className:"text-xl max-w-2xl",children:t.heroContent[e]})]})]}),u.jsx("section",{className:"py-16 bg-white",children:u.jsx("div",{className:"container mx-auto px-4",children:u.jsxs("div",{className:"flex flex-col md:flex-row items-center",children:[u.jsx("div",{className:"md:w-1/2 mb-8 md:mb-0 md:pr-8",children:u.jsx("img",{src:"https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",alt:"Sustainable Approach",className:"rounded-lg shadow-lg w-full h-auto"})}),u.jsxs("div",{className:"md:w-1/2",children:[u.jsx("h2",{className:"text-3xl font-bold mb-6 text-green-600",children:t.ourApproach.title[e]}),u.jsx("div",{className:"text-gray-700 space-y-4",children:t.ourApproach.content[e].split(`

`).map((n,i)=>u.jsx("p",{className:"text-lg leading-relaxed",children:n},i))})]})]})})}),u.jsx("section",{className:"py-16 bg-gray-50",children:u.jsx("div",{className:"container mx-auto px-4",children:u.jsxs("div",{className:"flex flex-col md:flex-row-reverse items-center",children:[u.jsx("div",{className:"md:w-1/2 mb-8 md:mb-0 md:pl-8",children:u.jsx("img",{src:"/images/about/DJI_0025.jpg",alt:"Environmental Initiatives",className:"rounded-lg shadow-lg w-full h-auto"})}),u.jsxs("div",{className:"md:w-1/2",children:[u.jsx("h2",{className:"text-3xl font-bold mb-6 text-green-600",children:t.environmentalInitiatives.title[e]}),u.jsx("div",{className:"text-gray-700 space-y-4",children:t.environmentalInitiatives.content[e].split(`

`).map((n,i)=>u.jsx("div",{children:n.includes("-")?u.jsx("ul",{className:"list-disc pl-5 space-y-2",children:n.split(`
`).map((r,s)=>u.jsx("li",{className:"text-lg leading-relaxed",children:r.replace("- ","")},s))}):u.jsx("p",{className:"text-lg leading-relaxed",children:n})},i))})]})]})})}),u.jsx("section",{className:"py-16 bg-white",children:u.jsx("div",{className:"container mx-auto px-4",children:u.jsxs("div",{className:"flex flex-col md:flex-row items-center",children:[u.jsx("div",{className:"md:w-1/2 mb-8 md:mb-0 md:pr-8",children:u.jsx("img",{src:"https://images.unsplash.com/photo-1560493676-04071c5f467b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",alt:"Social Responsibility",className:"rounded-lg shadow-lg w-full h-auto"})}),u.jsxs("div",{className:"md:w-1/2",children:[u.jsx("h2",{className:"text-3xl font-bold mb-6 text-green-600",children:t.socialResponsibility.title[e]}),u.jsx("div",{className:"text-gray-700 space-y-4",children:t.socialResponsibility.content[e].split(`

`).map((n,i)=>u.jsx("div",{children:n.includes("-")?u.jsx("ul",{className:"list-disc pl-5 space-y-2",children:n.split(`
`).map((r,s)=>u.jsx("li",{className:"text-lg leading-relaxed",children:r.replace("- ","")},s))}):u.jsx("p",{className:"text-lg leading-relaxed",children:n})},i))})]})]})})})]})},Vg=({language:e,category:t})=>u.jsxs("div",{className:"mb-8 bg-white rounded-[20px] shadow-md overflow-hidden border border-gray-200 hover:shadow-xl transition-all duration-300 p-8 min-h-[350px]",children:[u.jsx("h2",{className:"text-xl font-bold mb-4 text-gray-800 border-b pb-2 whitespace-nowrap overflow-hidden text-overflow-ellipsis text-left uppercase",children:t.name[e]}),u.jsx("ul",{className:"space-y-2",children:t.products.map(n=>u.jsx("li",{children:u.jsx(me,{to:`/products/${t.id}/${n.slug}`,className:"text-gray-500 hover:text-green-600 transition-colors block text-left uppercase tracking-wide py-1",children:n.name[e]})},n.id))})]}),Gg=({language:e})=>{const t={pageTitle:{vi:"SẢN PHẨM",en:"PRODUCTS"},heroTitle:{vi:"DANH MỤC SẢN PHẨM",en:"PRODUCT CATEGORIES"},searchPlaceholder:{vi:"Tìm kiếm sản phẩm...",en:"Search products..."}},[n,i]=k.useState(""),s=[{id:"fresh-fruit",name:{vi:"TRÁI CÂY TƯƠI",en:"FRESH FRUIT"},products:[{id:"avocado",name:{vi:"BƠ",en:"AVOCADO"},slug:"avocado"},{id:"banana",name:{vi:"CHUỐI",en:"BANANA"},slug:"banana"},{id:"pitaya",name:{vi:"THANH LONG",en:"PITAYA"},slug:"pitaya"},{id:"durian",name:{vi:"SẦU RIÊNG",en:"DURIAN"},slug:"durian"},{id:"jackfruit",name:{vi:"MÍT",en:"JACKFRUIT"},slug:"jackfruit"},{id:"mango",name:{vi:"XOÀI",en:"MANGO"},slug:"mango"},{id:"coconut",name:{vi:"DỪA",en:"COCONUT"},slug:"coconut"},{id:"pineapple",name:{vi:"KHÓM",en:"PINEAPPLE"},slug:"pineapple"}]},{id:"frozen-fruit",name:{vi:"TRÁI CÂY ĐÔNG LẠNH",en:"FROZEN FRUIT"},products:[{id:"frozen-avocado",name:{vi:"BƠ ĐÔNG LẠNH",en:"FROZEN AVOCADO"},slug:"frozen-avocado"},{id:"frozen-banana",name:{vi:"CHUỐI ĐÔNG LẠNH",en:"FROZEN BANANA"},slug:"frozen-banana"},{id:"frozen-pitaya",name:{vi:"THANH LONG ĐÔNG LẠNH",en:"FROZEN PITAYA"},slug:"frozen-pitaya"},{id:"frozen-durian",name:{vi:"SẦU RIÊNG ĐÔNG LẠNH",en:"FROZEN DURIAN"},slug:"frozen-durian"},{id:"frozen-jackfruit",name:{vi:"MÍT ĐÔNG LẠNH",en:"FROZEN JACKFRUIT"},slug:"frozen-jackfruit"},{id:"frozen-mango",name:{vi:"XOÀI ĐÔNG LẠNH",en:"FROZEN MANGO"},slug:"frozen-mango"},{id:"frozen-coconut-meat",name:{vi:"CƠM DỪA ĐÔNG LẠNH",en:"FROZEN COCONUT MEAT"},slug:"frozen-coconut-meat"},{id:"frozen-pineapple",name:{vi:"KHÓM ĐÔNG LẠNH",en:"FROZEN PINEAPPLE"},slug:"frozen-pineapple"}]},{id:"dried-fruit",name:{vi:"TRÁI CÂY SẤY KHÔ",en:"DRIED FRUIT"},products:[{id:"dried-banana",name:{vi:"CHUỐI SẤY KHÔ",en:"DRIED BANANA"},slug:"dried-banana"},{id:"dried-jackfruit",name:{vi:"MÍT SẤY KHÔ",en:"DRIED JACKFRUIT"},slug:"dried-jackfruit"},{id:"dried-mango",name:{vi:"XOÀI SẤY KHÔ",en:"DRIED MANGO"},slug:"dried-mango"},{id:"dried-cashew-nut",name:{vi:"HẠT ĐIỀU SẤY KHÔ",en:"DRIED CASHEW NUT"},slug:"dried-cashew-nut"},{id:"dried-carrots",name:{vi:"CÀ RỐT SẤY KHÔ",en:"DRIED CARROTS"},slug:"dried-carrots"},{id:"dried-lotus-seed",name:{vi:"HẠT SEN SẤY KHÔ",en:"DRIED LOTUS SEED"},slug:"dried-lotus-seed"},{id:"dried-coconut",name:{vi:"DỪA SẤY KHÔ",en:"DRIED COCONUT"},slug:"dried-coconut"},{id:"dried-sweet-potato",name:{vi:"KHOAI LANG SẤY KHÔ",en:"DRIED SWEET POTATO"},slug:"dried-sweet-potato"}]},{id:"freeze-dried-fruit",name:{vi:"TRÁI CÂY ĐÔNG KHÔ",en:"FREEZE DRIED FRUIT"},products:[{id:"fd-avocado",name:{vi:"BƠ ĐÔNG KHÔ",en:"FD AVOCADO"},slug:"fd-avocado"},{id:"fd-banana",name:{vi:"CHUỐI ĐÔNG KHÔ",en:"FD BANANA"},slug:"fd-banana"},{id:"fd-pitaya",name:{vi:"THANH LONG ĐÔNG KHÔ",en:"FD PITAYA"},slug:"fd-pitaya"},{id:"fd-durian",name:{vi:"SẦU RIÊNG ĐÔNG KHÔ",en:"FD DURIAN"},slug:"fd-durian"},{id:"fd-jackfruit",name:{vi:"MÍT ĐÔNG KHÔ",en:"FD JACKFRUIT"},slug:"fd-jackfruit"},{id:"fd-mango",name:{vi:"XOÀI ĐÔNG KHÔ",en:"FD MANGO"},slug:"fd-mango"},{id:"fd-rambutan",name:{vi:"CHÔM CHÔM ĐÔNG KHÔ",en:"FD RAMBUTAN"},slug:"fd-rambutan"},{id:"fd-pineapple",name:{vi:"KHÓM ĐÔNG KHÔ",en:"FD PINEAPPLE"},slug:"fd-pineapple"}]}].map(a=>({...a,products:a.products.filter(o=>o.name[e].toLowerCase().includes(n.toLowerCase()))})).filter(a=>a.products.length>0);return u.jsxs("div",{children:[u.jsxs("section",{className:"relative h-[300px] bg-cover bg-center",style:{backgroundImage:"url(https://images.unsplash.com/photo-1490885578174-acda8905c2c6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80)"},children:[u.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-60"}),u.jsxs("div",{className:"absolute inset-0 flex flex-col items-center justify-center text-white text-center px-4",children:[u.jsx("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:t.pageTitle[e]}),u.jsx("div",{className:"w-20 h-1 bg-green-600 mb-6"}),u.jsx("p",{className:"text-xl max-w-2xl",children:t.heroTitle[e]})]})]}),u.jsx("section",{className:"py-16",children:u.jsxs("div",{className:"container mx-auto px-4",children:[u.jsx("div",{className:"mb-12",children:u.jsx("div",{className:"max-w-md mx-auto",children:u.jsx("input",{type:"text",placeholder:t.searchPlaceholder[e],className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-600",value:n,onChange:a=>i(a.target.value)})})}),u.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:s.map(a=>u.jsx(Vg,{language:e,category:a},a.id))})]})})]})};function mu(e){return e!==null&&typeof e=="object"&&"constructor"in e&&e.constructor===Object}function Zo(e,t){e===void 0&&(e={}),t===void 0&&(t={});const n=["__proto__","constructor","prototype"];Object.keys(t).filter(i=>n.indexOf(i)<0).forEach(i=>{typeof e[i]>"u"?e[i]=t[i]:mu(t[i])&&mu(e[i])&&Object.keys(t[i]).length>0&&Zo(e[i],t[i])})}const Zd={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function nn(){const e=typeof document<"u"?document:{};return Zo(e,Zd),e}const $g={document:Zd,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(e){return typeof setTimeout>"u"?(e(),null):setTimeout(e,0)},cancelAnimationFrame(e){typeof setTimeout>"u"||clearTimeout(e)}};function be(){const e=typeof window<"u"?window:{};return Zo(e,$g),e}function Wg(e){return e===void 0&&(e=""),e.trim().split(" ").filter(t=>!!t.trim())}function Yg(e){const t=e;Object.keys(t).forEach(n=>{try{t[n]=null}catch{}try{delete t[n]}catch{}})}function Ga(e,t){return t===void 0&&(t=0),setTimeout(e,t)}function Br(){return Date.now()}function Kg(e){const t=be();let n;return t.getComputedStyle&&(n=t.getComputedStyle(e,null)),!n&&e.currentStyle&&(n=e.currentStyle),n||(n=e.style),n}function qg(e,t){t===void 0&&(t="x");const n=be();let i,r,s;const a=Kg(e);return n.WebKitCSSMatrix?(r=a.transform||a.webkitTransform,r.split(",").length>6&&(r=r.split(", ").map(o=>o.replace(",",".")).join(", ")),s=new n.WebKitCSSMatrix(r==="none"?"":r)):(s=a.MozTransform||a.OTransform||a.MsTransform||a.msTransform||a.transform||a.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),i=s.toString().split(",")),t==="x"&&(n.WebKitCSSMatrix?r=s.m41:i.length===16?r=parseFloat(i[12]):r=parseFloat(i[4])),t==="y"&&(n.WebKitCSSMatrix?r=s.m42:i.length===16?r=parseFloat(i[13]):r=parseFloat(i[5])),r||0}function er(e){return typeof e=="object"&&e!==null&&e.constructor&&Object.prototype.toString.call(e).slice(8,-1)==="Object"}function Zg(e){return typeof window<"u"&&typeof window.HTMLElement<"u"?e instanceof HTMLElement:e&&(e.nodeType===1||e.nodeType===11)}function Fe(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let n=1;n<arguments.length;n+=1){const i=n<0||arguments.length<=n?void 0:arguments[n];if(i!=null&&!Zg(i)){const r=Object.keys(Object(i)).filter(s=>t.indexOf(s)<0);for(let s=0,a=r.length;s<a;s+=1){const o=r[s],l=Object.getOwnPropertyDescriptor(i,o);l!==void 0&&l.enumerable&&(er(e[o])&&er(i[o])?i[o].__swiper__?e[o]=i[o]:Fe(e[o],i[o]):!er(e[o])&&er(i[o])?(e[o]={},i[o].__swiper__?e[o]=i[o]:Fe(e[o],i[o])):e[o]=i[o])}}}return e}function tr(e,t,n){e.style.setProperty(t,n)}function Qd(e){let{swiper:t,targetPosition:n,side:i}=e;const r=be(),s=-t.translate;let a=null,o;const l=t.params.speed;t.wrapperEl.style.scrollSnapType="none",r.cancelAnimationFrame(t.cssModeFrameID);const c=n>s?"next":"prev",d=(g,y)=>c==="next"&&g>=y||c==="prev"&&g<=y,h=()=>{o=new Date().getTime(),a===null&&(a=o);const g=Math.max(Math.min((o-a)/l,1),0),y=.5-Math.cos(g*Math.PI)/2;let v=s+y*(n-s);if(d(v,n)&&(v=n),t.wrapperEl.scrollTo({[i]:v}),d(v,n)){t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout(()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[i]:v})}),r.cancelAnimationFrame(t.cssModeFrameID);return}t.cssModeFrameID=r.requestAnimationFrame(h)};h()}function Qe(e,t){t===void 0&&(t="");const n=be(),i=[...e.children];return n.HTMLSlotElement&&e instanceof HTMLSlotElement&&i.push(...e.assignedElements()),t?i.filter(r=>r.matches(t)):i}function Qg(e,t){const n=[t];for(;n.length>0;){const i=n.shift();if(e===i)return!0;n.push(...i.children,...i.shadowRoot?i.shadowRoot.children:[],...i.assignedElements?i.assignedElements():[])}}function Xg(e,t){const n=be();let i=t.contains(e);return!i&&n.HTMLSlotElement&&t instanceof HTMLSlotElement&&(i=[...t.assignedElements()].includes(e),i||(i=Qg(e,t))),i}function Vr(e){try{console.warn(e);return}catch{}}function Gr(e,t){t===void 0&&(t=[]);const n=document.createElement(e);return n.classList.add(...Array.isArray(t)?t:Wg(t)),n}function Jg(e,t){const n=[];for(;e.previousElementSibling;){const i=e.previousElementSibling;t?i.matches(t)&&n.push(i):n.push(i),e=i}return n}function ev(e,t){const n=[];for(;e.nextElementSibling;){const i=e.nextElementSibling;t?i.matches(t)&&n.push(i):n.push(i),e=i}return n}function Et(e,t){return be().getComputedStyle(e,null).getPropertyValue(t)}function $r(e){let t=e,n;if(t){for(n=0;(t=t.previousSibling)!==null;)t.nodeType===1&&(n+=1);return n}}function Xd(e,t){const n=[];let i=e.parentElement;for(;i;)t?i.matches(t)&&n.push(i):n.push(i),i=i.parentElement;return n}function $a(e,t,n){const i=be();return e[t==="width"?"offsetWidth":"offsetHeight"]+parseFloat(i.getComputedStyle(e,null).getPropertyValue(t==="width"?"margin-right":"margin-top"))+parseFloat(i.getComputedStyle(e,null).getPropertyValue(t==="width"?"margin-left":"margin-bottom"))}function ae(e){return(Array.isArray(e)?e:[e]).filter(t=>!!t)}function Wr(e,t){t===void 0&&(t=""),typeof trustedTypes<"u"?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:n=>n}).createHTML(t):e.innerHTML=t}let Us;function tv(){const e=be(),t=nn();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}function Jd(){return Us||(Us=tv()),Us}let Bs;function nv(e){let{userAgent:t}=e===void 0?{}:e;const n=Jd(),i=be(),r=i.navigator.platform,s=t||i.navigator.userAgent,a={ios:!1,android:!1},o=i.screen.width,l=i.screen.height,c=s.match(/(Android);?[\s\/]+([\d.]+)?/);let d=s.match(/(iPad).*OS\s([\d_]+)/);const h=s.match(/(iPod)(.*OS\s([\d_]+))?/),g=!d&&s.match(/(iPhone\sOS|iOS)\s([\d_]+)/),y=r==="Win32";let v=r==="MacIntel";const x=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!d&&v&&n.touch&&x.indexOf(`${o}x${l}`)>=0&&(d=s.match(/(Version)\/([\d.]+)/),d||(d=[0,1,"13_0_0"]),v=!1),c&&!y&&(a.os="android",a.android=!0),(d||g||h)&&(a.os="ios",a.ios=!0),a}function ef(e){return e===void 0&&(e={}),Bs||(Bs=nv(e)),Bs}let Vs;function iv(){const e=be(),t=ef();let n=!1;function i(){const o=e.navigator.userAgent.toLowerCase();return o.indexOf("safari")>=0&&o.indexOf("chrome")<0&&o.indexOf("android")<0}if(i()){const o=String(e.navigator.userAgent);if(o.includes("Version/")){const[l,c]=o.split("Version/")[1].split(" ")[0].split(".").map(d=>Number(d));n=l<16||l===16&&c<2}}const r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),s=i(),a=s||r&&t.ios;return{isSafari:n||s,needPerspectiveFix:n,need3dFix:a,isWebView:r}}function tf(){return Vs||(Vs=iv()),Vs}function rv(e){let{swiper:t,on:n,emit:i}=e;const r=be();let s=null,a=null;const o=()=>{!t||t.destroyed||!t.initialized||(i("beforeResize"),i("resize"))},l=()=>{!t||t.destroyed||!t.initialized||(s=new ResizeObserver(h=>{a=r.requestAnimationFrame(()=>{const{width:g,height:y}=t;let v=g,x=y;h.forEach(S=>{let{contentBoxSize:p,contentRect:f,target:m}=S;m&&m!==t.el||(v=f?f.width:(p[0]||p).inlineSize,x=f?f.height:(p[0]||p).blockSize)}),(v!==g||x!==y)&&o()})}),s.observe(t.el))},c=()=>{a&&r.cancelAnimationFrame(a),s&&s.unobserve&&t.el&&(s.unobserve(t.el),s=null)},d=()=>{!t||t.destroyed||!t.initialized||i("orientationchange")};n("init",()=>{if(t.params.resizeObserver&&typeof r.ResizeObserver<"u"){l();return}r.addEventListener("resize",o),r.addEventListener("orientationchange",d)}),n("destroy",()=>{c(),r.removeEventListener("resize",o),r.removeEventListener("orientationchange",d)})}function sv(e){let{swiper:t,extendParams:n,on:i,emit:r}=e;const s=[],a=be(),o=function(d,h){h===void 0&&(h={});const g=a.MutationObserver||a.WebkitMutationObserver,y=new g(v=>{if(t.__preventObserver__)return;if(v.length===1){r("observerUpdate",v[0]);return}const x=function(){r("observerUpdate",v[0])};a.requestAnimationFrame?a.requestAnimationFrame(x):a.setTimeout(x,0)});y.observe(d,{attributes:typeof h.attributes>"u"?!0:h.attributes,childList:t.isElement||(typeof h.childList>"u"?!0:h).childList,characterData:typeof h.characterData>"u"?!0:h.characterData}),s.push(y)},l=()=>{if(t.params.observer){if(t.params.observeParents){const d=Xd(t.hostEl);for(let h=0;h<d.length;h+=1)o(d[h])}o(t.hostEl,{childList:t.params.observeSlideChildren}),o(t.wrapperEl,{attributes:!1})}},c=()=>{s.forEach(d=>{d.disconnect()}),s.splice(0,s.length)};n({observer:!1,observeParents:!1,observeSlideChildren:!1}),i("init",l),i("destroy",c)}var av={on(e,t,n){const i=this;if(!i.eventsListeners||i.destroyed||typeof t!="function")return i;const r=n?"unshift":"push";return e.split(" ").forEach(s=>{i.eventsListeners[s]||(i.eventsListeners[s]=[]),i.eventsListeners[s][r](t)}),i},once(e,t,n){const i=this;if(!i.eventsListeners||i.destroyed||typeof t!="function")return i;function r(){i.off(e,r),r.__emitterProxy&&delete r.__emitterProxy;for(var s=arguments.length,a=new Array(s),o=0;o<s;o++)a[o]=arguments[o];t.apply(i,a)}return r.__emitterProxy=t,i.on(e,r,n)},onAny(e,t){const n=this;if(!n.eventsListeners||n.destroyed||typeof e!="function")return n;const i=t?"unshift":"push";return n.eventsAnyListeners.indexOf(e)<0&&n.eventsAnyListeners[i](e),n},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed||!t.eventsAnyListeners)return t;const n=t.eventsAnyListeners.indexOf(e);return n>=0&&t.eventsAnyListeners.splice(n,1),t},off(e,t){const n=this;return!n.eventsListeners||n.destroyed||!n.eventsListeners||e.split(" ").forEach(i=>{typeof t>"u"?n.eventsListeners[i]=[]:n.eventsListeners[i]&&n.eventsListeners[i].forEach((r,s)=>{(r===t||r.__emitterProxy&&r.__emitterProxy===t)&&n.eventsListeners[i].splice(s,1)})}),n},emit(){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsListeners)return e;let t,n,i;for(var r=arguments.length,s=new Array(r),a=0;a<r;a++)s[a]=arguments[a];return typeof s[0]=="string"||Array.isArray(s[0])?(t=s[0],n=s.slice(1,s.length),i=e):(t=s[0].events,n=s[0].data,i=s[0].context||e),n.unshift(i),(Array.isArray(t)?t:t.split(" ")).forEach(l=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach(c=>{c.apply(i,[l,...n])}),e.eventsListeners&&e.eventsListeners[l]&&e.eventsListeners[l].forEach(c=>{c.apply(i,n)})}),e}};function ov(){const e=this;let t,n;const i=e.el;typeof e.params.width<"u"&&e.params.width!==null?t=e.params.width:t=i.clientWidth,typeof e.params.height<"u"&&e.params.height!==null?n=e.params.height:n=i.clientHeight,!(t===0&&e.isHorizontal()||n===0&&e.isVertical())&&(t=t-parseInt(Et(i,"padding-left")||0,10)-parseInt(Et(i,"padding-right")||0,10),n=n-parseInt(Et(i,"padding-top")||0,10)-parseInt(Et(i,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(n)&&(n=0),Object.assign(e,{width:t,height:n,size:e.isHorizontal()?t:n}))}function lv(){const e=this;function t(C,R){return parseFloat(C.getPropertyValue(e.getDirectionLabel(R))||0)}const n=e.params,{wrapperEl:i,slidesEl:r,size:s,rtlTranslate:a,wrongRTL:o}=e,l=e.virtual&&n.virtual.enabled,c=l?e.virtual.slides.length:e.slides.length,d=Qe(r,`.${e.params.slideClass}, swiper-slide`),h=l?e.virtual.slides.length:d.length;let g=[];const y=[],v=[];let x=n.slidesOffsetBefore;typeof x=="function"&&(x=n.slidesOffsetBefore.call(e));let S=n.slidesOffsetAfter;typeof S=="function"&&(S=n.slidesOffsetAfter.call(e));const p=e.snapGrid.length,f=e.slidesGrid.length;let m=n.spaceBetween,w=-x,b=0,E=0;if(typeof s>"u")return;typeof m=="string"&&m.indexOf("%")>=0?m=parseFloat(m.replace("%",""))/100*s:typeof m=="string"&&(m=parseFloat(m)),e.virtualSize=-m,d.forEach(C=>{a?C.style.marginLeft="":C.style.marginRight="",C.style.marginBottom="",C.style.marginTop=""}),n.centeredSlides&&n.cssMode&&(tr(i,"--swiper-centered-offset-before",""),tr(i,"--swiper-centered-offset-after",""));const N=n.grid&&n.grid.rows>1&&e.grid;N?e.grid.initSlides(d):e.grid&&e.grid.unsetSlides();let T;const j=n.slidesPerView==="auto"&&n.breakpoints&&Object.keys(n.breakpoints).filter(C=>typeof n.breakpoints[C].slidesPerView<"u").length>0;for(let C=0;C<h;C+=1){T=0;let R;if(d[C]&&(R=d[C]),N&&e.grid.updateSlide(C,R,d),!(d[C]&&Et(R,"display")==="none")){if(n.slidesPerView==="auto"){j&&(d[C].style[e.getDirectionLabel("width")]="");const L=getComputedStyle(R),O=R.style.transform,H=R.style.webkitTransform;if(O&&(R.style.transform="none"),H&&(R.style.webkitTransform="none"),n.roundLengths)T=e.isHorizontal()?$a(R,"width"):$a(R,"height");else{const U=t(L,"width"),A=t(L,"padding-left"),X=t(L,"padding-right"),I=t(L,"margin-left"),P=t(L,"margin-right"),M=L.getPropertyValue("box-sizing");if(M&&M==="border-box")T=U+I+P;else{const{clientWidth:Y,offsetWidth:te}=R;T=U+A+X+I+P+(te-Y)}}O&&(R.style.transform=O),H&&(R.style.webkitTransform=H),n.roundLengths&&(T=Math.floor(T))}else T=(s-(n.slidesPerView-1)*m)/n.slidesPerView,n.roundLengths&&(T=Math.floor(T)),d[C]&&(d[C].style[e.getDirectionLabel("width")]=`${T}px`);d[C]&&(d[C].swiperSlideSize=T),v.push(T),n.centeredSlides?(w=w+T/2+b/2+m,b===0&&C!==0&&(w=w-s/2-m),C===0&&(w=w-s/2-m),Math.abs(w)<1/1e3&&(w=0),n.roundLengths&&(w=Math.floor(w)),E%n.slidesPerGroup===0&&g.push(w),y.push(w)):(n.roundLengths&&(w=Math.floor(w)),(E-Math.min(e.params.slidesPerGroupSkip,E))%e.params.slidesPerGroup===0&&g.push(w),y.push(w),w=w+T+m),e.virtualSize+=T+m,b=T,E+=1}}if(e.virtualSize=Math.max(e.virtualSize,s)+S,a&&o&&(n.effect==="slide"||n.effect==="coverflow")&&(i.style.width=`${e.virtualSize+m}px`),n.setWrapperSize&&(i.style[e.getDirectionLabel("width")]=`${e.virtualSize+m}px`),N&&e.grid.updateWrapperSize(T,g),!n.centeredSlides){const C=[];for(let R=0;R<g.length;R+=1){let L=g[R];n.roundLengths&&(L=Math.floor(L)),g[R]<=e.virtualSize-s&&C.push(L)}g=C,Math.floor(e.virtualSize-s)-Math.floor(g[g.length-1])>1&&g.push(e.virtualSize-s)}if(l&&n.loop){const C=v[0]+m;if(n.slidesPerGroup>1){const R=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/n.slidesPerGroup),L=C*n.slidesPerGroup;for(let O=0;O<R;O+=1)g.push(g[g.length-1]+L)}for(let R=0;R<e.virtual.slidesBefore+e.virtual.slidesAfter;R+=1)n.slidesPerGroup===1&&g.push(g[g.length-1]+C),y.push(y[y.length-1]+C),e.virtualSize+=C}if(g.length===0&&(g=[0]),m!==0){const C=e.isHorizontal()&&a?"marginLeft":e.getDirectionLabel("marginRight");d.filter((R,L)=>!n.cssMode||n.loop?!0:L!==d.length-1).forEach(R=>{R.style[C]=`${m}px`})}if(n.centeredSlides&&n.centeredSlidesBounds){let C=0;v.forEach(L=>{C+=L+(m||0)}),C-=m;const R=C>s?C-s:0;g=g.map(L=>L<=0?-x:L>R?R+S:L)}if(n.centerInsufficientSlides){let C=0;v.forEach(L=>{C+=L+(m||0)}),C-=m;const R=(n.slidesOffsetBefore||0)+(n.slidesOffsetAfter||0);if(C+R<s){const L=(s-C-R)/2;g.forEach((O,H)=>{g[H]=O-L}),y.forEach((O,H)=>{y[H]=O+L})}}if(Object.assign(e,{slides:d,snapGrid:g,slidesGrid:y,slidesSizesGrid:v}),n.centeredSlides&&n.cssMode&&!n.centeredSlidesBounds){tr(i,"--swiper-centered-offset-before",`${-g[0]}px`),tr(i,"--swiper-centered-offset-after",`${e.size/2-v[v.length-1]/2}px`);const C=-e.snapGrid[0],R=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map(L=>L+C),e.slidesGrid=e.slidesGrid.map(L=>L+R)}if(h!==c&&e.emit("slidesLengthChange"),g.length!==p&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),y.length!==f&&e.emit("slidesGridLengthChange"),n.watchSlidesProgress&&e.updateSlidesOffset(),e.emit("slidesUpdated"),!l&&!n.cssMode&&(n.effect==="slide"||n.effect==="fade")){const C=`${n.containerModifierClass}backface-hidden`,R=e.el.classList.contains(C);h<=n.maxBackfaceHiddenSlides?R||e.el.classList.add(C):R&&e.el.classList.remove(C)}}function uv(e){const t=this,n=[],i=t.virtual&&t.params.virtual.enabled;let r=0,s;typeof e=="number"?t.setTransition(e):e===!0&&t.setTransition(t.params.speed);const a=o=>i?t.slides[t.getSlideIndexByData(o)]:t.slides[o];if(t.params.slidesPerView!=="auto"&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach(o=>{n.push(o)});else for(s=0;s<Math.ceil(t.params.slidesPerView);s+=1){const o=t.activeIndex+s;if(o>t.slides.length&&!i)break;n.push(a(o))}else n.push(a(t.activeIndex));for(s=0;s<n.length;s+=1)if(typeof n[s]<"u"){const o=n[s].offsetHeight;r=o>r?o:r}(r||r===0)&&(t.wrapperEl.style.height=`${r}px`)}function cv(){const e=this,t=e.slides,n=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let i=0;i<t.length;i+=1)t[i].swiperSlideOffset=(e.isHorizontal()?t[i].offsetLeft:t[i].offsetTop)-n-e.cssOverflowAdjustment()}const gu=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)};function dv(e){e===void 0&&(e=this&&this.translate||0);const t=this,n=t.params,{slides:i,rtlTranslate:r,snapGrid:s}=t;if(i.length===0)return;typeof i[0].swiperSlideOffset>"u"&&t.updateSlidesOffset();let a=-e;r&&(a=e),t.visibleSlidesIndexes=[],t.visibleSlides=[];let o=n.spaceBetween;typeof o=="string"&&o.indexOf("%")>=0?o=parseFloat(o.replace("%",""))/100*t.size:typeof o=="string"&&(o=parseFloat(o));for(let l=0;l<i.length;l+=1){const c=i[l];let d=c.swiperSlideOffset;n.cssMode&&n.centeredSlides&&(d-=i[0].swiperSlideOffset);const h=(a+(n.centeredSlides?t.minTranslate():0)-d)/(c.swiperSlideSize+o),g=(a-s[0]+(n.centeredSlides?t.minTranslate():0)-d)/(c.swiperSlideSize+o),y=-(a-d),v=y+t.slidesSizesGrid[l],x=y>=0&&y<=t.size-t.slidesSizesGrid[l],S=y>=0&&y<t.size-1||v>1&&v<=t.size||y<=0&&v>=t.size;S&&(t.visibleSlides.push(c),t.visibleSlidesIndexes.push(l)),gu(c,S,n.slideVisibleClass),gu(c,x,n.slideFullyVisibleClass),c.progress=r?-h:h,c.originalProgress=r?-g:g}}function fv(e){const t=this;if(typeof e>"u"){const d=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*d||0}const n=t.params,i=t.maxTranslate()-t.minTranslate();let{progress:r,isBeginning:s,isEnd:a,progressLoop:o}=t;const l=s,c=a;if(i===0)r=0,s=!0,a=!0;else{r=(e-t.minTranslate())/i;const d=Math.abs(e-t.minTranslate())<1,h=Math.abs(e-t.maxTranslate())<1;s=d||r<=0,a=h||r>=1,d&&(r=0),h&&(r=1)}if(n.loop){const d=t.getSlideIndexByData(0),h=t.getSlideIndexByData(t.slides.length-1),g=t.slidesGrid[d],y=t.slidesGrid[h],v=t.slidesGrid[t.slidesGrid.length-1],x=Math.abs(e);x>=g?o=(x-g)/v:o=(x+v-y)/v,o>1&&(o-=1)}Object.assign(t,{progress:r,progressLoop:o,isBeginning:s,isEnd:a}),(n.watchSlidesProgress||n.centeredSlides&&n.autoHeight)&&t.updateSlidesProgress(e),s&&!l&&t.emit("reachBeginning toEdge"),a&&!c&&t.emit("reachEnd toEdge"),(l&&!s||c&&!a)&&t.emit("fromEdge"),t.emit("progress",r)}const Gs=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)};function hv(){const e=this,{slides:t,params:n,slidesEl:i,activeIndex:r}=e,s=e.virtual&&n.virtual.enabled,a=e.grid&&n.grid&&n.grid.rows>1,o=h=>Qe(i,`.${n.slideClass}${h}, swiper-slide${h}`)[0];let l,c,d;if(s)if(n.loop){let h=r-e.virtual.slidesBefore;h<0&&(h=e.virtual.slides.length+h),h>=e.virtual.slides.length&&(h-=e.virtual.slides.length),l=o(`[data-swiper-slide-index="${h}"]`)}else l=o(`[data-swiper-slide-index="${r}"]`);else a?(l=t.find(h=>h.column===r),d=t.find(h=>h.column===r+1),c=t.find(h=>h.column===r-1)):l=t[r];l&&(a||(d=ev(l,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!d&&(d=t[0]),c=Jg(l,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!c===0&&(c=t[t.length-1]))),t.forEach(h=>{Gs(h,h===l,n.slideActiveClass),Gs(h,h===d,n.slideNextClass),Gs(h,h===c,n.slidePrevClass)}),e.emitSlidesClasses()}const gr=(e,t)=>{if(!e||e.destroyed||!e.params)return;const n=()=>e.isElement?"swiper-slide":`.${e.params.slideClass}`,i=t.closest(n());if(i){let r=i.querySelector(`.${e.params.lazyPreloaderClass}`);!r&&e.isElement&&(i.shadowRoot?r=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(r=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`),r&&r.remove())})),r&&r.remove()}},$s=(e,t)=>{if(!e.slides[t])return;const n=e.slides[t].querySelector('[loading="lazy"]');n&&n.removeAttribute("loading")},Wa=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const n=e.slides.length;if(!n||!t||t<0)return;t=Math.min(t,n);const i=e.params.slidesPerView==="auto"?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),r=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const a=r,o=[a-t];o.push(...Array.from({length:t}).map((l,c)=>a+i+c)),e.slides.forEach((l,c)=>{o.includes(l.column)&&$s(e,c)});return}const s=r+i-1;if(e.params.rewind||e.params.loop)for(let a=r-t;a<=s+t;a+=1){const o=(a%n+n)%n;(o<r||o>s)&&$s(e,o)}else for(let a=Math.max(r-t,0);a<=Math.min(s+t,n-1);a+=1)a!==r&&(a>s||a<r)&&$s(e,a)};function pv(e){const{slidesGrid:t,params:n}=e,i=e.rtlTranslate?e.translate:-e.translate;let r;for(let s=0;s<t.length;s+=1)typeof t[s+1]<"u"?i>=t[s]&&i<t[s+1]-(t[s+1]-t[s])/2?r=s:i>=t[s]&&i<t[s+1]&&(r=s+1):i>=t[s]&&(r=s);return n.normalizeSlideIndex&&(r<0||typeof r>"u")&&(r=0),r}function mv(e){const t=this,n=t.rtlTranslate?t.translate:-t.translate,{snapGrid:i,params:r,activeIndex:s,realIndex:a,snapIndex:o}=t;let l=e,c;const d=y=>{let v=y-t.virtual.slidesBefore;return v<0&&(v=t.virtual.slides.length+v),v>=t.virtual.slides.length&&(v-=t.virtual.slides.length),v};if(typeof l>"u"&&(l=pv(t)),i.indexOf(n)>=0)c=i.indexOf(n);else{const y=Math.min(r.slidesPerGroupSkip,l);c=y+Math.floor((l-y)/r.slidesPerGroup)}if(c>=i.length&&(c=i.length-1),l===s&&!t.params.loop){c!==o&&(t.snapIndex=c,t.emit("snapIndexChange"));return}if(l===s&&t.params.loop&&t.virtual&&t.params.virtual.enabled){t.realIndex=d(l);return}const h=t.grid&&r.grid&&r.grid.rows>1;let g;if(t.virtual&&r.virtual.enabled&&r.loop)g=d(l);else if(h){const y=t.slides.find(x=>x.column===l);let v=parseInt(y.getAttribute("data-swiper-slide-index"),10);Number.isNaN(v)&&(v=Math.max(t.slides.indexOf(y),0)),g=Math.floor(v/r.grid.rows)}else if(t.slides[l]){const y=t.slides[l].getAttribute("data-swiper-slide-index");y?g=parseInt(y,10):g=l}else g=l;Object.assign(t,{previousSnapIndex:o,snapIndex:c,previousRealIndex:a,realIndex:g,previousIndex:s,activeIndex:l}),t.initialized&&Wa(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(a!==g&&t.emit("realIndexChange"),t.emit("slideChange"))}function gv(e,t){const n=this,i=n.params;let r=e.closest(`.${i.slideClass}, swiper-slide`);!r&&n.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(o=>{!r&&o.matches&&o.matches(`.${i.slideClass}, swiper-slide`)&&(r=o)});let s=!1,a;if(r){for(let o=0;o<n.slides.length;o+=1)if(n.slides[o]===r){s=!0,a=o;break}}if(r&&s)n.clickedSlide=r,n.virtual&&n.params.virtual.enabled?n.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):n.clickedIndex=a;else{n.clickedSlide=void 0,n.clickedIndex=void 0;return}i.slideToClickedSlide&&n.clickedIndex!==void 0&&n.clickedIndex!==n.activeIndex&&n.slideToClickedSlide()}var vv={updateSize:ov,updateSlides:lv,updateAutoHeight:uv,updateSlidesOffset:cv,updateSlidesProgress:dv,updateProgress:fv,updateSlidesClasses:hv,updateActiveIndex:mv,updateClickedSlide:gv};function yv(e){e===void 0&&(e=this.isHorizontal()?"x":"y");const t=this,{params:n,rtlTranslate:i,translate:r,wrapperEl:s}=t;if(n.virtualTranslate)return i?-r:r;if(n.cssMode)return r;let a=qg(s,e);return a+=t.cssOverflowAdjustment(),i&&(a=-a),a||0}function xv(e,t){const n=this,{rtlTranslate:i,params:r,wrapperEl:s,progress:a}=n;let o=0,l=0;const c=0;n.isHorizontal()?o=i?-e:e:l=e,r.roundLengths&&(o=Math.floor(o),l=Math.floor(l)),n.previousTranslate=n.translate,n.translate=n.isHorizontal()?o:l,r.cssMode?s[n.isHorizontal()?"scrollLeft":"scrollTop"]=n.isHorizontal()?-o:-l:r.virtualTranslate||(n.isHorizontal()?o-=n.cssOverflowAdjustment():l-=n.cssOverflowAdjustment(),s.style.transform=`translate3d(${o}px, ${l}px, ${c}px)`);let d;const h=n.maxTranslate()-n.minTranslate();h===0?d=0:d=(e-n.minTranslate())/h,d!==a&&n.updateProgress(e),n.emit("setTranslate",n.translate,t)}function wv(){return-this.snapGrid[0]}function kv(){return-this.snapGrid[this.snapGrid.length-1]}function bv(e,t,n,i,r){e===void 0&&(e=0),t===void 0&&(t=this.params.speed),n===void 0&&(n=!0),i===void 0&&(i=!0);const s=this,{params:a,wrapperEl:o}=s;if(s.animating&&a.preventInteractionOnTransition)return!1;const l=s.minTranslate(),c=s.maxTranslate();let d;if(i&&e>l?d=l:i&&e<c?d=c:d=e,s.updateProgress(d),a.cssMode){const h=s.isHorizontal();if(t===0)o[h?"scrollLeft":"scrollTop"]=-d;else{if(!s.support.smoothScroll)return Qd({swiper:s,targetPosition:-d,side:h?"left":"top"}),!0;o.scrollTo({[h?"left":"top"]:-d,behavior:"smooth"})}return!0}return t===0?(s.setTransition(0),s.setTranslate(d),n&&(s.emit("beforeTransitionStart",t,r),s.emit("transitionEnd"))):(s.setTransition(t),s.setTranslate(d),n&&(s.emit("beforeTransitionStart",t,r),s.emit("transitionStart")),s.animating||(s.animating=!0,s.onTranslateToWrapperTransitionEnd||(s.onTranslateToWrapperTransitionEnd=function(g){!s||s.destroyed||g.target===this&&(s.wrapperEl.removeEventListener("transitionend",s.onTranslateToWrapperTransitionEnd),s.onTranslateToWrapperTransitionEnd=null,delete s.onTranslateToWrapperTransitionEnd,s.animating=!1,n&&s.emit("transitionEnd"))}),s.wrapperEl.addEventListener("transitionend",s.onTranslateToWrapperTransitionEnd))),!0}var Sv={getTranslate:yv,setTranslate:xv,minTranslate:wv,maxTranslate:kv,translateTo:bv};function Ev(e,t){const n=this;n.params.cssMode||(n.wrapperEl.style.transitionDuration=`${e}ms`,n.wrapperEl.style.transitionDelay=e===0?"0ms":""),n.emit("setTransition",e,t)}function nf(e){let{swiper:t,runCallbacks:n,direction:i,step:r}=e;const{activeIndex:s,previousIndex:a}=t;let o=i;o||(s>a?o="next":s<a?o="prev":o="reset"),t.emit(`transition${r}`),n&&o==="reset"?t.emit(`slideResetTransition${r}`):n&&s!==a&&(t.emit(`slideChangeTransition${r}`),o==="next"?t.emit(`slideNextTransition${r}`):t.emit(`slidePrevTransition${r}`))}function Cv(e,t){e===void 0&&(e=!0);const n=this,{params:i}=n;i.cssMode||(i.autoHeight&&n.updateAutoHeight(),nf({swiper:n,runCallbacks:e,direction:t,step:"Start"}))}function Nv(e,t){e===void 0&&(e=!0);const n=this,{params:i}=n;n.animating=!1,!i.cssMode&&(n.setTransition(0),nf({swiper:n,runCallbacks:e,direction:t,step:"End"}))}var Tv={setTransition:Ev,transitionStart:Cv,transitionEnd:Nv};function jv(e,t,n,i,r){e===void 0&&(e=0),n===void 0&&(n=!0),typeof e=="string"&&(e=parseInt(e,10));const s=this;let a=e;a<0&&(a=0);const{params:o,snapGrid:l,slidesGrid:c,previousIndex:d,activeIndex:h,rtlTranslate:g,wrapperEl:y,enabled:v}=s;if(!v&&!i&&!r||s.destroyed||s.animating&&o.preventInteractionOnTransition)return!1;typeof t>"u"&&(t=s.params.speed);const x=Math.min(s.params.slidesPerGroupSkip,a);let S=x+Math.floor((a-x)/s.params.slidesPerGroup);S>=l.length&&(S=l.length-1);const p=-l[S];if(o.normalizeSlideIndex)for(let N=0;N<c.length;N+=1){const T=-Math.floor(p*100),j=Math.floor(c[N]*100),C=Math.floor(c[N+1]*100);typeof c[N+1]<"u"?T>=j&&T<C-(C-j)/2?a=N:T>=j&&T<C&&(a=N+1):T>=j&&(a=N)}if(s.initialized&&a!==h&&(!s.allowSlideNext&&(g?p>s.translate&&p>s.minTranslate():p<s.translate&&p<s.minTranslate())||!s.allowSlidePrev&&p>s.translate&&p>s.maxTranslate()&&(h||0)!==a))return!1;a!==(d||0)&&n&&s.emit("beforeSlideChangeStart"),s.updateProgress(p);let f;a>h?f="next":a<h?f="prev":f="reset";const m=s.virtual&&s.params.virtual.enabled;if(!(m&&r)&&(g&&-p===s.translate||!g&&p===s.translate))return s.updateActiveIndex(a),o.autoHeight&&s.updateAutoHeight(),s.updateSlidesClasses(),o.effect!=="slide"&&s.setTranslate(p),f!=="reset"&&(s.transitionStart(n,f),s.transitionEnd(n,f)),!1;if(o.cssMode){const N=s.isHorizontal(),T=g?p:-p;if(t===0)m&&(s.wrapperEl.style.scrollSnapType="none",s._immediateVirtual=!0),m&&!s._cssModeVirtualInitialSet&&s.params.initialSlide>0?(s._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{y[N?"scrollLeft":"scrollTop"]=T})):y[N?"scrollLeft":"scrollTop"]=T,m&&requestAnimationFrame(()=>{s.wrapperEl.style.scrollSnapType="",s._immediateVirtual=!1});else{if(!s.support.smoothScroll)return Qd({swiper:s,targetPosition:T,side:N?"left":"top"}),!0;y.scrollTo({[N?"left":"top"]:T,behavior:"smooth"})}return!0}const E=tf().isSafari;return m&&!r&&E&&s.isElement&&s.virtual.update(!1,!1,a),s.setTransition(t),s.setTranslate(p),s.updateActiveIndex(a),s.updateSlidesClasses(),s.emit("beforeTransitionStart",t,i),s.transitionStart(n,f),t===0?s.transitionEnd(n,f):s.animating||(s.animating=!0,s.onSlideToWrapperTransitionEnd||(s.onSlideToWrapperTransitionEnd=function(T){!s||s.destroyed||T.target===this&&(s.wrapperEl.removeEventListener("transitionend",s.onSlideToWrapperTransitionEnd),s.onSlideToWrapperTransitionEnd=null,delete s.onSlideToWrapperTransitionEnd,s.transitionEnd(n,f))}),s.wrapperEl.addEventListener("transitionend",s.onSlideToWrapperTransitionEnd)),!0}function Fv(e,t,n,i){e===void 0&&(e=0),n===void 0&&(n=!0),typeof e=="string"&&(e=parseInt(e,10));const r=this;if(r.destroyed)return;typeof t>"u"&&(t=r.params.speed);const s=r.grid&&r.params.grid&&r.params.grid.rows>1;let a=e;if(r.params.loop)if(r.virtual&&r.params.virtual.enabled)a=a+r.virtual.slidesBefore;else{let o;if(s){const g=a*r.params.grid.rows;o=r.slides.find(y=>y.getAttribute("data-swiper-slide-index")*1===g).column}else o=r.getSlideIndexByData(a);const l=s?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:c}=r.params;let d=r.params.slidesPerView;d==="auto"?d=r.slidesPerViewDynamic():(d=Math.ceil(parseFloat(r.params.slidesPerView,10)),c&&d%2===0&&(d=d+1));let h=l-o<d;if(c&&(h=h||o<Math.ceil(d/2)),i&&c&&r.params.slidesPerView!=="auto"&&!s&&(h=!1),h){const g=c?o<r.activeIndex?"prev":"next":o-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:g,slideTo:!0,activeSlideIndex:g==="next"?o+1:o-l+1,slideRealIndex:g==="next"?r.realIndex:void 0})}if(s){const g=a*r.params.grid.rows;a=r.slides.find(y=>y.getAttribute("data-swiper-slide-index")*1===g).column}else a=r.getSlideIndexByData(a)}return requestAnimationFrame(()=>{r.slideTo(a,t,n,i)}),r}function Rv(e,t,n){t===void 0&&(t=!0);const i=this,{enabled:r,params:s,animating:a}=i;if(!r||i.destroyed)return i;typeof e>"u"&&(e=i.params.speed);let o=s.slidesPerGroup;s.slidesPerView==="auto"&&s.slidesPerGroup===1&&s.slidesPerGroupAuto&&(o=Math.max(i.slidesPerViewDynamic("current",!0),1));const l=i.activeIndex<s.slidesPerGroupSkip?1:o,c=i.virtual&&s.virtual.enabled;if(s.loop){if(a&&!c&&s.loopPreventsSliding)return!1;if(i.loopFix({direction:"next"}),i._clientLeft=i.wrapperEl.clientLeft,i.activeIndex===i.slides.length-1&&s.cssMode)return requestAnimationFrame(()=>{i.slideTo(i.activeIndex+l,e,t,n)}),!0}return s.rewind&&i.isEnd?i.slideTo(0,e,t,n):i.slideTo(i.activeIndex+l,e,t,n)}function Iv(e,t,n){t===void 0&&(t=!0);const i=this,{params:r,snapGrid:s,slidesGrid:a,rtlTranslate:o,enabled:l,animating:c}=i;if(!l||i.destroyed)return i;typeof e>"u"&&(e=i.params.speed);const d=i.virtual&&r.virtual.enabled;if(r.loop){if(c&&!d&&r.loopPreventsSliding)return!1;i.loopFix({direction:"prev"}),i._clientLeft=i.wrapperEl.clientLeft}const h=o?i.translate:-i.translate;function g(f){return f<0?-Math.floor(Math.abs(f)):Math.floor(f)}const y=g(h),v=s.map(f=>g(f)),x=r.freeMode&&r.freeMode.enabled;let S=s[v.indexOf(y)-1];if(typeof S>"u"&&(r.cssMode||x)){let f;s.forEach((m,w)=>{y>=m&&(f=w)}),typeof f<"u"&&(S=x?s[f]:s[f>0?f-1:f])}let p=0;if(typeof S<"u"&&(p=a.indexOf(S),p<0&&(p=i.activeIndex-1),r.slidesPerView==="auto"&&r.slidesPerGroup===1&&r.slidesPerGroupAuto&&(p=p-i.slidesPerViewDynamic("previous",!0)+1,p=Math.max(p,0))),r.rewind&&i.isBeginning){const f=i.params.virtual&&i.params.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1;return i.slideTo(f,e,t,n)}else if(r.loop&&i.activeIndex===0&&r.cssMode)return requestAnimationFrame(()=>{i.slideTo(p,e,t,n)}),!0;return i.slideTo(p,e,t,n)}function Pv(e,t,n){t===void 0&&(t=!0);const i=this;if(!i.destroyed)return typeof e>"u"&&(e=i.params.speed),i.slideTo(i.activeIndex,e,t,n)}function Dv(e,t,n,i){t===void 0&&(t=!0),i===void 0&&(i=.5);const r=this;if(r.destroyed)return;typeof e>"u"&&(e=r.params.speed);let s=r.activeIndex;const a=Math.min(r.params.slidesPerGroupSkip,s),o=a+Math.floor((s-a)/r.params.slidesPerGroup),l=r.rtlTranslate?r.translate:-r.translate;if(l>=r.snapGrid[o]){const c=r.snapGrid[o],d=r.snapGrid[o+1];l-c>(d-c)*i&&(s+=r.params.slidesPerGroup)}else{const c=r.snapGrid[o-1],d=r.snapGrid[o];l-c<=(d-c)*i&&(s-=r.params.slidesPerGroup)}return s=Math.max(s,0),s=Math.min(s,r.slidesGrid.length-1),r.slideTo(s,e,t,n)}function Lv(){const e=this;if(e.destroyed)return;const{params:t,slidesEl:n}=e,i=t.slidesPerView==="auto"?e.slidesPerViewDynamic():t.slidesPerView;let r=e.clickedIndex,s;const a=e.isElement?"swiper-slide":`.${t.slideClass}`;if(t.loop){if(e.animating)return;s=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?r<e.loopedSlides-i/2||r>e.slides.length-e.loopedSlides+i/2?(e.loopFix(),r=e.getSlideIndex(Qe(n,`${a}[data-swiper-slide-index="${s}"]`)[0]),Ga(()=>{e.slideTo(r)})):e.slideTo(r):r>e.slides.length-i?(e.loopFix(),r=e.getSlideIndex(Qe(n,`${a}[data-swiper-slide-index="${s}"]`)[0]),Ga(()=>{e.slideTo(r)})):e.slideTo(r)}else e.slideTo(r)}var Mv={slideTo:jv,slideToLoop:Fv,slideNext:Rv,slidePrev:Iv,slideReset:Pv,slideToClosest:Dv,slideToClickedSlide:Lv};function Ov(e,t){const n=this,{params:i,slidesEl:r}=n;if(!i.loop||n.virtual&&n.params.virtual.enabled)return;const s=()=>{Qe(r,`.${i.slideClass}, swiper-slide`).forEach((g,y)=>{g.setAttribute("data-swiper-slide-index",y)})},a=n.grid&&i.grid&&i.grid.rows>1,o=i.slidesPerGroup*(a?i.grid.rows:1),l=n.slides.length%o!==0,c=a&&n.slides.length%i.grid.rows!==0,d=h=>{for(let g=0;g<h;g+=1){const y=n.isElement?Gr("swiper-slide",[i.slideBlankClass]):Gr("div",[i.slideClass,i.slideBlankClass]);n.slidesEl.append(y)}};if(l){if(i.loopAddBlankSlides){const h=o-n.slides.length%o;d(h),n.recalcSlides(),n.updateSlides()}else Vr("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");s()}else if(c){if(i.loopAddBlankSlides){const h=i.grid.rows-n.slides.length%i.grid.rows;d(h),n.recalcSlides(),n.updateSlides()}else Vr("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");s()}else s();n.loopFix({slideRealIndex:e,direction:i.centeredSlides?void 0:"next",initial:t})}function Av(e){let{slideRealIndex:t,slideTo:n=!0,direction:i,setTranslate:r,activeSlideIndex:s,initial:a,byController:o,byMousewheel:l}=e===void 0?{}:e;const c=this;if(!c.params.loop)return;c.emit("beforeLoopFix");const{slides:d,allowSlidePrev:h,allowSlideNext:g,slidesEl:y,params:v}=c,{centeredSlides:x,initialSlide:S}=v;if(c.allowSlidePrev=!0,c.allowSlideNext=!0,c.virtual&&v.virtual.enabled){n&&(!v.centeredSlides&&c.snapIndex===0?c.slideTo(c.virtual.slides.length,0,!1,!0):v.centeredSlides&&c.snapIndex<v.slidesPerView?c.slideTo(c.virtual.slides.length+c.snapIndex,0,!1,!0):c.snapIndex===c.snapGrid.length-1&&c.slideTo(c.virtual.slidesBefore,0,!1,!0)),c.allowSlidePrev=h,c.allowSlideNext=g,c.emit("loopFix");return}let p=v.slidesPerView;p==="auto"?p=c.slidesPerViewDynamic():(p=Math.ceil(parseFloat(v.slidesPerView,10)),x&&p%2===0&&(p=p+1));const f=v.slidesPerGroupAuto?p:v.slidesPerGroup;let m=f;m%f!==0&&(m+=f-m%f),m+=v.loopAdditionalSlides,c.loopedSlides=m;const w=c.grid&&v.grid&&v.grid.rows>1;d.length<p+m||c.params.effect==="cards"&&d.length<p+m*2?Vr("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):w&&v.grid.fill==="row"&&Vr("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const b=[],E=[],N=w?Math.ceil(d.length/v.grid.rows):d.length,T=a&&N-S<p&&!x;let j=T?S:c.activeIndex;typeof s>"u"?s=c.getSlideIndex(d.find(A=>A.classList.contains(v.slideActiveClass))):j=s;const C=i==="next"||!i,R=i==="prev"||!i;let L=0,O=0;const U=(w?d[s].column:s)+(x&&typeof r>"u"?-p/2+.5:0);if(U<m){L=Math.max(m-U,f);for(let A=0;A<m-U;A+=1){const X=A-Math.floor(A/N)*N;if(w){const I=N-X-1;for(let P=d.length-1;P>=0;P-=1)d[P].column===I&&b.push(P)}else b.push(N-X-1)}}else if(U+p>N-m){O=Math.max(U-(N-m*2),f),T&&(O=Math.max(O,p-N+S+1));for(let A=0;A<O;A+=1){const X=A-Math.floor(A/N)*N;w?d.forEach((I,P)=>{I.column===X&&E.push(P)}):E.push(X)}}if(c.__preventObserver__=!0,requestAnimationFrame(()=>{c.__preventObserver__=!1}),c.params.effect==="cards"&&d.length<p+m*2&&(E.includes(s)&&E.splice(E.indexOf(s),1),b.includes(s)&&b.splice(b.indexOf(s),1)),R&&b.forEach(A=>{d[A].swiperLoopMoveDOM=!0,y.prepend(d[A]),d[A].swiperLoopMoveDOM=!1}),C&&E.forEach(A=>{d[A].swiperLoopMoveDOM=!0,y.append(d[A]),d[A].swiperLoopMoveDOM=!1}),c.recalcSlides(),v.slidesPerView==="auto"?c.updateSlides():w&&(b.length>0&&R||E.length>0&&C)&&c.slides.forEach((A,X)=>{c.grid.updateSlide(X,A,c.slides)}),v.watchSlidesProgress&&c.updateSlidesOffset(),n){if(b.length>0&&R){if(typeof t>"u"){const A=c.slidesGrid[j],I=c.slidesGrid[j+L]-A;l?c.setTranslate(c.translate-I):(c.slideTo(j+Math.ceil(L),0,!1,!0),r&&(c.touchEventsData.startTranslate=c.touchEventsData.startTranslate-I,c.touchEventsData.currentTranslate=c.touchEventsData.currentTranslate-I))}else if(r){const A=w?b.length/v.grid.rows:b.length;c.slideTo(c.activeIndex+A,0,!1,!0),c.touchEventsData.currentTranslate=c.translate}}else if(E.length>0&&C)if(typeof t>"u"){const A=c.slidesGrid[j],I=c.slidesGrid[j-O]-A;l?c.setTranslate(c.translate-I):(c.slideTo(j-O,0,!1,!0),r&&(c.touchEventsData.startTranslate=c.touchEventsData.startTranslate-I,c.touchEventsData.currentTranslate=c.touchEventsData.currentTranslate-I))}else{const A=w?E.length/v.grid.rows:E.length;c.slideTo(c.activeIndex-A,0,!1,!0)}}if(c.allowSlidePrev=h,c.allowSlideNext=g,c.controller&&c.controller.control&&!o){const A={slideRealIndex:t,direction:i,setTranslate:r,activeSlideIndex:s,byController:!0};Array.isArray(c.controller.control)?c.controller.control.forEach(X=>{!X.destroyed&&X.params.loop&&X.loopFix({...A,slideTo:X.params.slidesPerView===v.slidesPerView?n:!1})}):c.controller.control instanceof c.constructor&&c.controller.control.params.loop&&c.controller.control.loopFix({...A,slideTo:c.controller.control.params.slidesPerView===v.slidesPerView?n:!1})}c.emit("loopFix")}function zv(){const e=this,{params:t,slidesEl:n}=e;if(!t.loop||!n||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const i=[];e.slides.forEach(r=>{const s=typeof r.swiperSlideIndex>"u"?r.getAttribute("data-swiper-slide-index")*1:r.swiperSlideIndex;i[s]=r}),e.slides.forEach(r=>{r.removeAttribute("data-swiper-slide-index")}),i.forEach(r=>{n.append(r)}),e.recalcSlides(),e.slideTo(e.realIndex,0)}var _v={loopCreate:Ov,loopFix:Av,loopDestroy:zv};function Hv(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const n=t.params.touchEventsTarget==="container"?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),n.style.cursor="move",n.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})}function Uv(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e[e.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}var Bv={setGrabCursor:Hv,unsetGrabCursor:Uv};function Vv(e,t){t===void 0&&(t=this);function n(i){if(!i||i===nn()||i===be())return null;i.assignedSlot&&(i=i.assignedSlot);const r=i.closest(e);return!r&&!i.getRootNode?null:r||n(i.getRootNode().host)}return n(t)}function vu(e,t,n){const i=be(),{params:r}=e,s=r.edgeSwipeDetection,a=r.edgeSwipeThreshold;return s&&(n<=a||n>=i.innerWidth-a)?s==="prevent"?(t.preventDefault(),!0):!1:!0}function Gv(e){const t=this,n=nn();let i=e;i.originalEvent&&(i=i.originalEvent);const r=t.touchEventsData;if(i.type==="pointerdown"){if(r.pointerId!==null&&r.pointerId!==i.pointerId)return;r.pointerId=i.pointerId}else i.type==="touchstart"&&i.targetTouches.length===1&&(r.touchId=i.targetTouches[0].identifier);if(i.type==="touchstart"){vu(t,i,i.targetTouches[0].pageX);return}const{params:s,touches:a,enabled:o}=t;if(!o||!s.simulateTouch&&i.pointerType==="mouse"||t.animating&&s.preventInteractionOnTransition)return;!t.animating&&s.cssMode&&s.loop&&t.loopFix();let l=i.target;if(s.touchEventsTarget==="wrapper"&&!Xg(l,t.wrapperEl)||"which"in i&&i.which===3||"button"in i&&i.button>0||r.isTouched&&r.isMoved)return;const c=!!s.noSwipingClass&&s.noSwipingClass!=="",d=i.composedPath?i.composedPath():i.path;c&&i.target&&i.target.shadowRoot&&d&&(l=d[0]);const h=s.noSwipingSelector?s.noSwipingSelector:`.${s.noSwipingClass}`,g=!!(i.target&&i.target.shadowRoot);if(s.noSwiping&&(g?Vv(h,l):l.closest(h))){t.allowClick=!0;return}if(s.swipeHandler&&!l.closest(s.swipeHandler))return;a.currentX=i.pageX,a.currentY=i.pageY;const y=a.currentX,v=a.currentY;if(!vu(t,i,y))return;Object.assign(r,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),a.startX=y,a.startY=v,r.touchStartTime=Br(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,s.threshold>0&&(r.allowThresholdMove=!1);let x=!0;l.matches(r.focusableElements)&&(x=!1,l.nodeName==="SELECT"&&(r.isTouched=!1)),n.activeElement&&n.activeElement.matches(r.focusableElements)&&n.activeElement!==l&&(i.pointerType==="mouse"||i.pointerType!=="mouse"&&!l.matches(r.focusableElements))&&n.activeElement.blur();const S=x&&t.allowTouchMove&&s.touchStartPreventDefault;(s.touchStartForcePreventDefault||S)&&!l.isContentEditable&&i.preventDefault(),s.freeMode&&s.freeMode.enabled&&t.freeMode&&t.animating&&!s.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",i)}function $v(e){const t=nn(),n=this,i=n.touchEventsData,{params:r,touches:s,rtlTranslate:a,enabled:o}=n;if(!o||!r.simulateTouch&&e.pointerType==="mouse")return;let l=e;if(l.originalEvent&&(l=l.originalEvent),l.type==="pointermove"&&(i.touchId!==null||l.pointerId!==i.pointerId))return;let c;if(l.type==="touchmove"){if(c=[...l.changedTouches].find(b=>b.identifier===i.touchId),!c||c.identifier!==i.touchId)return}else c=l;if(!i.isTouched){i.startMoving&&i.isScrolling&&n.emit("touchMoveOpposite",l);return}const d=c.pageX,h=c.pageY;if(l.preventedByNestedSwiper){s.startX=d,s.startY=h;return}if(!n.allowTouchMove){l.target.matches(i.focusableElements)||(n.allowClick=!1),i.isTouched&&(Object.assign(s,{startX:d,startY:h,currentX:d,currentY:h}),i.touchStartTime=Br());return}if(r.touchReleaseOnEdges&&!r.loop)if(n.isVertical()){if(h<s.startY&&n.translate<=n.maxTranslate()||h>s.startY&&n.translate>=n.minTranslate()){i.isTouched=!1,i.isMoved=!1;return}}else{if(a&&(d>s.startX&&-n.translate<=n.maxTranslate()||d<s.startX&&-n.translate>=n.minTranslate()))return;if(!a&&(d<s.startX&&n.translate<=n.maxTranslate()||d>s.startX&&n.translate>=n.minTranslate()))return}if(t.activeElement&&t.activeElement.matches(i.focusableElements)&&t.activeElement!==l.target&&l.pointerType!=="mouse"&&t.activeElement.blur(),t.activeElement&&l.target===t.activeElement&&l.target.matches(i.focusableElements)){i.isMoved=!0,n.allowClick=!1;return}i.allowTouchCallbacks&&n.emit("touchMove",l),s.previousX=s.currentX,s.previousY=s.currentY,s.currentX=d,s.currentY=h;const g=s.currentX-s.startX,y=s.currentY-s.startY;if(n.params.threshold&&Math.sqrt(g**2+y**2)<n.params.threshold)return;if(typeof i.isScrolling>"u"){let b;n.isHorizontal()&&s.currentY===s.startY||n.isVertical()&&s.currentX===s.startX?i.isScrolling=!1:g*g+y*y>=25&&(b=Math.atan2(Math.abs(y),Math.abs(g))*180/Math.PI,i.isScrolling=n.isHorizontal()?b>r.touchAngle:90-b>r.touchAngle)}if(i.isScrolling&&n.emit("touchMoveOpposite",l),typeof i.startMoving>"u"&&(s.currentX!==s.startX||s.currentY!==s.startY)&&(i.startMoving=!0),i.isScrolling||l.type==="touchmove"&&i.preventTouchMoveFromPointerMove){i.isTouched=!1;return}if(!i.startMoving)return;n.allowClick=!1,!r.cssMode&&l.cancelable&&l.preventDefault(),r.touchMoveStopPropagation&&!r.nested&&l.stopPropagation();let v=n.isHorizontal()?g:y,x=n.isHorizontal()?s.currentX-s.previousX:s.currentY-s.previousY;r.oneWayMovement&&(v=Math.abs(v)*(a?1:-1),x=Math.abs(x)*(a?1:-1)),s.diff=v,v*=r.touchRatio,a&&(v=-v,x=-x);const S=n.touchesDirection;n.swipeDirection=v>0?"prev":"next",n.touchesDirection=x>0?"prev":"next";const p=n.params.loop&&!r.cssMode,f=n.touchesDirection==="next"&&n.allowSlideNext||n.touchesDirection==="prev"&&n.allowSlidePrev;if(!i.isMoved){if(p&&f&&n.loopFix({direction:n.swipeDirection}),i.startTranslate=n.getTranslate(),n.setTransition(0),n.animating){const b=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});n.wrapperEl.dispatchEvent(b)}i.allowMomentumBounce=!1,r.grabCursor&&(n.allowSlideNext===!0||n.allowSlidePrev===!0)&&n.setGrabCursor(!0),n.emit("sliderFirstMove",l)}if(new Date().getTime(),r._loopSwapReset!==!1&&i.isMoved&&i.allowThresholdMove&&S!==n.touchesDirection&&p&&f&&Math.abs(v)>=1){Object.assign(s,{startX:d,startY:h,currentX:d,currentY:h,startTranslate:i.currentTranslate}),i.loopSwapReset=!0,i.startTranslate=i.currentTranslate;return}n.emit("sliderMove",l),i.isMoved=!0,i.currentTranslate=v+i.startTranslate;let m=!0,w=r.resistanceRatio;if(r.touchReleaseOnEdges&&(w=0),v>0?(p&&f&&i.allowThresholdMove&&i.currentTranslate>(r.centeredSlides?n.minTranslate()-n.slidesSizesGrid[n.activeIndex+1]-(r.slidesPerView!=="auto"&&n.slides.length-r.slidesPerView>=2?n.slidesSizesGrid[n.activeIndex+1]+n.params.spaceBetween:0)-n.params.spaceBetween:n.minTranslate())&&n.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),i.currentTranslate>n.minTranslate()&&(m=!1,r.resistance&&(i.currentTranslate=n.minTranslate()-1+(-n.minTranslate()+i.startTranslate+v)**w))):v<0&&(p&&f&&i.allowThresholdMove&&i.currentTranslate<(r.centeredSlides?n.maxTranslate()+n.slidesSizesGrid[n.slidesSizesGrid.length-1]+n.params.spaceBetween+(r.slidesPerView!=="auto"&&n.slides.length-r.slidesPerView>=2?n.slidesSizesGrid[n.slidesSizesGrid.length-1]+n.params.spaceBetween:0):n.maxTranslate())&&n.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:n.slides.length-(r.slidesPerView==="auto"?n.slidesPerViewDynamic():Math.ceil(parseFloat(r.slidesPerView,10)))}),i.currentTranslate<n.maxTranslate()&&(m=!1,r.resistance&&(i.currentTranslate=n.maxTranslate()+1-(n.maxTranslate()-i.startTranslate-v)**w))),m&&(l.preventedByNestedSwiper=!0),!n.allowSlideNext&&n.swipeDirection==="next"&&i.currentTranslate<i.startTranslate&&(i.currentTranslate=i.startTranslate),!n.allowSlidePrev&&n.swipeDirection==="prev"&&i.currentTranslate>i.startTranslate&&(i.currentTranslate=i.startTranslate),!n.allowSlidePrev&&!n.allowSlideNext&&(i.currentTranslate=i.startTranslate),r.threshold>0)if(Math.abs(v)>r.threshold||i.allowThresholdMove){if(!i.allowThresholdMove){i.allowThresholdMove=!0,s.startX=s.currentX,s.startY=s.currentY,i.currentTranslate=i.startTranslate,s.diff=n.isHorizontal()?s.currentX-s.startX:s.currentY-s.startY;return}}else{i.currentTranslate=i.startTranslate;return}!r.followFinger||r.cssMode||((r.freeMode&&r.freeMode.enabled&&n.freeMode||r.watchSlidesProgress)&&(n.updateActiveIndex(),n.updateSlidesClasses()),r.freeMode&&r.freeMode.enabled&&n.freeMode&&n.freeMode.onTouchMove(),n.updateProgress(i.currentTranslate),n.setTranslate(i.currentTranslate))}function Wv(e){const t=this,n=t.touchEventsData;let i=e;i.originalEvent&&(i=i.originalEvent);let r;if(i.type==="touchend"||i.type==="touchcancel"){if(r=[...i.changedTouches].find(b=>b.identifier===n.touchId),!r||r.identifier!==n.touchId)return}else{if(n.touchId!==null||i.pointerId!==n.pointerId)return;r=i}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(i.type)&&!(["pointercancel","contextmenu"].includes(i.type)&&(t.browser.isSafari||t.browser.isWebView)))return;n.pointerId=null,n.touchId=null;const{params:a,touches:o,rtlTranslate:l,slidesGrid:c,enabled:d}=t;if(!d||!a.simulateTouch&&i.pointerType==="mouse")return;if(n.allowTouchCallbacks&&t.emit("touchEnd",i),n.allowTouchCallbacks=!1,!n.isTouched){n.isMoved&&a.grabCursor&&t.setGrabCursor(!1),n.isMoved=!1,n.startMoving=!1;return}a.grabCursor&&n.isMoved&&n.isTouched&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!1);const h=Br(),g=h-n.touchStartTime;if(t.allowClick){const b=i.path||i.composedPath&&i.composedPath();t.updateClickedSlide(b&&b[0]||i.target,b),t.emit("tap click",i),g<300&&h-n.lastClickTime<300&&t.emit("doubleTap doubleClick",i)}if(n.lastClickTime=Br(),Ga(()=>{t.destroyed||(t.allowClick=!0)}),!n.isTouched||!n.isMoved||!t.swipeDirection||o.diff===0&&!n.loopSwapReset||n.currentTranslate===n.startTranslate&&!n.loopSwapReset){n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;return}n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;let y;if(a.followFinger?y=l?t.translate:-t.translate:y=-n.currentTranslate,a.cssMode)return;if(a.freeMode&&a.freeMode.enabled){t.freeMode.onTouchEnd({currentPos:y});return}const v=y>=-t.maxTranslate()&&!t.params.loop;let x=0,S=t.slidesSizesGrid[0];for(let b=0;b<c.length;b+=b<a.slidesPerGroupSkip?1:a.slidesPerGroup){const E=b<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;typeof c[b+E]<"u"?(v||y>=c[b]&&y<c[b+E])&&(x=b,S=c[b+E]-c[b]):(v||y>=c[b])&&(x=b,S=c[c.length-1]-c[c.length-2])}let p=null,f=null;a.rewind&&(t.isBeginning?f=a.virtual&&a.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(p=0));const m=(y-c[x])/S,w=x<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;if(g>a.longSwipesMs){if(!a.longSwipes){t.slideTo(t.activeIndex);return}t.swipeDirection==="next"&&(m>=a.longSwipesRatio?t.slideTo(a.rewind&&t.isEnd?p:x+w):t.slideTo(x)),t.swipeDirection==="prev"&&(m>1-a.longSwipesRatio?t.slideTo(x+w):f!==null&&m<0&&Math.abs(m)>a.longSwipesRatio?t.slideTo(f):t.slideTo(x))}else{if(!a.shortSwipes){t.slideTo(t.activeIndex);return}t.navigation&&(i.target===t.navigation.nextEl||i.target===t.navigation.prevEl)?i.target===t.navigation.nextEl?t.slideTo(x+w):t.slideTo(x):(t.swipeDirection==="next"&&t.slideTo(p!==null?p:x+w),t.swipeDirection==="prev"&&t.slideTo(f!==null?f:x))}}function yu(){const e=this,{params:t,el:n}=e;if(n&&n.offsetWidth===0)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:i,allowSlidePrev:r,snapGrid:s}=e,a=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const o=a&&t.loop;(t.slidesPerView==="auto"||t.slidesPerView>1)&&e.isEnd&&!e.isBeginning&&!e.params.centeredSlides&&!o?e.slideTo(e.slides.length-1,0,!1,!0):e.params.loop&&!a?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=r,e.allowSlideNext=i,e.params.watchOverflow&&s!==e.snapGrid&&e.checkOverflow()}function Yv(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function Kv(){const e=this,{wrapperEl:t,rtlTranslate:n,enabled:i}=e;if(!i)return;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,e.translate===0&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();let r;const s=e.maxTranslate()-e.minTranslate();s===0?r=0:r=(e.translate-e.minTranslate())/s,r!==e.progress&&e.updateProgress(n?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function qv(e){const t=this;gr(t,e.target),!(t.params.cssMode||t.params.slidesPerView!=="auto"&&!t.params.autoHeight)&&t.update()}function Zv(){const e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}const rf=(e,t)=>{const n=nn(),{params:i,el:r,wrapperEl:s,device:a}=e,o=!!i.nested,l=t==="on"?"addEventListener":"removeEventListener",c=t;!r||typeof r=="string"||(n[l]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:o}),r[l]("touchstart",e.onTouchStart,{passive:!1}),r[l]("pointerdown",e.onTouchStart,{passive:!1}),n[l]("touchmove",e.onTouchMove,{passive:!1,capture:o}),n[l]("pointermove",e.onTouchMove,{passive:!1,capture:o}),n[l]("touchend",e.onTouchEnd,{passive:!0}),n[l]("pointerup",e.onTouchEnd,{passive:!0}),n[l]("pointercancel",e.onTouchEnd,{passive:!0}),n[l]("touchcancel",e.onTouchEnd,{passive:!0}),n[l]("pointerout",e.onTouchEnd,{passive:!0}),n[l]("pointerleave",e.onTouchEnd,{passive:!0}),n[l]("contextmenu",e.onTouchEnd,{passive:!0}),(i.preventClicks||i.preventClicksPropagation)&&r[l]("click",e.onClick,!0),i.cssMode&&s[l]("scroll",e.onScroll),i.updateOnWindowResize?e[c](a.ios||a.android?"resize orientationchange observerUpdate":"resize observerUpdate",yu,!0):e[c]("observerUpdate",yu,!0),r[l]("load",e.onLoad,{capture:!0}))};function Qv(){const e=this,{params:t}=e;e.onTouchStart=Gv.bind(e),e.onTouchMove=$v.bind(e),e.onTouchEnd=Wv.bind(e),e.onDocumentTouchStart=Zv.bind(e),t.cssMode&&(e.onScroll=Kv.bind(e)),e.onClick=Yv.bind(e),e.onLoad=qv.bind(e),rf(e,"on")}function Xv(){rf(this,"off")}var Jv={attachEvents:Qv,detachEvents:Xv};const xu=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;function e0(){const e=this,{realIndex:t,initialized:n,params:i,el:r}=e,s=i.breakpoints;if(!s||s&&Object.keys(s).length===0)return;const a=nn(),o=i.breakpointsBase==="window"||!i.breakpointsBase?i.breakpointsBase:"container",l=["window","container"].includes(i.breakpointsBase)||!i.breakpointsBase?e.el:a.querySelector(i.breakpointsBase),c=e.getBreakpoint(s,o,l);if(!c||e.currentBreakpoint===c)return;const h=(c in s?s[c]:void 0)||e.originalParams,g=xu(e,i),y=xu(e,h),v=e.params.grabCursor,x=h.grabCursor,S=i.enabled;g&&!y?(r.classList.remove(`${i.containerModifierClass}grid`,`${i.containerModifierClass}grid-column`),e.emitContainerClasses()):!g&&y&&(r.classList.add(`${i.containerModifierClass}grid`),(h.grid.fill&&h.grid.fill==="column"||!h.grid.fill&&i.grid.fill==="column")&&r.classList.add(`${i.containerModifierClass}grid-column`),e.emitContainerClasses()),v&&!x?e.unsetGrabCursor():!v&&x&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(E=>{if(typeof h[E]>"u")return;const N=i[E]&&i[E].enabled,T=h[E]&&h[E].enabled;N&&!T&&e[E].disable(),!N&&T&&e[E].enable()});const p=h.direction&&h.direction!==i.direction,f=i.loop&&(h.slidesPerView!==i.slidesPerView||p),m=i.loop;p&&n&&e.changeDirection(),Fe(e.params,h);const w=e.params.enabled,b=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),S&&!w?e.disable():!S&&w&&e.enable(),e.currentBreakpoint=c,e.emit("_beforeBreakpoint",h),n&&(f?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!m&&b?(e.loopCreate(t),e.updateSlides()):m&&!b&&e.loopDestroy()),e.emit("breakpoint",h)}function t0(e,t,n){if(t===void 0&&(t="window"),!e||t==="container"&&!n)return;let i=!1;const r=be(),s=t==="window"?r.innerHeight:n.clientHeight,a=Object.keys(e).map(o=>{if(typeof o=="string"&&o.indexOf("@")===0){const l=parseFloat(o.substr(1));return{value:s*l,point:o}}return{value:o,point:o}});a.sort((o,l)=>parseInt(o.value,10)-parseInt(l.value,10));for(let o=0;o<a.length;o+=1){const{point:l,value:c}=a[o];t==="window"?r.matchMedia(`(min-width: ${c}px)`).matches&&(i=l):c<=n.clientWidth&&(i=l)}return i||"max"}var n0={setBreakpoint:e0,getBreakpoint:t0};function i0(e,t){const n=[];return e.forEach(i=>{typeof i=="object"?Object.keys(i).forEach(r=>{i[r]&&n.push(t+r)}):typeof i=="string"&&n.push(t+i)}),n}function r0(){const e=this,{classNames:t,params:n,rtl:i,el:r,device:s}=e,a=i0(["initialized",n.direction,{"free-mode":e.params.freeMode&&n.freeMode.enabled},{autoheight:n.autoHeight},{rtl:i},{grid:n.grid&&n.grid.rows>1},{"grid-column":n.grid&&n.grid.rows>1&&n.grid.fill==="column"},{android:s.android},{ios:s.ios},{"css-mode":n.cssMode},{centered:n.cssMode&&n.centeredSlides},{"watch-progress":n.watchSlidesProgress}],n.containerModifierClass);t.push(...a),r.classList.add(...t),e.emitContainerClasses()}function s0(){const e=this,{el:t,classNames:n}=e;!t||typeof t=="string"||(t.classList.remove(...n),e.emitContainerClasses())}var a0={addClasses:r0,removeClasses:s0};function o0(){const e=this,{isLocked:t,params:n}=e,{slidesOffsetBefore:i}=n;if(i){const r=e.slides.length-1,s=e.slidesGrid[r]+e.slidesSizesGrid[r]+i*2;e.isLocked=e.size>s}else e.isLocked=e.snapGrid.length===1;n.allowSlideNext===!0&&(e.allowSlideNext=!e.isLocked),n.allowSlidePrev===!0&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}var l0={checkOverflow:o0},Ya={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function u0(e,t){return function(i){i===void 0&&(i={});const r=Object.keys(i)[0],s=i[r];if(typeof s!="object"||s===null){Fe(t,i);return}if(e[r]===!0&&(e[r]={enabled:!0}),r==="navigation"&&e[r]&&e[r].enabled&&!e[r].prevEl&&!e[r].nextEl&&(e[r].auto=!0),["pagination","scrollbar"].indexOf(r)>=0&&e[r]&&e[r].enabled&&!e[r].el&&(e[r].auto=!0),!(r in e&&"enabled"in s)){Fe(t,i);return}typeof e[r]=="object"&&!("enabled"in e[r])&&(e[r].enabled=!0),e[r]||(e[r]={enabled:!1}),Fe(t,i)}}const Ws={eventsEmitter:av,update:vv,translate:Sv,transition:Tv,slide:Mv,loop:_v,grabCursor:Bv,events:Jv,breakpoints:n0,checkOverflow:l0,classes:a0},Ys={};let Qo=class it{constructor(){let t,n;for(var i=arguments.length,r=new Array(i),s=0;s<i;s++)r[s]=arguments[s];r.length===1&&r[0].constructor&&Object.prototype.toString.call(r[0]).slice(8,-1)==="Object"?n=r[0]:[t,n]=r,n||(n={}),n=Fe({},n),t&&!n.el&&(n.el=t);const a=nn();if(n.el&&typeof n.el=="string"&&a.querySelectorAll(n.el).length>1){const d=[];return a.querySelectorAll(n.el).forEach(h=>{const g=Fe({},n,{el:h});d.push(new it(g))}),d}const o=this;o.__swiper__=!0,o.support=Jd(),o.device=ef({userAgent:n.userAgent}),o.browser=tf(),o.eventsListeners={},o.eventsAnyListeners=[],o.modules=[...o.__modules__],n.modules&&Array.isArray(n.modules)&&o.modules.push(...n.modules);const l={};o.modules.forEach(d=>{d({params:n,swiper:o,extendParams:u0(n,l),on:o.on.bind(o),once:o.once.bind(o),off:o.off.bind(o),emit:o.emit.bind(o)})});const c=Fe({},Ya,l);return o.params=Fe({},c,Ys,n),o.originalParams=Fe({},o.params),o.passedParams=Fe({},n),o.params&&o.params.on&&Object.keys(o.params.on).forEach(d=>{o.on(d,o.params.on[d])}),o.params&&o.params.onAny&&o.onAny(o.params.onAny),Object.assign(o,{enabled:o.params.enabled,el:t,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return o.params.direction==="horizontal"},isVertical(){return o.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:o.params.allowSlideNext,allowSlidePrev:o.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:o.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:o.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),o.emit("_swiper"),o.params.init&&o.init(),o}getDirectionLabel(t){return this.isHorizontal()?t:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[t]}getSlideIndex(t){const{slidesEl:n,params:i}=this,r=Qe(n,`.${i.slideClass}, swiper-slide`),s=$r(r[0]);return $r(t)-s}getSlideIndexByData(t){return this.getSlideIndex(this.slides.find(n=>n.getAttribute("data-swiper-slide-index")*1===t))}recalcSlides(){const t=this,{slidesEl:n,params:i}=t;t.slides=Qe(n,`.${i.slideClass}, swiper-slide`)}enable(){const t=this;t.enabled||(t.enabled=!0,t.params.grabCursor&&t.setGrabCursor(),t.emit("enable"))}disable(){const t=this;t.enabled&&(t.enabled=!1,t.params.grabCursor&&t.unsetGrabCursor(),t.emit("disable"))}setProgress(t,n){const i=this;t=Math.min(Math.max(t,0),1);const r=i.minTranslate(),a=(i.maxTranslate()-r)*t+r;i.translateTo(a,typeof n>"u"?0:n),i.updateActiveIndex(),i.updateSlidesClasses()}emitContainerClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const n=t.el.className.split(" ").filter(i=>i.indexOf("swiper")===0||i.indexOf(t.params.containerModifierClass)===0);t.emit("_containerClasses",n.join(" "))}getSlideClasses(t){const n=this;return n.destroyed?"":t.className.split(" ").filter(i=>i.indexOf("swiper-slide")===0||i.indexOf(n.params.slideClass)===0).join(" ")}emitSlidesClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const n=[];t.slides.forEach(i=>{const r=t.getSlideClasses(i);n.push({slideEl:i,classNames:r}),t.emit("_slideClass",i,r)}),t.emit("_slideClasses",n)}slidesPerViewDynamic(t,n){t===void 0&&(t="current"),n===void 0&&(n=!1);const i=this,{params:r,slides:s,slidesGrid:a,slidesSizesGrid:o,size:l,activeIndex:c}=i;let d=1;if(typeof r.slidesPerView=="number")return r.slidesPerView;if(r.centeredSlides){let h=s[c]?Math.ceil(s[c].swiperSlideSize):0,g;for(let y=c+1;y<s.length;y+=1)s[y]&&!g&&(h+=Math.ceil(s[y].swiperSlideSize),d+=1,h>l&&(g=!0));for(let y=c-1;y>=0;y-=1)s[y]&&!g&&(h+=s[y].swiperSlideSize,d+=1,h>l&&(g=!0))}else if(t==="current")for(let h=c+1;h<s.length;h+=1)(n?a[h]+o[h]-a[c]<l:a[h]-a[c]<l)&&(d+=1);else for(let h=c-1;h>=0;h-=1)a[c]-a[h]<l&&(d+=1);return d}update(){const t=this;if(!t||t.destroyed)return;const{snapGrid:n,params:i}=t;i.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(a=>{a.complete&&gr(t,a)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses();function r(){const a=t.rtlTranslate?t.translate*-1:t.translate,o=Math.min(Math.max(a,t.maxTranslate()),t.minTranslate());t.setTranslate(o),t.updateActiveIndex(),t.updateSlidesClasses()}let s;if(i.freeMode&&i.freeMode.enabled&&!i.cssMode)r(),i.autoHeight&&t.updateAutoHeight();else{if((i.slidesPerView==="auto"||i.slidesPerView>1)&&t.isEnd&&!i.centeredSlides){const a=t.virtual&&i.virtual.enabled?t.virtual.slides:t.slides;s=t.slideTo(a.length-1,0,!1,!0)}else s=t.slideTo(t.activeIndex,0,!1,!0);s||r()}i.watchOverflow&&n!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(t,n){n===void 0&&(n=!0);const i=this,r=i.params.direction;return t||(t=r==="horizontal"?"vertical":"horizontal"),t===r||t!=="horizontal"&&t!=="vertical"||(i.el.classList.remove(`${i.params.containerModifierClass}${r}`),i.el.classList.add(`${i.params.containerModifierClass}${t}`),i.emitContainerClasses(),i.params.direction=t,i.slides.forEach(s=>{t==="vertical"?s.style.width="":s.style.height=""}),i.emit("changeDirection"),n&&i.update()),i}changeLanguageDirection(t){const n=this;n.rtl&&t==="rtl"||!n.rtl&&t==="ltr"||(n.rtl=t==="rtl",n.rtlTranslate=n.params.direction==="horizontal"&&n.rtl,n.rtl?(n.el.classList.add(`${n.params.containerModifierClass}rtl`),n.el.dir="rtl"):(n.el.classList.remove(`${n.params.containerModifierClass}rtl`),n.el.dir="ltr"),n.update())}mount(t){const n=this;if(n.mounted)return!0;let i=t||n.params.el;if(typeof i=="string"&&(i=document.querySelector(i)),!i)return!1;i.swiper=n,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===n.params.swiperElementNodeName.toUpperCase()&&(n.isElement=!0);const r=()=>`.${(n.params.wrapperClass||"").trim().split(" ").join(".")}`;let a=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(r()):Qe(i,r())[0];return!a&&n.params.createElements&&(a=Gr("div",n.params.wrapperClass),i.append(a),Qe(i,`.${n.params.slideClass}`).forEach(o=>{a.append(o)})),Object.assign(n,{el:i,wrapperEl:a,slidesEl:n.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:a,hostEl:n.isElement?i.parentNode.host:i,mounted:!0,rtl:i.dir.toLowerCase()==="rtl"||Et(i,"direction")==="rtl",rtlTranslate:n.params.direction==="horizontal"&&(i.dir.toLowerCase()==="rtl"||Et(i,"direction")==="rtl"),wrongRTL:Et(a,"display")==="-webkit-box"}),!0}init(t){const n=this;if(n.initialized||n.mount(t)===!1)return n;n.emit("beforeInit"),n.params.breakpoints&&n.setBreakpoint(),n.addClasses(),n.updateSize(),n.updateSlides(),n.params.watchOverflow&&n.checkOverflow(),n.params.grabCursor&&n.enabled&&n.setGrabCursor(),n.params.loop&&n.virtual&&n.params.virtual.enabled?n.slideTo(n.params.initialSlide+n.virtual.slidesBefore,0,n.params.runCallbacksOnInit,!1,!0):n.slideTo(n.params.initialSlide,0,n.params.runCallbacksOnInit,!1,!0),n.params.loop&&n.loopCreate(void 0,!0),n.attachEvents();const r=[...n.el.querySelectorAll('[loading="lazy"]')];return n.isElement&&r.push(...n.hostEl.querySelectorAll('[loading="lazy"]')),r.forEach(s=>{s.complete?gr(n,s):s.addEventListener("load",a=>{gr(n,a.target)})}),Wa(n),n.initialized=!0,Wa(n),n.emit("init"),n.emit("afterInit"),n}destroy(t,n){t===void 0&&(t=!0),n===void 0&&(n=!0);const i=this,{params:r,el:s,wrapperEl:a,slides:o}=i;return typeof i.params>"u"||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),r.loop&&i.loopDestroy(),n&&(i.removeClasses(),s&&typeof s!="string"&&s.removeAttribute("style"),a&&a.removeAttribute("style"),o&&o.length&&o.forEach(l=>{l.classList.remove(r.slideVisibleClass,r.slideFullyVisibleClass,r.slideActiveClass,r.slideNextClass,r.slidePrevClass),l.removeAttribute("style"),l.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(l=>{i.off(l)}),t!==!1&&(i.el&&typeof i.el!="string"&&(i.el.swiper=null),Yg(i)),i.destroyed=!0),null}static extendDefaults(t){Fe(Ys,t)}static get extendedDefaults(){return Ys}static get defaults(){return Ya}static installModule(t){it.prototype.__modules__||(it.prototype.__modules__=[]);const n=it.prototype.__modules__;typeof t=="function"&&n.indexOf(t)<0&&n.push(t)}static use(t){return Array.isArray(t)?(t.forEach(n=>it.installModule(n)),it):(it.installModule(t),it)}};Object.keys(Ws).forEach(e=>{Object.keys(Ws[e]).forEach(t=>{Qo.prototype[t]=Ws[e][t]})});Qo.use([rv,sv]);const sf=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function Jt(e){return typeof e=="object"&&e!==null&&e.constructor&&Object.prototype.toString.call(e).slice(8,-1)==="Object"&&!e.__swiper__}function En(e,t){const n=["__proto__","constructor","prototype"];Object.keys(t).filter(i=>n.indexOf(i)<0).forEach(i=>{typeof e[i]>"u"?e[i]=t[i]:Jt(t[i])&&Jt(e[i])&&Object.keys(t[i]).length>0?t[i].__swiper__?e[i]=t[i]:En(e[i],t[i]):e[i]=t[i]})}function af(e){return e===void 0&&(e={}),e.navigation&&typeof e.navigation.nextEl>"u"&&typeof e.navigation.prevEl>"u"}function of(e){return e===void 0&&(e={}),e.pagination&&typeof e.pagination.el>"u"}function lf(e){return e===void 0&&(e={}),e.scrollbar&&typeof e.scrollbar.el>"u"}function uf(e){e===void 0&&(e="");const t=e.split(" ").map(i=>i.trim()).filter(i=>!!i),n=[];return t.forEach(i=>{n.indexOf(i)<0&&n.push(i)}),n.join(" ")}function c0(e){return e===void 0&&(e=""),e?e.includes("swiper-wrapper")?e:`swiper-wrapper ${e}`:"swiper-wrapper"}function d0(e){let{swiper:t,slides:n,passedParams:i,changedParams:r,nextEl:s,prevEl:a,scrollbarEl:o,paginationEl:l}=e;const c=r.filter(j=>j!=="children"&&j!=="direction"&&j!=="wrapperClass"),{params:d,pagination:h,navigation:g,scrollbar:y,virtual:v,thumbs:x}=t;let S,p,f,m,w,b,E,N;r.includes("thumbs")&&i.thumbs&&i.thumbs.swiper&&!i.thumbs.swiper.destroyed&&d.thumbs&&(!d.thumbs.swiper||d.thumbs.swiper.destroyed)&&(S=!0),r.includes("controller")&&i.controller&&i.controller.control&&d.controller&&!d.controller.control&&(p=!0),r.includes("pagination")&&i.pagination&&(i.pagination.el||l)&&(d.pagination||d.pagination===!1)&&h&&!h.el&&(f=!0),r.includes("scrollbar")&&i.scrollbar&&(i.scrollbar.el||o)&&(d.scrollbar||d.scrollbar===!1)&&y&&!y.el&&(m=!0),r.includes("navigation")&&i.navigation&&(i.navigation.prevEl||a)&&(i.navigation.nextEl||s)&&(d.navigation||d.navigation===!1)&&g&&!g.prevEl&&!g.nextEl&&(w=!0);const T=j=>{t[j]&&(t[j].destroy(),j==="navigation"?(t.isElement&&(t[j].prevEl.remove(),t[j].nextEl.remove()),d[j].prevEl=void 0,d[j].nextEl=void 0,t[j].prevEl=void 0,t[j].nextEl=void 0):(t.isElement&&t[j].el.remove(),d[j].el=void 0,t[j].el=void 0))};r.includes("loop")&&t.isElement&&(d.loop&&!i.loop?b=!0:!d.loop&&i.loop?E=!0:N=!0),c.forEach(j=>{if(Jt(d[j])&&Jt(i[j]))Object.assign(d[j],i[j]),(j==="navigation"||j==="pagination"||j==="scrollbar")&&"enabled"in i[j]&&!i[j].enabled&&T(j);else{const C=i[j];(C===!0||C===!1)&&(j==="navigation"||j==="pagination"||j==="scrollbar")?C===!1&&T(j):d[j]=i[j]}}),c.includes("controller")&&!p&&t.controller&&t.controller.control&&d.controller&&d.controller.control&&(t.controller.control=d.controller.control),r.includes("children")&&n&&v&&d.virtual.enabled?(v.slides=n,v.update(!0)):r.includes("virtual")&&v&&d.virtual.enabled&&(n&&(v.slides=n),v.update(!0)),r.includes("children")&&n&&d.loop&&(N=!0),S&&x.init()&&x.update(!0),p&&(t.controller.control=d.controller.control),f&&(t.isElement&&(!l||typeof l=="string")&&(l=document.createElement("div"),l.classList.add("swiper-pagination"),l.part.add("pagination"),t.el.appendChild(l)),l&&(d.pagination.el=l),h.init(),h.render(),h.update()),m&&(t.isElement&&(!o||typeof o=="string")&&(o=document.createElement("div"),o.classList.add("swiper-scrollbar"),o.part.add("scrollbar"),t.el.appendChild(o)),o&&(d.scrollbar.el=o),y.init(),y.updateSize(),y.setTranslate()),w&&(t.isElement&&((!s||typeof s=="string")&&(s=document.createElement("div"),s.classList.add("swiper-button-next"),Wr(s,t.hostEl.constructor.nextButtonSvg),s.part.add("button-next"),t.el.appendChild(s)),(!a||typeof a=="string")&&(a=document.createElement("div"),a.classList.add("swiper-button-prev"),Wr(a,t.hostEl.constructor.prevButtonSvg),a.part.add("button-prev"),t.el.appendChild(a))),s&&(d.navigation.nextEl=s),a&&(d.navigation.prevEl=a),g.init(),g.update()),r.includes("allowSlideNext")&&(t.allowSlideNext=i.allowSlideNext),r.includes("allowSlidePrev")&&(t.allowSlidePrev=i.allowSlidePrev),r.includes("direction")&&t.changeDirection(i.direction,!1),(b||N)&&t.loopDestroy(),(E||N)&&t.loopCreate(),t.update()}function f0(e,t){e===void 0&&(e={}),t===void 0&&(t=!0);const n={on:{}},i={},r={};En(n,Ya),n._emitClasses=!0,n.init=!1;const s={},a=sf.map(l=>l.replace(/_/,"")),o=Object.assign({},e);return Object.keys(o).forEach(l=>{typeof e[l]>"u"||(a.indexOf(l)>=0?Jt(e[l])?(n[l]={},r[l]={},En(n[l],e[l]),En(r[l],e[l])):(n[l]=e[l],r[l]=e[l]):l.search(/on[A-Z]/)===0&&typeof e[l]=="function"?t?i[`${l[2].toLowerCase()}${l.substr(3)}`]=e[l]:n.on[`${l[2].toLowerCase()}${l.substr(3)}`]=e[l]:s[l]=e[l])}),["navigation","pagination","scrollbar"].forEach(l=>{n[l]===!0&&(n[l]={}),n[l]===!1&&delete n[l]}),{params:n,passedParams:r,rest:s,events:i}}function h0(e,t){let{el:n,nextEl:i,prevEl:r,paginationEl:s,scrollbarEl:a,swiper:o}=e;af(t)&&i&&r&&(o.params.navigation.nextEl=i,o.originalParams.navigation.nextEl=i,o.params.navigation.prevEl=r,o.originalParams.navigation.prevEl=r),of(t)&&s&&(o.params.pagination.el=s,o.originalParams.pagination.el=s),lf(t)&&a&&(o.params.scrollbar.el=a,o.originalParams.scrollbar.el=a),o.init(n)}function p0(e,t,n,i,r){const s=[];if(!t)return s;const a=l=>{s.indexOf(l)<0&&s.push(l)};if(n&&i){const l=i.map(r),c=n.map(r);l.join("")!==c.join("")&&a("children"),i.length!==n.length&&a("children")}return sf.filter(l=>l[0]==="_").map(l=>l.replace(/_/,"")).forEach(l=>{if(l in e&&l in t)if(Jt(e[l])&&Jt(t[l])){const c=Object.keys(e[l]),d=Object.keys(t[l]);c.length!==d.length?a(l):(c.forEach(h=>{e[l][h]!==t[l][h]&&a(l)}),d.forEach(h=>{e[l][h]!==t[l][h]&&a(l)}))}else e[l]!==t[l]&&a(l)}),s}const m0=e=>{!e||e.destroyed||!e.params.virtual||e.params.virtual&&!e.params.virtual.enabled||(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.emit("_virtualUpdated"),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())};function Yr(){return Yr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},Yr.apply(this,arguments)}function cf(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}function df(e){const t=[];return ie.Children.toArray(e).forEach(n=>{cf(n)?t.push(n):n.props&&n.props.children&&df(n.props.children).forEach(i=>t.push(i))}),t}function g0(e){const t=[],n={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return ie.Children.toArray(e).forEach(i=>{if(cf(i))t.push(i);else if(i.props&&i.props.slot&&n[i.props.slot])n[i.props.slot].push(i);else if(i.props&&i.props.children){const r=df(i.props.children);r.length>0?r.forEach(s=>t.push(s)):n["container-end"].push(i)}else n["container-end"].push(i)}),{slides:t,slots:n}}function v0(e,t,n){if(!n)return null;const i=d=>{let h=d;return d<0?h=t.length+d:h>=t.length&&(h=h-t.length),h},r=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:`${n.offset}px`}:{top:`${n.offset}px`},{from:s,to:a}=n,o=e.params.loop?-t.length:0,l=e.params.loop?t.length*2:t.length,c=[];for(let d=o;d<l;d+=1)d>=s&&d<=a&&c.push(t[i(d)]);return c.map((d,h)=>ie.cloneElement(d,{swiper:e,style:r,key:d.props.virtualIndex||d.key||`slide-${h}`}))}function ai(e,t){return typeof window>"u"?k.useEffect(e,t):k.useLayoutEffect(e,t)}const wu=k.createContext(null),y0=k.createContext(null),ff=k.forwardRef(function(e,t){let{className:n,tag:i="div",wrapperTag:r="div",children:s,onSwiper:a,...o}=e===void 0?{}:e,l=!1;const[c,d]=k.useState("swiper"),[h,g]=k.useState(null),[y,v]=k.useState(!1),x=k.useRef(!1),S=k.useRef(null),p=k.useRef(null),f=k.useRef(null),m=k.useRef(null),w=k.useRef(null),b=k.useRef(null),E=k.useRef(null),N=k.useRef(null),{params:T,passedParams:j,rest:C,events:R}=f0(o),{slides:L,slots:O}=g0(s),H=()=>{v(!y)};Object.assign(T.on,{_containerClasses(P,M){d(M)}});const U=()=>{Object.assign(T.on,R),l=!0;const P={...T};if(delete P.wrapperClass,p.current=new Qo(P),p.current.virtual&&p.current.params.virtual.enabled){p.current.virtual.slides=L;const M={cache:!1,slides:L,renderExternal:g,renderExternalUpdate:!1};En(p.current.params.virtual,M),En(p.current.originalParams.virtual,M)}};S.current||U(),p.current&&p.current.on("_beforeBreakpoint",H);const A=()=>{l||!R||!p.current||Object.keys(R).forEach(P=>{p.current.on(P,R[P])})},X=()=>{!R||!p.current||Object.keys(R).forEach(P=>{p.current.off(P,R[P])})};k.useEffect(()=>()=>{p.current&&p.current.off("_beforeBreakpoint",H)}),k.useEffect(()=>{!x.current&&p.current&&(p.current.emitSlidesClasses(),x.current=!0)}),ai(()=>{if(t&&(t.current=S.current),!!S.current)return p.current.destroyed&&U(),h0({el:S.current,nextEl:w.current,prevEl:b.current,paginationEl:E.current,scrollbarEl:N.current,swiper:p.current},T),a&&!p.current.destroyed&&a(p.current),()=>{p.current&&!p.current.destroyed&&p.current.destroy(!0,!1)}},[]),ai(()=>{A();const P=p0(j,f.current,L,m.current,M=>M.key);return f.current=j,m.current=L,P.length&&p.current&&!p.current.destroyed&&d0({swiper:p.current,slides:L,passedParams:j,changedParams:P,nextEl:w.current,prevEl:b.current,scrollbarEl:N.current,paginationEl:E.current}),()=>{X()}}),ai(()=>{m0(p.current)},[h]);function I(){return T.virtual?v0(p.current,L,h):L.map((P,M)=>ie.cloneElement(P,{swiper:p.current,swiperSlideIndex:M}))}return ie.createElement(i,Yr({ref:S,className:uf(`${c}${n?` ${n}`:""}`)},C),ie.createElement(y0.Provider,{value:p.current},O["container-start"],ie.createElement(r,{className:c0(T.wrapperClass)},O["wrapper-start"],I(),O["wrapper-end"]),af(T)&&ie.createElement(ie.Fragment,null,ie.createElement("div",{ref:b,className:"swiper-button-prev"}),ie.createElement("div",{ref:w,className:"swiper-button-next"})),lf(T)&&ie.createElement("div",{ref:N,className:"swiper-scrollbar"}),of(T)&&ie.createElement("div",{ref:E,className:"swiper-pagination"}),O["container-end"]))});ff.displayName="Swiper";const hf=k.forwardRef(function(e,t){let{tag:n="div",children:i,className:r="",swiper:s,zoom:a,lazy:o,virtualIndex:l,swiperSlideIndex:c,...d}=e===void 0?{}:e;const h=k.useRef(null),[g,y]=k.useState("swiper-slide"),[v,x]=k.useState(!1);function S(w,b,E){b===h.current&&y(E)}ai(()=>{if(typeof c<"u"&&(h.current.swiperSlideIndex=c),t&&(t.current=h.current),!(!h.current||!s)){if(s.destroyed){g!=="swiper-slide"&&y("swiper-slide");return}return s.on("_slideClass",S),()=>{s&&s.off("_slideClass",S)}}}),ai(()=>{s&&h.current&&!s.destroyed&&y(s.getSlideClasses(h.current))},[s]);const p={isActive:g.indexOf("swiper-slide-active")>=0,isVisible:g.indexOf("swiper-slide-visible")>=0,isPrev:g.indexOf("swiper-slide-prev")>=0,isNext:g.indexOf("swiper-slide-next")>=0},f=()=>typeof i=="function"?i(p):i,m=()=>{x(!0)};return ie.createElement(n,Yr({ref:h,className:uf(`${g}${r?` ${r}`:""}`),"data-swiper-slide-index":l,onLoad:m},d),a&&ie.createElement(wu.Provider,{value:p},ie.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":typeof a=="number"?a:void 0},f(),o&&!v&&ie.createElement("div",{className:"swiper-lazy-preloader"}))),!a&&ie.createElement(wu.Provider,{value:p},f(),o&&!v&&ie.createElement("div",{className:"swiper-lazy-preloader"})))});hf.displayName="SwiperSlide";function pf(e,t,n,i){return e.params.createElements&&Object.keys(i).forEach(r=>{if(!n[r]&&n.auto===!0){let s=Qe(e.el,`.${i[r]}`)[0];s||(s=Gr("div",i[r]),s.className=i[r],e.el.append(s)),n[r]=s,t[r]=s}}),n}function x0(e){let{swiper:t,extendParams:n,on:i,emit:r}=e;n({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null};function s(v){let x;return v&&typeof v=="string"&&t.isElement&&(x=t.el.querySelector(v)||t.hostEl.querySelector(v),x)?x:(v&&(typeof v=="string"&&(x=[...document.querySelectorAll(v)]),t.params.uniqueNavElements&&typeof v=="string"&&x&&x.length>1&&t.el.querySelectorAll(v).length===1?x=t.el.querySelector(v):x&&x.length===1&&(x=x[0])),v&&!x?v:x)}function a(v,x){const S=t.params.navigation;v=ae(v),v.forEach(p=>{p&&(p.classList[x?"add":"remove"](...S.disabledClass.split(" ")),p.tagName==="BUTTON"&&(p.disabled=x),t.params.watchOverflow&&t.enabled&&p.classList[t.isLocked?"add":"remove"](S.lockClass))})}function o(){const{nextEl:v,prevEl:x}=t.navigation;if(t.params.loop){a(x,!1),a(v,!1);return}a(x,t.isBeginning&&!t.params.rewind),a(v,t.isEnd&&!t.params.rewind)}function l(v){v.preventDefault(),!(t.isBeginning&&!t.params.loop&&!t.params.rewind)&&(t.slidePrev(),r("navigationPrev"))}function c(v){v.preventDefault(),!(t.isEnd&&!t.params.loop&&!t.params.rewind)&&(t.slideNext(),r("navigationNext"))}function d(){const v=t.params.navigation;if(t.params.navigation=pf(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(v.nextEl||v.prevEl))return;let x=s(v.nextEl),S=s(v.prevEl);Object.assign(t.navigation,{nextEl:x,prevEl:S}),x=ae(x),S=ae(S);const p=(f,m)=>{f&&f.addEventListener("click",m==="next"?c:l),!t.enabled&&f&&f.classList.add(...v.lockClass.split(" "))};x.forEach(f=>p(f,"next")),S.forEach(f=>p(f,"prev"))}function h(){let{nextEl:v,prevEl:x}=t.navigation;v=ae(v),x=ae(x);const S=(p,f)=>{p.removeEventListener("click",f==="next"?c:l),p.classList.remove(...t.params.navigation.disabledClass.split(" "))};v.forEach(p=>S(p,"next")),x.forEach(p=>S(p,"prev"))}i("init",()=>{t.params.navigation.enabled===!1?y():(d(),o())}),i("toEdge fromEdge lock unlock",()=>{o()}),i("destroy",()=>{h()}),i("enable disable",()=>{let{nextEl:v,prevEl:x}=t.navigation;if(v=ae(v),x=ae(x),t.enabled){o();return}[...v,...x].filter(S=>!!S).forEach(S=>S.classList.add(t.params.navigation.lockClass))}),i("click",(v,x)=>{let{nextEl:S,prevEl:p}=t.navigation;S=ae(S),p=ae(p);const f=x.target;let m=p.includes(f)||S.includes(f);if(t.isElement&&!m){const w=x.path||x.composedPath&&x.composedPath();w&&(m=w.find(b=>S.includes(b)||p.includes(b)))}if(t.params.navigation.hideOnClick&&!m){if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===f||t.pagination.el.contains(f)))return;let w;S.length?w=S[0].classList.contains(t.params.navigation.hiddenClass):p.length&&(w=p[0].classList.contains(t.params.navigation.hiddenClass)),r(w===!0?"navigationShow":"navigationHide"),[...S,...p].filter(b=>!!b).forEach(b=>b.classList.toggle(t.params.navigation.hiddenClass))}});const g=()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),d(),o()},y=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),h()};Object.assign(t.navigation,{enable:g,disable:y,update:o,init:d,destroy:h})}function Wn(e){return e===void 0&&(e=""),`.${e.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function w0(e){let{swiper:t,extendParams:n,on:i,emit:r}=e;const s="swiper-pagination";n({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:f=>f,formatFractionTotal:f=>f,bulletClass:`${s}-bullet`,bulletActiveClass:`${s}-bullet-active`,modifierClass:`${s}-`,currentClass:`${s}-current`,totalClass:`${s}-total`,hiddenClass:`${s}-hidden`,progressbarFillClass:`${s}-progressbar-fill`,progressbarOppositeClass:`${s}-progressbar-opposite`,clickableClass:`${s}-clickable`,lockClass:`${s}-lock`,horizontalClass:`${s}-horizontal`,verticalClass:`${s}-vertical`,paginationDisabledClass:`${s}-disabled`}}),t.pagination={el:null,bullets:[]};let a,o=0;function l(){return!t.params.pagination.el||!t.pagination.el||Array.isArray(t.pagination.el)&&t.pagination.el.length===0}function c(f,m){const{bulletActiveClass:w}=t.params.pagination;f&&(f=f[`${m==="prev"?"previous":"next"}ElementSibling`],f&&(f.classList.add(`${w}-${m}`),f=f[`${m==="prev"?"previous":"next"}ElementSibling`],f&&f.classList.add(`${w}-${m}-${m}`)))}function d(f,m,w){if(f=f%w,m=m%w,m===f+1)return"next";if(m===f-1)return"previous"}function h(f){const m=f.target.closest(Wn(t.params.pagination.bulletClass));if(!m)return;f.preventDefault();const w=$r(m)*t.params.slidesPerGroup;if(t.params.loop){if(t.realIndex===w)return;const b=d(t.realIndex,w,t.slides.length);b==="next"?t.slideNext():b==="previous"?t.slidePrev():t.slideToLoop(w)}else t.slideTo(w)}function g(){const f=t.rtl,m=t.params.pagination;if(l())return;let w=t.pagination.el;w=ae(w);let b,E;const N=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.slides.length,T=t.params.loop?Math.ceil(N/t.params.slidesPerGroup):t.snapGrid.length;if(t.params.loop?(E=t.previousRealIndex||0,b=t.params.slidesPerGroup>1?Math.floor(t.realIndex/t.params.slidesPerGroup):t.realIndex):typeof t.snapIndex<"u"?(b=t.snapIndex,E=t.previousSnapIndex):(E=t.previousIndex||0,b=t.activeIndex||0),m.type==="bullets"&&t.pagination.bullets&&t.pagination.bullets.length>0){const j=t.pagination.bullets;let C,R,L;if(m.dynamicBullets&&(a=$a(j[0],t.isHorizontal()?"width":"height"),w.forEach(O=>{O.style[t.isHorizontal()?"width":"height"]=`${a*(m.dynamicMainBullets+4)}px`}),m.dynamicMainBullets>1&&E!==void 0&&(o+=b-(E||0),o>m.dynamicMainBullets-1?o=m.dynamicMainBullets-1:o<0&&(o=0)),C=Math.max(b-o,0),R=C+(Math.min(j.length,m.dynamicMainBullets)-1),L=(R+C)/2),j.forEach(O=>{const H=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(U=>`${m.bulletActiveClass}${U}`)].map(U=>typeof U=="string"&&U.includes(" ")?U.split(" "):U).flat();O.classList.remove(...H)}),w.length>1)j.forEach(O=>{const H=$r(O);H===b?O.classList.add(...m.bulletActiveClass.split(" ")):t.isElement&&O.setAttribute("part","bullet"),m.dynamicBullets&&(H>=C&&H<=R&&O.classList.add(...`${m.bulletActiveClass}-main`.split(" ")),H===C&&c(O,"prev"),H===R&&c(O,"next"))});else{const O=j[b];if(O&&O.classList.add(...m.bulletActiveClass.split(" ")),t.isElement&&j.forEach((H,U)=>{H.setAttribute("part",U===b?"bullet-active":"bullet")}),m.dynamicBullets){const H=j[C],U=j[R];for(let A=C;A<=R;A+=1)j[A]&&j[A].classList.add(...`${m.bulletActiveClass}-main`.split(" "));c(H,"prev"),c(U,"next")}}if(m.dynamicBullets){const O=Math.min(j.length,m.dynamicMainBullets+4),H=(a*O-a)/2-L*a,U=f?"right":"left";j.forEach(A=>{A.style[t.isHorizontal()?U:"top"]=`${H}px`})}}w.forEach((j,C)=>{if(m.type==="fraction"&&(j.querySelectorAll(Wn(m.currentClass)).forEach(R=>{R.textContent=m.formatFractionCurrent(b+1)}),j.querySelectorAll(Wn(m.totalClass)).forEach(R=>{R.textContent=m.formatFractionTotal(T)})),m.type==="progressbar"){let R;m.progressbarOpposite?R=t.isHorizontal()?"vertical":"horizontal":R=t.isHorizontal()?"horizontal":"vertical";const L=(b+1)/T;let O=1,H=1;R==="horizontal"?O=L:H=L,j.querySelectorAll(Wn(m.progressbarFillClass)).forEach(U=>{U.style.transform=`translate3d(0,0,0) scaleX(${O}) scaleY(${H})`,U.style.transitionDuration=`${t.params.speed}ms`})}m.type==="custom"&&m.renderCustom?(Wr(j,m.renderCustom(t,b+1,T)),C===0&&r("paginationRender",j)):(C===0&&r("paginationRender",j),r("paginationUpdate",j)),t.params.watchOverflow&&t.enabled&&j.classList[t.isLocked?"add":"remove"](m.lockClass)})}function y(){const f=t.params.pagination;if(l())return;const m=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.grid&&t.params.grid.rows>1?t.slides.length/Math.ceil(t.params.grid.rows):t.slides.length;let w=t.pagination.el;w=ae(w);let b="";if(f.type==="bullets"){let E=t.params.loop?Math.ceil(m/t.params.slidesPerGroup):t.snapGrid.length;t.params.freeMode&&t.params.freeMode.enabled&&E>m&&(E=m);for(let N=0;N<E;N+=1)f.renderBullet?b+=f.renderBullet.call(t,N,f.bulletClass):b+=`<${f.bulletElement} ${t.isElement?'part="bullet"':""} class="${f.bulletClass}"></${f.bulletElement}>`}f.type==="fraction"&&(f.renderFraction?b=f.renderFraction.call(t,f.currentClass,f.totalClass):b=`<span class="${f.currentClass}"></span> / <span class="${f.totalClass}"></span>`),f.type==="progressbar"&&(f.renderProgressbar?b=f.renderProgressbar.call(t,f.progressbarFillClass):b=`<span class="${f.progressbarFillClass}"></span>`),t.pagination.bullets=[],w.forEach(E=>{f.type!=="custom"&&Wr(E,b||""),f.type==="bullets"&&t.pagination.bullets.push(...E.querySelectorAll(Wn(f.bulletClass)))}),f.type!=="custom"&&r("paginationRender",w[0])}function v(){t.params.pagination=pf(t,t.originalParams.pagination,t.params.pagination,{el:"swiper-pagination"});const f=t.params.pagination;if(!f.el)return;let m;typeof f.el=="string"&&t.isElement&&(m=t.el.querySelector(f.el)),!m&&typeof f.el=="string"&&(m=[...document.querySelectorAll(f.el)]),m||(m=f.el),!(!m||m.length===0)&&(t.params.uniqueNavElements&&typeof f.el=="string"&&Array.isArray(m)&&m.length>1&&(m=[...t.el.querySelectorAll(f.el)],m.length>1&&(m=m.find(w=>Xd(w,".swiper")[0]===t.el))),Array.isArray(m)&&m.length===1&&(m=m[0]),Object.assign(t.pagination,{el:m}),m=ae(m),m.forEach(w=>{f.type==="bullets"&&f.clickable&&w.classList.add(...(f.clickableClass||"").split(" ")),w.classList.add(f.modifierClass+f.type),w.classList.add(t.isHorizontal()?f.horizontalClass:f.verticalClass),f.type==="bullets"&&f.dynamicBullets&&(w.classList.add(`${f.modifierClass}${f.type}-dynamic`),o=0,f.dynamicMainBullets<1&&(f.dynamicMainBullets=1)),f.type==="progressbar"&&f.progressbarOpposite&&w.classList.add(f.progressbarOppositeClass),f.clickable&&w.addEventListener("click",h),t.enabled||w.classList.add(f.lockClass)}))}function x(){const f=t.params.pagination;if(l())return;let m=t.pagination.el;m&&(m=ae(m),m.forEach(w=>{w.classList.remove(f.hiddenClass),w.classList.remove(f.modifierClass+f.type),w.classList.remove(t.isHorizontal()?f.horizontalClass:f.verticalClass),f.clickable&&(w.classList.remove(...(f.clickableClass||"").split(" ")),w.removeEventListener("click",h))})),t.pagination.bullets&&t.pagination.bullets.forEach(w=>w.classList.remove(...f.bulletActiveClass.split(" ")))}i("changeDirection",()=>{if(!t.pagination||!t.pagination.el)return;const f=t.params.pagination;let{el:m}=t.pagination;m=ae(m),m.forEach(w=>{w.classList.remove(f.horizontalClass,f.verticalClass),w.classList.add(t.isHorizontal()?f.horizontalClass:f.verticalClass)})}),i("init",()=>{t.params.pagination.enabled===!1?p():(v(),y(),g())}),i("activeIndexChange",()=>{typeof t.snapIndex>"u"&&g()}),i("snapIndexChange",()=>{g()}),i("snapGridLengthChange",()=>{y(),g()}),i("destroy",()=>{x()}),i("enable disable",()=>{let{el:f}=t.pagination;f&&(f=ae(f),f.forEach(m=>m.classList[t.enabled?"remove":"add"](t.params.pagination.lockClass)))}),i("lock unlock",()=>{g()}),i("click",(f,m)=>{const w=m.target,b=ae(t.pagination.el);if(t.params.pagination.el&&t.params.pagination.hideOnClick&&b&&b.length>0&&!w.classList.contains(t.params.pagination.bulletClass)){if(t.navigation&&(t.navigation.nextEl&&w===t.navigation.nextEl||t.navigation.prevEl&&w===t.navigation.prevEl))return;const E=b[0].classList.contains(t.params.pagination.hiddenClass);r(E===!0?"paginationShow":"paginationHide"),b.forEach(N=>N.classList.toggle(t.params.pagination.hiddenClass))}});const S=()=>{t.el.classList.remove(t.params.pagination.paginationDisabledClass);let{el:f}=t.pagination;f&&(f=ae(f),f.forEach(m=>m.classList.remove(t.params.pagination.paginationDisabledClass))),v(),y(),g()},p=()=>{t.el.classList.add(t.params.pagination.paginationDisabledClass);let{el:f}=t.pagination;f&&(f=ae(f),f.forEach(m=>m.classList.add(t.params.pagination.paginationDisabledClass))),x()};Object.assign(t.pagination,{enable:S,disable:p,render:y,update:g,init:v,destroy:x})}const k0=({language:e,product:t})=>{const n=$o(),i={specifications:{vi:"Thông số kỹ thuật",en:"Specifications"},composition:{vi:"Thành phần",en:"Composition"},color:{vi:"Màu sắc",en:"Color"},storageConditions:{vi:"Điều kiện bảo quản",en:"Storage Conditions"},packing:{vi:"Đóng gói",en:"Packing"},bulk:{vi:"Đóng gói số lượng lớn",en:"Bulk packing"},retail:{vi:"Đóng gói bán lẻ",en:"Retail packing"},shelfLife:{vi:"Thời hạn sử dụng",en:"Shelf life"},harvestSeason:{vi:"Mùa thu hoạch",en:"Harvest season"},nutritionFacts:{vi:"Thông tin dinh dưỡng",en:"Nutrition Facts"},servingSize:{vi:"Khẩu phần",en:"Serving Size"},calories:{vi:"Calo",en:"Calories"},totalFat:{vi:"Tổng chất béo",en:"Total Fat"},saturatedFat:{vi:"Chất béo bão hòa",en:"Saturated Fat"},transFat:{vi:"Chất béo trans",en:"Trans Fat"},cholesterol:{vi:"Cholesterol",en:"Cholesterol"},sodium:{vi:"Natri",en:"Sodium"},totalCarbs:{vi:"Tổng carbohydrate",en:"Total Carbohydrate"},dietaryFiber:{vi:"Chất xơ",en:"Dietary Fiber"},sugars:{vi:"Đường",en:"Sugars"},protein:{vi:"Protein",en:"Protein"},vitaminD:{vi:"Vitamin D",en:"Vitamin D"},calcium:{vi:"Canxi",en:"Calcium"},iron:{vi:"Sắt",en:"Iron"},potassium:{vi:"Kali",en:"Potassium"},buyNow:{vi:"MUA NGAY",en:"BUY NOW"}},[r,s]=k.useState(t.images[0]);return k.useEffect(()=>{s(t.images[0])},[t.images]),u.jsxs("div",{className:"w-full min-h-screen py-12 bg-[url('/images/assets/bg_1.png')] bg-cover bg-center bg-no-repeat",children:[u.jsxs("div",{className:"flex flex-col md:flex-row ml-[7%] mr-[7%] md:items-start",children:[u.jsx("div",{className:"md:w-1/4 flex justify-center",children:u.jsxs("div",{className:"bg-white rounded-[20px] shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 w-full h-[500px] flex flex-col",children:[u.jsx("div",{className:"overflow-hidden flex-shrink-0",children:u.jsx("img",{src:r,alt:t.name[e],className:"w-full h-[300px] object-cover hover:scale-105 transition-transform duration-500"})}),u.jsx("div",{className:"flex space-x-2 overflow-x-auto p-2 justify-center flex-shrink-0 ",children:t.images.map((a,o)=>u.jsx("div",{className:`w-20 h-20 rounded-[10px] cursor-pointer border-2 ${r===a?"border-[rgb(19,104,174)]":"border-gray-200"} hover:border-[rgb(19,104,174)] transition-all duration-300 overflow-hidden shadow-md`,onClick:()=>s(a),children:u.jsx("img",{src:a,alt:`${t.name[e]} ${o+1}`,className:"w-full h-full object-cover rounded-[8px] hover:scale-110 transition-transform duration-300"})},o))}),u.jsxs("div",{className:"flex justify-center space-x-4 bg-white p-4 shadow-inner mt-auto flex-shrink-0",children:[u.jsx("img",{src:"/images/chungchi/HACCP.jpg",alt:"HACCP",className:"h-16 hover:scale-110 transition-transform duration-300"}),u.jsx("img",{src:"/images/chungchi/ISO.jpg",alt:"ISO",className:"h-16 hover:scale-110 transition-transform duration-300"}),u.jsx("img",{src:"/images/chungchi/KOSHER.jpg",alt:"KOSHER",className:"h-16 hover:scale-110 transition-transform duration-300"}),u.jsx("img",{src:"/images/chungchi/USDA.jpg",alt:"USDA",className:"h-16 hover:scale-110 transition-transform duration-300"})]})]})}),u.jsxs("div",{className:"md:w-1/2 md:px-10  text-Justify ",children:[u.jsx("h1",{className:"text-4xl font-bold text-[rgb(19,104,174)] mb-2 text-center ",children:t.name[e]}),u.jsx("p",{className:"text-black mb-3 text-justify font-momo",children:t.description[e]}),u.jsxs("div",{className:"mb-1 text-sm",children:[u.jsxs("div",{className:"flex items-start",children:[u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-[rgb(19,104,174)] mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})}),u.jsxs("h2",{className:"text-x font-semibold mb-3",children:[i.specifications[e],":"]})]}),u.jsxs("ul",{className:"space-y-1",children:[t.specifications.composition&&u.jsxs("li",{className:"flex items-start ml-8",children:[u.jsx("span",{className:"text-[rgb(19,104,174)] mr-2",children:"•"}),u.jsxs("span",{className:"font-medium mr-2",children:[i.composition[e],":"]}),u.jsx("span",{children:t.specifications.composition[e]})]}),t.specifications.color&&u.jsxs("li",{className:"flex items-start  ml-8",children:[u.jsx("span",{className:"text-[rgb(19,104,174)] mr-2",children:"•"}),u.jsxs("span",{className:"font-medium mr-2",children:[i.color[e],":"]}),u.jsx("span",{children:t.specifications.color[e]})]})]})]}),t.storageConditions&&u.jsx("div",{className:"mb-2 text-sm",children:u.jsxs("div",{className:"flex items-start",children:[u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-[rgb(19,104,174)] mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})}),u.jsxs("div",{children:[u.jsxs("span",{className:"font-medium",children:[i.storageConditions[e],":"]})," ",t.storageConditions[e]]})]})}),t.packing&&u.jsx("div",{className:"mb-2 text-sm",children:u.jsxs("div",{className:"flex items-start",children:[u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-7 w-7 text-[rgb(19,104,174)] mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"})}),u.jsxs("div",{children:[u.jsxs("span",{className:"font-medium",children:[i.packing[e],":"]}),u.jsxs("ul",{className:" mt-1",children:[t.packing.bulk&&u.jsxs("li",{children:[u.jsx("span",{className:"text-[rgb(19,104,174)] mr-2",children:"•"}),i.bulk[e],": ",t.packing.bulk[e]]}),t.packing.retail&&u.jsxs("li",{children:[u.jsx("span",{className:"text-[rgb(19,104,174)] mr-2",children:"•"}),i.retail[e],": ",t.packing.retail[e]]})]})]})]})}),t.shelfLife&&u.jsx("div",{className:"mb-2 text-sm",children:u.jsxs("div",{className:"flex items-start",children:[u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-[rgb(19,104,174)] mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),u.jsxs("div",{children:[u.jsxs("span",{className:"font-medium",children:[i.shelfLife[e],":"]})," ",t.shelfLife[e]]})]})}),t.harvestSeason&&u.jsx("div",{className:"mb-2 text-sm",children:u.jsxs("div",{className:"flex items-start",children:[u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-[rgb(19,104,174)] mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),u.jsxs("div",{children:[u.jsxs("span",{className:"font-medium",children:[i.harvestSeason[e],":"]})," ",t.harvestSeason[e]]})]})})]}),t.nutritionFacts&&u.jsx("div",{className:"md:w-1/4 flex justify-center",children:u.jsxs("div",{className:"border-8 border-[rgb(19,104,174)] p-2 w-full max-w-[300px] h-[500px] rounded-lg shadow-md overflow-y-auto",children:[u.jsx("div",{className:"text-sm font-bold border-b-2 border-[rgb(19,104,174)] pb-0.5",children:i.nutritionFacts[e]}),u.jsxs("div",{className:"text-xs border-b-4 border-[rgb(19,104,174)] py-0.5",children:[i.servingSize[e],": ",t.nutritionFacts.servingSize]}),u.jsx("div",{className:"border-b-6 border-[rgb(19,104,174)] py-1",children:u.jsxs("div",{className:"font-bold text-xl",children:[i.calories[e]," ",t.nutritionFacts.calories]})}),u.jsx("div",{className:"border-b-4 border-[rgb(19,104,174)] py-0.5 text-right font-bold text-xs",children:"% Daily Value*"}),u.jsxs("div",{className:"flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 text-xs",children:[u.jsxs("div",{children:[u.jsx("strong",{children:i.totalFat[e]})," ",t.nutritionFacts.totalFat,"g"]}),u.jsxs("div",{className:"font-bold",children:[Math.round(t.nutritionFacts.totalFat/78*100),"%"]})]}),u.jsxs("div",{className:"flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 pl-2 text-xs",children:[u.jsxs("div",{children:[i.saturatedFat[e]," ",t.nutritionFacts.saturatedFat,"g"]}),u.jsxs("div",{className:"font-bold",children:[Math.round(t.nutritionFacts.saturatedFat/20*100),"%"]})]}),u.jsxs("div",{className:"flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 pl-2 text-xs",children:[u.jsxs("div",{children:[i.transFat[e]," ",t.nutritionFacts.transFat,"g"]}),u.jsx("div",{})]}),u.jsxs("div",{className:"flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 text-xs",children:[u.jsxs("div",{children:[u.jsx("strong",{children:i.cholesterol[e]})," ",t.nutritionFacts.cholesterol,"mg"]}),u.jsxs("div",{className:"font-bold",children:[Math.round(t.nutritionFacts.cholesterol/300*100),"%"]})]}),u.jsxs("div",{className:"flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 text-xs",children:[u.jsxs("div",{children:[u.jsx("strong",{children:i.sodium[e]})," ",t.nutritionFacts.sodium,"mg"]}),u.jsxs("div",{className:"font-bold",children:[Math.round(t.nutritionFacts.sodium/2300*100),"%"]})]}),u.jsxs("div",{className:"flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 text-xs",children:[u.jsxs("div",{children:[u.jsx("strong",{children:i.totalCarbs[e]})," ",t.nutritionFacts.totalCarbs,"g"]}),u.jsxs("div",{className:"font-bold",children:[Math.round(t.nutritionFacts.totalCarbs/275*100),"%"]})]}),u.jsxs("div",{className:"flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 pl-2 text-xs",children:[u.jsxs("div",{children:[i.dietaryFiber[e]," ",t.nutritionFacts.dietaryFiber,"g"]}),u.jsxs("div",{className:"font-bold",children:[Math.round(t.nutritionFacts.dietaryFiber/28*100),"%"]})]}),u.jsxs("div",{className:"flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 pl-2 text-xs",children:[u.jsxs("div",{children:[i.sugars[e]," ",t.nutritionFacts.sugars,"g"]}),u.jsx("div",{})]}),u.jsxs("div",{className:"flex justify-between border-b-4 border-[rgb(19,104,174)] py-0.5 text-xs",children:[u.jsxs("div",{children:[u.jsx("strong",{children:i.protein[e]})," ",t.nutritionFacts.protein,"g"]}),u.jsx("div",{})]}),u.jsxs("div",{className:"flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 text-xs",children:[u.jsxs("div",{children:[i.vitaminD[e]," ",t.nutritionFacts.vitaminD,"mcg"]}),u.jsxs("div",{className:"font-bold",children:[Math.round(t.nutritionFacts.vitaminD/20*100),"%"]})]}),u.jsxs("div",{className:"flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 text-xs",children:[u.jsxs("div",{children:[i.calcium[e]," ",t.nutritionFacts.calcium,"mg"]}),u.jsxs("div",{className:"font-bold",children:[Math.round(t.nutritionFacts.calcium/1300*100),"%"]})]}),u.jsxs("div",{className:"flex justify-between border-b-2 border-[rgb(19,104,174)] py-0.5 text-xs",children:[u.jsxs("div",{children:[i.iron[e]," ",t.nutritionFacts.iron,"mg"]}),u.jsxs("div",{className:"font-bold",children:[Math.round(t.nutritionFacts.iron/18*100),"%"]})]}),u.jsxs("div",{className:"flex justify-between border-b-4 border-[rgb(19,104,174)] py-0.5 text-xs",children:[u.jsxs("div",{children:[i.potassium[e]," ",t.nutritionFacts.potassium,"mg"]}),u.jsxs("div",{className:"font-bold",children:[Math.round(t.nutritionFacts.potassium/4700*100),"%"]})]}),u.jsx("div",{className:"text-[10px] mt-1 text-justify",children:"* The % Daily Value (DV) tells you how much a nutrient in a serving of food contributes to a daily diet. 2,000 calories a day is used for general nutrition advice."})]})})]}),u.jsx("div",{className:"flex justify-center px-4",children:u.jsx("button",{className:`bg-[rgb(19,104,174)] text-white font-bold py-3 px-8 mt-4 rounded-md hover:bg-[rgb(15,83,139)] transition duration-300
            w-[80%] max-w-xs sm:w-auto sm:min-w-[200px] whitespace-nowrap`,onClick:()=>n("/contact"),children:i.buyNow[e]})}),u.jsxs("div",{className:"mt-8 mb-8 ml-[10%] mr-[10%]",children:[u.jsx("h2",{className:"text-2xl font-bold text-[rgb(19,104,174)] mb-6 text-center",children:e==="vi"?"Sản phẩm đề xuất":"Recommended Products"}),u.jsxs("div",{className:"relative px-[40px] recommended-products-container",children:[u.jsx("div",{className:"swiper-button-prev custom-swiper-button-prev"}),u.jsx("div",{className:"swiper-button-next custom-swiper-button-next"}),u.jsx(ff,{slidesPerView:1,spaceBetween:15,pagination:{clickable:!0,dynamicBullets:!0},navigation:{nextEl:".custom-swiper-button-next",prevEl:".custom-swiper-button-prev"},modules:[w0,x0],breakpoints:{640:{slidesPerView:2,spaceBetween:15},768:{slidesPerView:3,spaceBetween:15},1024:{slidesPerView:4,spaceBetween:15}},loop:!0,className:"mySwiper",children:vn.filter(a=>a.category.id===t.category.id&&a.id!==t.id).map(a=>{var o;return u.jsx(hf,{children:u.jsxs("div",{className:"bg-white rounded-[20px] shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 h-full",children:[u.jsx("div",{className:"h-48 overflow-hidden",children:u.jsx("img",{src:a.images[0],alt:a.name[e],className:"w-full h-full object-cover hover:scale-105 transition-transform duration-500"})}),u.jsxs("div",{className:"p-4",children:[u.jsx("h3",{className:"text-lg font-semibold text-[rgb(19,104,174)] mb-2 text-center",children:a.name[e]}),u.jsx("p",{className:"text-sm text-gray-600 mb-3 text-center",children:((o=a.specifications.composition)==null?void 0:o[e])||""}),u.jsx(me,{to:`/products/${a.category.id}/${a.id}`,children:u.jsx("button",{className:"bg-[rgb(19,104,174)] text-white text-sm py-2 px-4 rounded-md hover:bg-[rgb(15,83,139)] transition duration-300 w-full",children:e==="vi"?"Xem chi tiết":"View details"})})]})]})},a.id)})})]})]})]})},b0=({language:e})=>{const{categoryId:t,productId:n}=Mm(),i=vn.find(r=>r.id===n&&r.category.id===t);return i?u.jsx(k0,{language:e,product:i}):u.jsxs("div",{className:"container mx-auto px-4 py-16 text-center bg-[url('/images/assets/bg.jpg')]",children:[u.jsx("h1",{className:"text-2xl font-bold mb-4",children:e==="vi"?"Không tìm thấy sản phẩm":"Product not found"}),u.jsx("p",{children:e==="vi"?"Sản phẩm bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.":"The product you are looking for does not exist or has been removed."})]})};class Di{constructor(t=0,n="Network Error"){this.status=t,this.text=n}}const S0=()=>{if(!(typeof localStorage>"u"))return{get:e=>Promise.resolve(localStorage.getItem(e)),set:(e,t)=>Promise.resolve(localStorage.setItem(e,t)),remove:e=>Promise.resolve(localStorage.removeItem(e))}},ce={origin:"https://api.emailjs.com",blockHeadless:!1,storageProvider:S0()},Xo=e=>e?typeof e=="string"?{publicKey:e}:e.toString()==="[object Object]"?e:{}:{},E0=(e,t="https://api.emailjs.com")=>{if(!e)return;const n=Xo(e);ce.publicKey=n.publicKey,ce.blockHeadless=n.blockHeadless,ce.storageProvider=n.storageProvider,ce.blockList=n.blockList,ce.limitRate=n.limitRate,ce.origin=n.origin||t},mf=async(e,t,n={})=>{const i=await fetch(ce.origin+e,{method:"POST",headers:n,body:t}),r=await i.text(),s=new Di(i.status,r);if(i.ok)return s;throw s},gf=(e,t,n)=>{if(!e||typeof e!="string")throw"The public key is required. Visit https://dashboard.emailjs.com/admin/account";if(!t||typeof t!="string")throw"The service ID is required. Visit https://dashboard.emailjs.com/admin";if(!n||typeof n!="string")throw"The template ID is required. Visit https://dashboard.emailjs.com/admin/templates"},C0=e=>{if(e&&e.toString()!=="[object Object]")throw"The template params have to be the object. Visit https://www.emailjs.com/docs/sdk/send/"},vf=e=>e.webdriver||!e.languages||e.languages.length===0,yf=()=>new Di(451,"Unavailable For Headless Browser"),N0=(e,t)=>{if(!Array.isArray(e))throw"The BlockList list has to be an array";if(typeof t!="string")throw"The BlockList watchVariable has to be a string"},T0=e=>{var t;return!((t=e.list)!=null&&t.length)||!e.watchVariable},j0=(e,t)=>e instanceof FormData?e.get(t):e[t],xf=(e,t)=>{if(T0(e))return!1;N0(e.list,e.watchVariable);const n=j0(t,e.watchVariable);return typeof n!="string"?!1:e.list.includes(n)},wf=()=>new Di(403,"Forbidden"),F0=(e,t)=>{if(typeof e!="number"||e<0)throw"The LimitRate throttle has to be a positive number";if(t&&typeof t!="string")throw"The LimitRate ID has to be a non-empty string"},R0=async(e,t,n)=>{const i=Number(await n.get(e)||0);return t-Date.now()+i},kf=async(e,t,n)=>{if(!t.throttle||!n)return!1;F0(t.throttle,t.id);const i=t.id||e;return await R0(i,t.throttle,n)>0?!0:(await n.set(i,Date.now().toString()),!1)},bf=()=>new Di(429,"Too Many Requests"),I0=async(e,t,n,i)=>{const r=Xo(i),s=r.publicKey||ce.publicKey,a=r.blockHeadless||ce.blockHeadless,o=r.storageProvider||ce.storageProvider,l={...ce.blockList,...r.blockList},c={...ce.limitRate,...r.limitRate};return a&&vf(navigator)?Promise.reject(yf()):(gf(s,e,t),C0(n),n&&xf(l,n)?Promise.reject(wf()):await kf(location.pathname,c,o)?Promise.reject(bf()):mf("/api/v1.0/email/send",JSON.stringify({lib_version:"4.4.1",user_id:s,service_id:e,template_id:t,template_params:n}),{"Content-type":"application/json"}))},P0=e=>{if(!e||e.nodeName!=="FORM")throw"The 3rd parameter is expected to be the HTML form element or the style selector of the form"},D0=e=>typeof e=="string"?document.querySelector(e):e,L0=async(e,t,n,i)=>{const r=Xo(i),s=r.publicKey||ce.publicKey,a=r.blockHeadless||ce.blockHeadless,o=ce.storageProvider||r.storageProvider,l={...ce.blockList,...r.blockList},c={...ce.limitRate,...r.limitRate};if(a&&vf(navigator))return Promise.reject(yf());const d=D0(n);gf(s,e,t),P0(d);const h=new FormData(d);return xf(l,h)?Promise.reject(wf()):await kf(location.pathname,c,o)?Promise.reject(bf()):(h.append("lib_version","4.4.1"),h.append("service_id",e),h.append("template_id",t),h.append("user_id",s),mf("/api/v1.0/email/send-form",h))},M0={init:E0,send:I0,sendForm:L0,EmailJSResponseStatus:Di},Ks={serviceId:"service_anbinh",templateId:"template_anbinh",publicKey:"tO5uO9VeQLNxSLKBr"},O0=({language:e})=>{const[t,n]=k.useState({name:"",email:"",phone:"",subject:"",message:""}),[i,r]=k.useState(!1),[s,a]=k.useState(!1),[o,l]=k.useState(""),c=k.useRef(null),d={pageTitle:{vi:"LIÊN HỆ",en:"CONTACT US"},heroTitle:{vi:"LIÊN HỆ VỚI CHÚNG TÔI",en:"GET IN TOUCH"},contactInfo:{title:{vi:"THÔNG TIN LIÊN HỆ",en:"CONTACT INFORMATION"},address:{label:{vi:"Địa chỉ",en:"Address"},value:{vi:"so 58, đường 3, thôn 4, Đức Hạnh, Đức Linh, Bình Thuận, Việt Nam",en:"No.58, 3th Street, Hamlet 4, Duc Hanh, Duc Linh, Binh Thuan, Vietnam"}},phone:{label:{vi:"Điện thoại",en:"Phone"},value:"+(84)(0252) 388-8468"},email:{label:{vi:"Email",en:"Email"},value:"<EMAIL>"},workingHours:{label:{vi:"Giờ làm việc",en:"Working Hours"},value:{vi:"Thứ Hai - chủ nhật: 8:00 - 17:00",en:"Monday - Sunday: 8:00 AM - 5:00 PM"}}},form:{title:{vi:"GỬI TIN NHẮN",en:"SEND A MESSAGE"},name:{label:{vi:"Họ và tên",en:"Full Name"},placeholder:{vi:"Nhập họ và tên của bạn",en:"Enter your full name"}},email:{label:{vi:"Email",en:"Email"},placeholder:{vi:"Nhập địa chỉ email của bạn",en:"Enter your email address"}},phone:{label:{vi:"Số điện thoại",en:"Phone Number"},placeholder:{vi:"Nhập số điện thoại của bạn",en:"Enter your phone number"}},subject:{label:{vi:"Tiêu đề",en:"Subject"},placeholder:{vi:"Nhập tiêu đề tin nhắn",en:"Enter message subject"}},message:{label:{vi:"Nội dung tin nhắn",en:"Message"},placeholder:{vi:"Nhập nội dung tin nhắn của bạn",en:"Enter your message"}},submit:{vi:"Gửi tin nhắn",en:"Send Message"},success:{vi:"Tin nhắn của bạn đã được gửi thành công. Chúng tôi sẽ liên hệ lại với bạn sớm nhất có thể.",en:"Your message has been sent successfully. We will contact you as soon as possible."}},faq:{title:{vi:"CÂU HỎI THƯỜNG GẶP",en:"FREQUENTLY ASKED QUESTIONS"},questions:[{question:{vi:"Làm thế nào để đặt hàng trái cây tươi?",en:"How can I order fresh fruits?"},answer:{vi:"Bạn có thể đặt hàng trực tiếp qua website của chúng tôi, gọi điện thoại đến số hotline hoặc gửi email đến địa chỉ <EMAIL>.",en:"You can place an order directly through our website, call our hotline, or send an <NAME_EMAIL>."}},{question:{vi:"Làm thế nào để trở thành đại lý phân phối của ABF?",en:"How can I become a distributor for ABF?"},answer:{vi:"Để trở thành đại lý phân phối, vui lòng gửi thông tin công ty của bạn đế<NAME_EMAIL>. Đội ngũ kinh doanh của chúng tôi sẽ liên hệ với bạn trong vòng 48 giờ.",en:"To become a distributor, please send your company <NAME_EMAIL>. Our business team will contact you within 48 hours."}},{question:{vi:"Các sản phẩm của ABF có chứng nhận hữu cơ không?",en:"Are ABF products organically certified?"},answer:{vi:"Một số sản phẩm của chúng tôi được chứng nhận hữu cơ. Bạn có thể xem thông tin chi tiết về chứng nhận trên trang sản phẩm hoặc liên hệ với chúng tôi để biết thêm thông tin.",en:"Some of our products are organically certified. You can view detailed certification information on the product page or contact us for more information."}}]}},h=y=>{const{name:v,value:x}=y.target;n(S=>({...S,[v]:x}))},g=y=>{y.preventDefault(),c.current&&(a(!0),l(""),M0.sendForm(Ks.serviceId,Ks.templateId,c.current,Ks.publicKey).then(v=>{console.log("Email sent successfully:",v.text),r(!0),a(!1),n({name:"",email:"",phone:"",subject:"",message:""})}).catch(v=>{console.error("Failed to send email:",v.text),l("Có lỗi xảy ra khi gửi tin nhắn. Vui lòng thử lại sau."),a(!1)}))};return u.jsxs("div",{children:[u.jsxs("section",{className:"relative h-[300px] bg-cover bg-center",style:{backgroundImage:"url(/images/about/lh.avif)"},children:[u.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-60"}),u.jsxs("div",{className:"absolute inset-0 flex flex-col items-center justify-center text-white text-center px-4",children:[u.jsx("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:d.pageTitle[e]}),u.jsx("div",{className:"w-20 h-1 bg-red-600 mb-6"}),u.jsx("p",{className:"text-xl max-w-2xl",children:d.heroTitle[e]})]})]}),u.jsx("section",{className:"py-16 bg-gray-50",children:u.jsx("div",{className:"container mx-auto px-4",children:u.jsxs("div",{className:"flex flex-col lg:flex-row gap-8",children:[u.jsx("div",{className:"lg:w-1/3",children:u.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 h-full",children:[u.jsx("h2",{className:"text-2xl font-bold mb-6 text-red-600",children:d.contactInfo.title[e]}),u.jsxs("div",{className:"space-y-6",children:[u.jsxs("div",{className:"flex items-start",children:[u.jsx("div",{className:"flex-shrink-0 bg-red-100 p-3 rounded-full mr-4",children:u.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})}),u.jsxs("div",{children:[u.jsx("h3",{className:"text-lg font-semibold mb-1",children:d.contactInfo.address.label[e]}),u.jsx("p",{className:"text-gray-600",children:d.contactInfo.address.value[e]})]})]}),u.jsxs("div",{className:"flex items-start",children:[u.jsx("div",{className:"flex-shrink-0 bg-red-100 p-3 rounded-full mr-4",children:u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})})}),u.jsxs("div",{children:[u.jsx("h3",{className:"text-lg font-semibold mb-1",children:d.contactInfo.phone.label[e]}),u.jsx("p",{className:"text-gray-600",children:d.contactInfo.phone.value})]})]}),u.jsxs("div",{className:"flex items-start",children:[u.jsx("div",{className:"flex-shrink-0 bg-red-100 p-3 rounded-full mr-4",children:u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),u.jsxs("div",{children:[u.jsx("h3",{className:"text-lg font-semibold mb-1",children:d.contactInfo.email.label[e]}),u.jsx("p",{className:"text-gray-600",children:d.contactInfo.email.value})]})]}),u.jsxs("div",{className:"flex items-start",children:[u.jsx("div",{className:"flex-shrink-0 bg-red-100 p-3 rounded-full mr-4",children:u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),u.jsxs("div",{children:[u.jsx("h3",{className:"text-lg font-semibold mb-1",children:d.contactInfo.workingHours.label[e]}),u.jsx("p",{className:"text-gray-600",children:d.contactInfo.workingHours.value[e]})]})]})]})]})}),u.jsx("div",{className:"lg:w-2/3",children:u.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8",children:[u.jsx("h2",{className:"text-2xl font-bold mb-6 text-red-600",children:d.form.title[e]}),i?u.jsx("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4",children:u.jsx("p",{children:d.form.success[e]})}):null,o&&u.jsx("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:u.jsx("p",{children:o})}),u.jsxs("form",{ref:c,onSubmit:g,children:[u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[u.jsxs("div",{children:[u.jsx("label",{htmlFor:"name",className:"block text-gray-700 font-medium mb-2",children:d.form.name.label[e]}),u.jsx("input",{type:"text",id:"name",name:"name",value:t.name,onChange:h,placeholder:d.form.name.placeholder[e],className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-600",required:!0})]}),u.jsxs("div",{children:[u.jsx("label",{htmlFor:"email",className:"block text-gray-700 font-medium mb-2",children:d.form.email.label[e]}),u.jsx("input",{type:"email",id:"email",name:"email",value:t.email,onChange:h,placeholder:d.form.email.placeholder[e],className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-600",required:!0})]}),u.jsxs("div",{children:[u.jsx("label",{htmlFor:"phone",className:"block text-gray-700 font-medium mb-2",children:d.form.phone.label[e]}),u.jsx("input",{type:"tel",id:"phone",name:"phone",value:t.phone,onChange:h,placeholder:d.form.phone.placeholder[e],className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-600"})]}),u.jsxs("div",{children:[u.jsx("label",{htmlFor:"subject",className:"block text-gray-700 font-medium mb-2",children:d.form.subject.label[e]}),u.jsx("input",{type:"text",id:"subject",name:"subject",value:t.subject,onChange:h,placeholder:d.form.subject.placeholder[e],className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-600",required:!0})]})]}),u.jsxs("div",{className:"mb-6",children:[u.jsx("label",{htmlFor:"message",className:"block text-gray-700 font-medium mb-2",children:d.form.message.label[e]}),u.jsx("textarea",{id:"message",name:"message",value:t.message,onChange:h,placeholder:d.form.message.placeholder[e],rows:5,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-600",required:!0})]}),u.jsx("button",{type:"submit",className:"bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-md transition duration-300",disabled:s,children:s?e==="vi"?"Đang gửi...":"Sending...":d.form.submit[e]})]})]})})]})})}),u.jsx("section",{className:"py-16 bg-white",children:u.jsxs("div",{className:"container mx-auto px-4",children:[u.jsx("h2",{className:"text-3xl font-bold text-center mb-12 text-red-600",children:e==="vi"?"VỊ TRÍ CÔNG TY":"OUR LOCATION"}),u.jsx("div",{className:"w-full h-[450px] rounded-lg overflow-hidden shadow-lg",children:u.jsx("iframe",{src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3915.9553118127!2d107.49705009999999!3d11.142512499999999!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3174431d346cb6d5%3A0xb0fdeeb1fa1edf24!2sAN%20BINH%20INTERNATIONAL%20FOOD%20COMPANY%20LIMITED!5e0!3m2!1svi!2s!4v1716348000000!5m2!1svi!2s",width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade",title:"AN BINH INTERNATIONAL FOOD COMPANY LIMITED",className:"rounded-lg"})})]})}),u.jsx("section",{className:"py-16 bg-gray-50",children:u.jsxs("div",{className:"container mx-auto px-4",children:[u.jsx("h2",{className:"text-3xl font-bold text-center mb-12 text-red-600",children:d.faq.title[e]}),u.jsx("div",{className:"max-w-3xl mx-auto",children:u.jsx("div",{className:"space-y-4",children:d.faq.questions.map((y,v)=>u.jsxs("details",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[u.jsxs("summary",{className:"px-6 py-4 cursor-pointer font-semibold text-lg flex justify-between items-center",children:[y.question[e],u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),u.jsx("div",{className:"px-6 py-4 border-t border-gray-200",children:u.jsx("p",{className:"text-gray-700",children:y.answer[e]})})]},v))})})]})})]})},qs={email:"<EMAIL>",phone:"+(84)(0252) 388-8468",messengerPageId:"100500896121647"};function A0(){const[e,t]=k.useState("vi"),n=()=>{t(i=>i==="vi"?"en":"vi")};return u.jsxs(wg,{children:[u.jsx(Ag,{}),u.jsxs("div",{className:"flex flex-col min-h-screen",children:[u.jsx(Lg,{language:e,toggleLanguage:n}),u.jsx("main",{className:"flex-grow",children:u.jsxs(Zm,{children:[u.jsx(vt,{path:"/",element:u.jsx(_g,{language:e})}),u.jsx(vt,{path:"/about",element:u.jsx(Hg,{language:e})}),u.jsx(vt,{path:"/about/passion",element:u.jsx(Ug,{language:e})}),u.jsx(vt,{path:"/about/sustainability",element:u.jsx(Bg,{language:e})}),u.jsx(vt,{path:"/products",element:u.jsx(Gg,{language:e})}),u.jsx(vt,{path:"/products/:categoryId/:productId",element:u.jsx(b0,{language:e})}),u.jsx(vt,{path:"/contact",element:u.jsx(O0,{language:e})})]})}),u.jsx(Mg,{language:e}),u.jsx(Og,{email:qs.email,phone:qs.phone,messengerPageId:qs.messengerPageId})]})]})}Dd(document.getElementById("root")).render(u.jsx(k.StrictMode,{children:u.jsx(A0,{})}));
