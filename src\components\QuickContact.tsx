import React, { useState, useEffect } from 'react';
import './QuickContact.css';

interface QuickContactProps {
  email: string;
  phone: string;
  whatsapp: string;
  messengerPageId: string; // Facebook Page ID for Messenger (not used but kept for compatibility)
}

const QuickContact: React.FC<QuickContactProps> = ({ email, phone, whatsapp }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const handleEmailClick = () => {
    window.location.href = `mailto:${email}`;
  };

  const handlePhoneClick = () => {
    window.location.href = `tel:${phone}`;
  };

  const handleWhatsAppClick = () => {
    window.open(`https://wa.me/${whatsapp}`, '_blank');
  };

  // Close the menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (isExpanded && !target.closest('.quick-contact-container')) {
        setIsExpanded(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isExpanded]);

  return (
    <div className="fixed right-5 bottom-5 z-50 quick-contact-container">
      <div className={`flex flex-col-reverse items-center gap-4 mb-4 transition-all duration-300 ${isExpanded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10 pointer-events-none'}`}>
        {/* Email Button */}
        <div className="relative group flex items-center">
          <div className="absolute right-full mr-3 bg-white text-gray-800 px-3 py-1 rounded-lg shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
            {email}
          </div>
          <button
            onClick={handleEmailClick}
            className="w-14 h-14 rounded-full bg-gray-200 flex items-center justify-center shadow-lg hover:bg-gray-300 transition-all duration-200 transform hover:scale-110"
            title={`Email: ${email}`}
          >
            <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center">
              <div className="w-10 h-10 rounded-full bg-gray-500 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
          </button>
        </div>

        {/* WhatsApp Button */}
        <div className="relative group flex items-center">
          <div className="absolute right-full mr-3 bg-white text-gray-800 px-3 py-1 rounded-lg shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
            WhatsApp: 0941 508 468
          </div>
          <button
            onClick={handleWhatsAppClick}
            className="w-14 h-14 rounded-full bg-green-100 flex items-center justify-center shadow-lg hover:bg-green-200 transition-all duration-200 transform hover:scale-110"
            title="WhatsApp: 0941 508 468"
          >
            <div className="w-12 h-12 rounded-full bg-green-200 flex items-center justify-center">
              <div className="w-10 h-10 rounded-full bg-green-500 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                </svg>
              </div>
            </div>
          </button>
        </div>

        {/* Phone Button */}
        <div className="relative group flex items-center">
          <div className="absolute right-full mr-3 bg-white text-gray-800 px-3 py-1 rounded-lg shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
            0941 508 468
          </div>
          <button
            onClick={handlePhoneClick}
            className="w-14 h-14 rounded-full bg-green-200 flex items-center justify-center shadow-lg hover:bg-green-300 transition-all duration-200 transform hover:scale-110"
            title={`Call: ${phone}`}
          >
            <div className="w-12 h-12 rounded-full bg-green-300 flex items-center justify-center">
              <div className="w-10 h-10 rounded-full bg-green-600 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
            </div>
          </button>
        </div>


      </div>

      {/* Toggle Button */}
      <button
        onClick={toggleExpand}
        className={`w-16 h-16 rounded-full bg-[rgb(44,159,253)] flex items-center justify-center shadow-lg hover:bg-[rgb(44,159,253)] transition-all duration-200 transform hover:scale-105 ${!isExpanded ? 'pulse-animation' : ''}`}
        aria-label="Toggle contact options"
      >
        <div className="w-14 h-14 rounded-full bg-[rgb(44,159,253)] flex items-center justify-center border-2 border-white">
          {isExpanded ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          )}
        </div>
      </button>
    </div>
  );
};

export default QuickContact;
